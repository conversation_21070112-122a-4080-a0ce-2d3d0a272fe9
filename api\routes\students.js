/**
 * 学生相关路由
 */

const express = require('express');
const { db, COLLECTIONS } = require('../../cloudbase/database/db');
const { authMiddleware, studentOnlyMiddleware } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取学生个人信息
 * GET /api/students/profile
 */
router.get('/profile', authMiddleware, studentOnlyMiddleware, async (req, res) => {
  try {
    res.json({
      success: true,
      data: req.user
    });
  } catch (error) {
    console.error('获取学生信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学生信息失败'
    });
  }
});

module.exports = router;