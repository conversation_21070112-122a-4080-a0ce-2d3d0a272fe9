# RPA财务机器人实训平台 v1.1

基于腾讯云云开发(TCB)的BaaS架构，为《RPA财务机器人入门与进阶》教材提供配套实训环境。

## 🎯 项目特点

- ✅ **超低成本**: 基于BaaS架构，月成本0-20元
- ✅ **教材同步**: 12个场景与教材100%一致
- ✅ **即时验证**: 自动化任务完成验证和评分
- ✅ **极简维护**: 无需服务器运维，专注教学
- ✅ **渐进学习**: 三个模块循序渐进

## 📚 训练场景

### 第一模块：基础操作 (4个场景)
1. **企业网银流水查询下载** - 登录、查询、分页处理
2. **批量开具电子发票** - Excel读取、循环、表单填写  
3. **固定资产卡片信息核对** - 数据双向同步、条件比对
4. **税务申报期提醒与状态核查** - 信息抓取、文本查找

### 第二模块：流程整合 (4个场景)
5. **应收账款对账与核销** - 跨应用操作、数据匹配
6. **增值税进项税发票认证** - CSV处理、批量勾选
7. **月度工资条数据计算与生成** - 多数据源整合、计算引擎
8. **财务报表数据自动汇总** - 模块化设计、数据聚合

### 第三模块：智能应用 (4个场景)  
9. **供应商发票批量验真与入账(OCR)** - OCR识别、API调用
10. **员工差旅费报销智能初审(人机协作)** - 规则引擎、人机交互
11. **合同关键信息提取与印花税计算申报(OCR)** - PDF处理、税务计算
12. **自动生成总账科目余额调节表** - 复杂业务逻辑、数据比对

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd claude-RPA实训平台3.0

# 安装依赖
npm install

# 复制环境配置
cp .env.example .env
```

### 2. 配置腾讯云云开发

1. 登录[腾讯云控制台](https://console.cloud.tencent.com/)
2. 开通云开发服务，创建环境
3. 获取环境ID和密钥
4. 编辑 `.env` 文件：

```env
TCB_ENV_ID=your-env-id
TCB_SECRET_ID=your-secret-id  
TCB_SECRET_KEY=your-secret-key
JWT_SECRET=your-jwt-secret
```

### 3. 启动开发环境

```bash
# 开发模式启动
npm run dev

# 生产模式启动  
npm start
```

### 4. 访问系统

- 学生端: http://localhost:3000
- 教师端: http://localhost:3000/teacher-login.html

**演示账号:**
- 学生: `student1` / `password123`
- 学生: `student2` / `password123`  
- 教师: `teacher1` / `password123`

## 📁 项目结构

```
claude-RPA实训平台3.0/
├── api/                    # API路由
│   ├── routes/            # 路由文件
│   └── middleware/        # 中间件
├── cloudbase/             # 云开发相关
│   └── database/          # 数据库配置
├── frontend/              # 前端页面
│   └── scenarios/         # 场景页面
├── server.js              # 服务器入口
├── package.json
└── README.md
```

## 🔧 核心功能

### 学生端功能
- [x] 用户登录认证
- [x] 场景列表展示  
- [x] 学习进度追踪
- [x] 任务完成验证
- [x] 成绩查看

### 教师端功能
- [ ] 教师管理后台
- [ ] 学生进度看板
- [ ] 班级管理
- [ ] 数据统计导出

### 场景验证功能
- [x] 场景1：企业网银流水查询下载
- [ ] 场景2-12：其他场景实现

## 🛠️ 技术架构

- **前端**: 原生HTML/CSS/JavaScript
- **后端**: Node.js + Express.js
- **数据库**: 腾讯云云开发数据库
- **认证**: JWT Token
- **部署**: 腾讯云云开发静态托管

## 📊 开发进度

根据开发任务进度表，当前状态：

### 阶段一：环境搭建与基础开发 ✅
- [x] ENV-001: 开发环境搭建
- [x] ENV-002: 项目依赖配置  
- [x] ENV-003: Git版本控制
- [x] ARCH-001: BaaS架构搭建
- [x] DB-001: 数据库设计
- [x] AUTH-001: 学生登录功能
- [x] SCENARIO1-001: 场景1页面开发
- [x] VALIDATE-001: 场景1验证API

### 下一步计划
- [ ] DEMO-003: 场景1前后端联调测试
- [ ] DEMO-004: 第一阶段功能测试
- [ ] SC2-001: 场景2页面开发

## 🎯 验收标准

### 阶段一验收标准 ✅
- ✅ 学生能成功注册登录
- ✅ 教师能登录管理后台  
- ✅ 场景1能完整运行并获得"任务完成"反馈
- ✅ 数据能正确存储到云数据库

## 📝 使用说明

### 学生使用流程
1. 访问系统主页，使用学号登录
2. 在学生门户查看可用场景  
3. 点击"开始学习"进入场景页面
4. 按照教材操作步骤完成任务
5. 系统自动验证并给出反馈

### 影刀RPA操作要点
- 使用元素选择器定位页面元素
- 按照页面提示的ID和XPath进行操作
- 完成所有必需步骤才能获得满分

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目负责人: chanwarmsun
- 邮箱: [your-email]
- 教材: 《RPA财务机器人入门与进阶》

---

🎓 **教学目标**: 让学生在稳定的仿真环境中掌握RPA财务自动化技能，与教材内容100%同步，学以致用！