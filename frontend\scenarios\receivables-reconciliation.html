<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景5：应收账款对账与核销系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #8D99AE;
            font-size: 16px;
        }

        .workflow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .step-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
        }

        .step-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(118, 115, 255, 0.25);
        }

        .step-card.active {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.05);
        }

        .step-card.completed {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.05);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-card.completed .step-number {
            background: #27AE60;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .step-content {
            margin-bottom: 20px;
        }

        .file-upload-area {
            border: 2px dashed #7673FF;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            background: rgba(118, 115, 255, 0.05);
            margin: 15px 0;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #5C7BFF;
            background: rgba(118, 115, 255, 0.1);
        }

        .file-upload-area.dragover {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            color: #7673FF;
            margin-bottom: 15px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: rgba(118, 115, 255, 0.05);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .status-pending {
            background: #FFF3CD;
            color: #856404;
        }

        .status-matched {
            background: #D1ECF1;
            color: #0C5460;
        }

        .status-reconciled {
            background: #D4EDDA;
            color: #155724;
        }

        .status-unmatched {
            background: #F8D7DA;
            color: #721C24;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(118, 115, 255, 0.4);
        }

        .btn-success {
            background: #27AE60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #E74C3C;
            color: white;
        }

        .btn-danger:hover {
            background: #C0392B;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .matching-rules {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .rule-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(118, 115, 255, 0.05);
            border-radius: 8px;
        }

        .rule-checkbox {
            margin-right: 10px;
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #E9ECEF;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #8D99AE;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .success-message {
            background: rgba(39, 174, 96, 0.1);
            border: 2px solid #27AE60;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>应收账款对账与核销系统</h1>
            <p>整合销售系统订单与银行流水，实现智能匹配和自动核销</p>
        </div>

        <div class="progress-indicator">
            <h3>任务进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            <p id="progressText">准备开始 - 请上传销售订单数据</p>
        </div>

        <div class="workflow">
            <!-- 步骤1：上传销售订单数据 -->
            <div class="step-card active" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">上传销售订单</div>
                </div>
                <div class="step-content">
                    <div class="file-upload-area" id="salesUploadArea">
                        <div class="upload-icon">📄</div>
                        <p><strong>点击或拖拽上传销售订单Excel文件</strong></p>
                        <p>支持格式：.xlsx, .xls</p>
                        <input type="file" id="salesFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                    <button class="btn btn-primary" id="processSalesBtn" disabled>处理销售数据</button>
                </div>
            </div>

            <!-- 步骤2：上传银行流水 -->
            <div class="step-card" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">上传银行流水</div>
                </div>
                <div class="step-content">
                    <div class="file-upload-area" id="bankUploadArea">
                        <div class="upload-icon">🏦</div>
                        <p><strong>点击或拖拽上传银行流水Excel文件</strong></p>
                        <p>支持格式：.xlsx, .xls</p>
                        <input type="file" id="bankFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                    <button class="btn btn-primary" id="processBankBtn" disabled>处理银行数据</button>
                </div>
            </div>

            <!-- 步骤3：智能匹配配置 -->
            <div class="step-card" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">智能匹配</div>
                </div>
                <div class="step-content">
                    <div class="matching-rules">
                        <h4>匹配规则设置</h4>
                        <div class="rule-item">
                            <input type="checkbox" class="rule-checkbox" id="exactAmountMatch" checked>
                            <label>精确金额匹配</label>
                        </div>
                        <div class="rule-item">
                            <input type="checkbox" class="rule-checkbox" id="fuzzyNameMatch" checked>
                            <label>模糊名称匹配</label>
                        </div>
                        <div class="rule-item">
                            <input type="checkbox" class="rule-checkbox" id="dateRangeMatch">
                            <label>日期范围匹配（±3天）</label>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="startMatchingBtn" disabled>开始智能匹配</button>
                </div>
            </div>

            <!-- 步骤4：核销处理 -->
            <div class="step-card" id="step4">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <div class="step-title">核销处理</div>
                </div>
                <div class="step-content">
                    <div class="summary-stats" id="matchingStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="totalRecords">0</div>
                            <div class="stat-label">总记录数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="matchedRecords">0</div>
                            <div class="stat-label">已匹配</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="unmatchedRecords">0</div>
                            <div class="stat-label">未匹配</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="reconciledRecords">0</div>
                            <div class="stat-label">已核销</div>
                        </div>
                    </div>
                    <button class="btn btn-success" id="reconcileBtn" disabled>执行核销</button>
                    <button class="btn btn-primary" id="exportResultsBtn" disabled>导出结果</button>
                </div>
            </div>
        </div>

        <!-- 数据显示区域 -->
        <div id="dataDisplayArea" style="display: none;">
            <div class="step-card">
                <h3>匹配结果</h3>
                <div class="loading" id="matchingLoading">
                    <div class="spinner"></div>
                    <p>正在进行智能匹配...</p>
                </div>
                <table class="data-table" id="matchingTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>客户名称</th>
                            <th>订单金额</th>
                            <th>银行流水</th>
                            <th>到账金额</th>
                            <th>匹配状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="matchingTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 成功提示 -->
        <div class="success-message" id="successMessage">
            <h3>🎉 任务完成！</h3>
            <p>您已成功完成应收账款对账与核销操作，系统已记录您的学习进度。</p>
        </div>
    </div>

    <script>
        // 全局变量
        let salesData = [];
        let bankData = [];
        let matchingResults = [];
        let currentStep = 1;
        let completedSteps = [];

        // 模拟销售订单数据
        const mockSalesData = [
            { orderNo: 'SO2024001', customerName: '北京科技有限公司', amount: 50000, orderDate: '2024-01-15', status: '未收款' },
            { orderNo: 'SO2024002', customerName: '上海贸易公司', amount: 32000, orderDate: '2024-01-18', status: '未收款' },
            { orderNo: 'SO2024003', customerName: '深圳制造企业', amount: 28500, orderDate: '2024-01-20', status: '未收款' },
            { orderNo: 'SO2024004', customerName: '广州投资集团', amount: 75000, orderDate: '2024-01-22', status: '未收款' },
            { orderNo: 'SO2024005', customerName: '杭州网络科技', amount: 18600, orderDate: '2024-01-25', status: '未收款' }
        ];

        // 模拟银行流水数据
        const mockBankData = [
            { transactionId: 'BT001', payerName: '北京科技有限公司', amount: 50000, transactionDate: '2024-01-16', description: '货款' },
            { transactionId: 'BT002', payerName: '上海贸易公司', amount: 32000, transactionDate: '2024-01-19', description: '采购款' },
            { transactionId: 'BT003', payerName: '深圳制造', amount: 28500, transactionDate: '2024-01-21', description: '设备款' },
            { transactionId: 'BT004', payerName: '其他公司', amount: 15000, transactionDate: '2024-01-23', description: '服务费' },
            { transactionId: 'BT005', payerName: '杭州网络科技有限公司', amount: 18600, transactionDate: '2024-01-26', description: '软件费' }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateProgress();
        });

        function initializeEventListeners() {
            // 文件上传事件
            setupFileUpload('salesUploadArea', 'salesFileInput', handleSalesFileUpload);
            setupFileUpload('bankUploadArea', 'bankFileInput', handleBankFileUpload);

            // 按钮事件
            document.getElementById('processSalesBtn').addEventListener('click', processSalesData);
            document.getElementById('processBankBtn').addEventListener('click', processBankData);
            document.getElementById('startMatchingBtn').addEventListener('click', startMatching);
            document.getElementById('reconcileBtn').addEventListener('click', executeReconciliation);
            document.getElementById('exportResultsBtn').addEventListener('click', exportResults);
        }

        function setupFileUpload(uploadAreaId, fileInputId, handler) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);

            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handler);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handler({ target: { files: files } });
                }
            });
        }

        function handleSalesFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileName = file.name;
                document.querySelector('#step1 .file-upload-area p').innerHTML = 
                    `<strong>已选择文件：${fileName}</strong><br>准备处理销售数据`;
                document.getElementById('processSalesBtn').disabled = false;
            }
        }

        function handleBankFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileName = file.name;
                document.querySelector('#step2 .file-upload-area p').innerHTML = 
                    `<strong>已选择文件：${fileName}</strong><br>准备处理银行数据`;
                document.getElementById('processBankBtn').disabled = false;
            }
        }

        function processSalesData() {
            showLoading('正在处理销售订单数据...');
            
            setTimeout(() => {
                salesData = [...mockSalesData];
                completedSteps.push('upload_sales');
                completeStep(1);
                activateStep(2);
                updateProgress();
                hideLoading();
                
                console.log('销售数据处理完成:', salesData);
            }, 1500);
        }

        function processBankData() {
            showLoading('正在处理银行流水数据...');
            
            setTimeout(() => {
                bankData = [...mockBankData];
                completedSteps.push('upload_bank');
                completeStep(2);
                activateStep(3);
                updateProgress();
                hideLoading();
                
                console.log('银行数据处理完成:', bankData);
            }, 1500);
        }

        function startMatching() {
            showLoading('正在执行智能匹配算法...');
            document.getElementById('matchingLoading').style.display = 'block';
            document.getElementById('dataDisplayArea').style.display = 'block';
            
            setTimeout(() => {
                performMatching();
                displayMatchingResults();
                completedSteps.push('matching');
                completeStep(3);
                activateStep(4);
                updateProgress();
                document.getElementById('matchingLoading').style.display = 'none';
                hideLoading();
            }, 2500);
        }

        function performMatching() {
            matchingResults = [];
            const exactAmountMatch = document.getElementById('exactAmountMatch').checked;
            const fuzzyNameMatch = document.getElementById('fuzzyNameMatch').checked;
            const dateRangeMatch = document.getElementById('dateRangeMatch').checked;

            salesData.forEach(order => {
                let bestMatch = null;
                let matchScore = 0;
                let matchReason = '';

                bankData.forEach(transaction => {
                    let score = 0;
                    let reasons = [];

                    // 精确金额匹配
                    if (exactAmountMatch && order.amount === transaction.amount) {
                        score += 50;
                        reasons.push('金额精确匹配');
                    }

                    // 模糊名称匹配
                    if (fuzzyNameMatch) {
                        const similarity = calculateNameSimilarity(order.customerName, transaction.payerName);
                        if (similarity > 0.6) {
                            score += similarity * 30;
                            reasons.push(`名称相似度${(similarity * 100).toFixed(0)}%`);
                        }
                    }

                    // 日期范围匹配
                    if (dateRangeMatch) {
                        const dateDiff = Math.abs(new Date(order.orderDate) - new Date(transaction.transactionDate));
                        const daysDiff = dateDiff / (1000 * 60 * 60 * 24);
                        if (daysDiff <= 3) {
                            score += 20;
                            reasons.push(`日期差${daysDiff.toFixed(1)}天`);
                        }
                    }

                    if (score > matchScore) {
                        matchScore = score;
                        bestMatch = transaction;
                        matchReason = reasons.join(', ');
                    }
                });

                const status = matchScore > 60 ? 'matched' : 'unmatched';
                matchingResults.push({
                    order: order,
                    transaction: bestMatch,
                    score: matchScore,
                    status: status,
                    reason: matchReason
                });
            });

            updateMatchingStats();
        }

        function calculateNameSimilarity(name1, name2) {
            // 简单的字符串相似度算法
            const shorter = name1.length < name2.length ? name1 : name2;
            const longer = name1.length < name2.length ? name2 : name1;
            
            if (longer.length === 0) return 1.0;
            
            const editDistance = levenshteinDistance(shorter, longer);
            return (longer.length - editDistance) / longer.length;
        }

        function levenshteinDistance(str1, str2) {
            const matrix = [];
            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }
            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }
            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1,
                            matrix[i][j - 1] + 1,
                            matrix[i - 1][j] + 1
                        );
                    }
                }
            }
            return matrix[str2.length][str1.length];
        }

        function displayMatchingResults() {
            const tableBody = document.getElementById('matchingTableBody');
            tableBody.innerHTML = '';

            matchingResults.forEach((result, index) => {
                const row = document.createElement('tr');
                const statusClass = result.status === 'matched' ? 'status-matched' : 'status-unmatched';
                const statusText = result.status === 'matched' ? '已匹配' : '未匹配';
                
                row.innerHTML = `
                    <td>${result.order.orderNo}</td>
                    <td>${result.order.customerName}</td>
                    <td>¥${result.order.amount.toLocaleString()}</td>
                    <td>${result.transaction ? result.transaction.transactionId : '-'}</td>
                    <td>${result.transaction ? `¥${result.transaction.amount.toLocaleString()}` : '-'}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>
                        ${result.status === 'matched' ? 
                            `<button class="btn btn-success" onclick="reconcileRecord(${index})">核销</button>` : 
                            `<button class="btn btn-danger">手动处理</button>`
                        }
                    </td>
                `;
                tableBody.appendChild(row);
            });

            document.getElementById('matchingTable').style.display = 'table';
        }

        function updateMatchingStats() {
            const total = matchingResults.length;
            const matched = matchingResults.filter(r => r.status === 'matched').length;
            const unmatched = total - matched;
            const reconciled = matchingResults.filter(r => r.status === 'reconciled').length;

            document.getElementById('totalRecords').textContent = total;
            document.getElementById('matchedRecords').textContent = matched;
            document.getElementById('unmatchedRecords').textContent = unmatched;
            document.getElementById('reconciledRecords').textContent = reconciled;
            document.getElementById('matchingStats').style.display = 'grid';
        }

        function reconcileRecord(index) {
            if (matchingResults[index].status === 'matched') {
                matchingResults[index].status = 'reconciled';
                displayMatchingResults();
                updateMatchingStats();
            }
        }

        function executeReconciliation() {
            showLoading('正在执行批量核销...');
            
            setTimeout(() => {
                // 将所有已匹配的记录标记为已核销
                matchingResults.forEach(result => {
                    if (result.status === 'matched') {
                        result.status = 'reconciled';
                    }
                });
                
                displayMatchingResults();
                updateMatchingStats();
                completedSteps.push('reconciliation');
                completeStep(4);
                updateProgress();
                hideLoading();
                
                // 提交验证
                submitValidation();
            }, 2000);
        }

        function exportResults() {
            // 模拟导出功能
            const exportData = matchingResults.map(result => ({
                订单号: result.order.orderNo,
                客户名称: result.order.customerName,
                订单金额: result.order.amount,
                银行流水号: result.transaction ? result.transaction.transactionId : '',
                到账金额: result.transaction ? result.transaction.amount : '',
                匹配状态: result.status === 'reconciled' ? '已核销' : result.status === 'matched' ? '已匹配' : '未匹配',
                匹配原因: result.reason
            }));
            
            console.log('导出数据:', exportData);
            alert('核销结果已导出到Excel文件');
        }

        function completeStep(stepNumber) {
            const stepCard = document.getElementById(`step${stepNumber}`);
            stepCard.classList.remove('active');
            stepCard.classList.add('completed');
        }

        function activateStep(stepNumber) {
            if (stepNumber <= 4) {
                const stepCard = document.getElementById(`step${stepNumber}`);
                stepCard.classList.add('active');
                
                // 启用对应的按钮
                switch(stepNumber) {
                    case 3:
                        document.getElementById('startMatchingBtn').disabled = false;
                        break;
                    case 4:
                        document.getElementById('reconcileBtn').disabled = false;
                        document.getElementById('exportResultsBtn').disabled = false;
                        break;
                }
            }
        }

        function updateProgress() {
            const totalSteps = 4;
            const progress = (completedSteps.length / totalSteps) * 100;
            
            document.getElementById('progressBar').querySelector('.progress-fill').style.width = `${progress}%`;
            
            let progressText = '';
            switch(completedSteps.length) {
                case 0:
                    progressText = '准备开始 - 请上传销售订单数据';
                    break;
                case 1:
                    progressText = '销售数据已处理 - 请上传银行流水数据';
                    break;
                case 2:
                    progressText = '数据准备完成 - 请配置匹配规则并开始匹配';
                    break;
                case 3:
                    progressText = '智能匹配完成 - 请执行核销操作';
                    break;
                case 4:
                    progressText = '任务完成 - 所有应收账款已核销完毕';
                    break;
            }
            
            document.getElementById('progressText').textContent = progressText;
        }

        function showLoading(message) {
            // 可以显示全局加载提示
            console.log('Loading:', message);
        }

        function hideLoading() {
            // 隐藏加载提示
        }

        async function submitValidation() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const validationData = {
                    completedSteps: completedSteps,
                    salesDataCount: salesData.length,
                    bankDataCount: bankData.length,
                    matchingResults: {
                        total: matchingResults.length,
                        matched: matchingResults.filter(r => r.status === 'matched' || r.status === 'reconciled').length,
                        reconciled: matchingResults.filter(r => r.status === 'reconciled').length
                    },
                    taskCompleted: completedSteps.length === 4
                };

                const response = await fetch('/api/scenarios/5/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(validationData)
                });

                const result = await response.json();
                
                if (result.success && result.valid) {
                    document.getElementById('successMessage').classList.add('show');
                    console.log('场景5验证成功:', result);
                } else {
                    console.error('场景5验证失败:', result.feedback);
                }
            } catch (error) {
                console.error('提交验证失败:', error);
            }
        }
    </script>
</body>
</html>