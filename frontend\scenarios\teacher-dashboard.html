<!DOCTYPE html>
<html lang="zh-CN">  
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师管理后台 - RPA财务机器人实训平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #F9FAFB;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(92,123,255,0.25);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .logo p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .teacher-badge {
            background: rgba(255,255,255,0.2);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
        }
        
        .stat-card .number {
            font-size: 36px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 10px;
        }
        
        .stat-card .label {
            color: #8D99AE;
            font-size: 14px;
        }
        
        .management-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .section-header {
            padding: 25px 30px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-header h2 {
            color: #2c3e50;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .action-btn:hover {
            border-color: #7673FF;
            color: #7673FF;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(118,115,255,0.15);
        }
        
        .progress-matrix {
            background: white;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        .matrix-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        
        .matrix-table th,
        .matrix-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #e5e7eb;
            font-size: 14px;
        }
        
        .matrix-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
        }
        
        .matrix-table td.student-name {
            text-align: left;
            font-weight: 500;
            background: #f9fafb;
            position: sticky;
            left: 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-in-progress {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-not-started {
            background: #f3f4f6;
            color: #374151;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #8D99AE;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }

        .placeholder-message {
            text-align: center;
            padding: 40px;
            color: #8D99AE;
            background: #f9fafb;
            border-radius: 8px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118,115,255,0.3);
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118,115,255,0.4);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🤖 RPA实训平台 <span class="teacher-badge">教师管理后台</span></h1>
                <p>财务机器人入门与进阶实训平台</p>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">👨‍🏫</div>
                <div>
                    <div id="userName">加载中...</div>
                    <div style="font-size: 12px; opacity: 0.8;" id="userDepartment">教师</div>
                </div>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <h2>欢迎回来，<span id="welcomeName">教师</span>！</h2>
            <p>通过管理后台监控学生学习进度，管理实训场景，提升教学效果</p>
        </div>

        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="number" id="totalStudents">0</div>
                <div class="label">学生总数</div>
            </div>
            <div class="stat-card">
                <div class="number" id="activeStudents">0</div>
                <div class="label">活跃学生</div>
            </div>
            <div class="stat-card">
                <div class="number" id="completedTasks">0</div>
                <div class="label">完成任务数</div>
            </div>
            <div class="stat-card">
                <div class="number" id="averageProgress">0%</div>
                <div class="label">平均完成率</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="management-section">
            <div class="section-header">
                <h2>快速操作</h2>
            </div>
            <div class="section-content">
                <div class="quick-actions">
                    <div class="action-btn" onclick="window.alert('学生批量导入功能开发中...')">
                        <span>👥</span>
                        <span>批量导入学生</span>
                    </div>
                    <div class="action-btn" onclick="window.alert('数据导出功能开发中...')">
                        <span>📊</span>
                        <span>导出学习数据</span>
                    </div>
                    <div class="action-btn" onclick="window.alert('任务重置功能开发中...')">
                        <span>🔄</span>
                        <span>重置学生任务</span>
                    </div>
                    <div class="action-btn" onclick="window.alert('班级管理功能开发中...')">
                        <span>🏫</span>
                        <span>班级管理</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学生进度矩阵 -->
        <div class="management-section">
            <div class="section-header">
                <h2>学生进度看板</h2>
                <button class="btn btn-primary" onclick="location.reload()">刷新数据</button>
            </div>
            <div class="section-content">
                <div class="loading" id="progressLoading">
                    <div class="spinner"></div>
                    正在加载学生进度数据...
                </div>
                
                <div class="progress-matrix" id="progressMatrix" style="display: none;">
                    <table class="matrix-table">
                        <thead>
                            <tr>
                                <th>学生姓名</th>
                                <th>场景1</th>
                                <th>场景2</th>
                                <th>场景3</th>
                                <th>场景4</th>
                                <th>场景5</th>
                                <th>场景6</th>
                                <th>场景7</th>
                                <th>场景8</th>
                                <th>场景9</th>
                                <th>场景10</th>
                                <th>场景11</th>
                                <th>场景12</th>
                                <th>总进度</th>
                            </tr>
                        </thead>
                        <tbody id="progressTableBody">
                            <!-- 动态生成学生进度数据 -->
                        </tbody>
                    </table>
                </div>

                <div class="placeholder-message" id="noDataMessage" style="display: none;">
                    <h3>暂无学生数据</h3>
                    <p>当前还没有学生注册或开始学习，请等待学生注册后再查看进度</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        class TeacherDashboard {
            constructor() {
                this.user = null;
                this.studentsData = [];
                this.progressData = [];
                
                this.init();
            }

            async init() {
                // 检查登录状态
                if (!this.checkAuth()) {
                    return;
                }

                // 绑定事件
                this.bindEvents();
                
                // 加载用户信息
                await this.loadUserInfo();
                
                // 加载统计数据
                await this.loadStatistics();
                
                // 加载学生进度数据
                await this.loadProgressData();
            }

            checkAuth() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                if (!token || !user) {
                    window.location.href = 'teacher-login.html';
                    return false;
                }
                
                try {
                    this.user = JSON.parse(user);
                    if (this.user.role !== 'teacher') {
                        window.location.href = 'teacher-login.html';
                        return false;
                    }
                } catch (error) {
                    window.location.href = 'teacher-login.html';
                    return false;
                }
                
                return true;
            }

            bindEvents() {
                // 退出登录
                document.getElementById('logoutBtn').addEventListener('click', () => {
                    this.logout();
                });
            }

            async loadUserInfo() {
                if (this.user) {
                    document.getElementById('userName').textContent = this.user.name;
                    document.getElementById('welcomeName').textContent = this.user.name;
                    document.getElementById('userDepartment').textContent = this.user.department || '教师';
                    document.getElementById('userAvatar').textContent = this.user.name.charAt(0);
                }
            }

            async loadStatistics() {
                try {
                    // 模拟统计数据，实际应该从API获取
                    const stats = {
                        totalStudents: 2,
                        activeStudents: 1,
                        completedTasks: 1,
                        averageProgress: 8.3
                    };

                    document.getElementById('totalStudents').textContent = stats.totalStudents;
                    document.getElementById('activeStudents').textContent = stats.activeStudents;
                    document.getElementById('completedTasks').textContent = stats.completedTasks;
                    document.getElementById('averageProgress').textContent = stats.averageProgress.toFixed(1) + '%';
                } catch (error) {
                    console.error('加载统计数据失败:', error);
                }
            }

            async loadProgressData() {
                try {
                    const token = localStorage.getItem('token');
                    
                    // 尝试获取学生进度数据，如果失败则显示占位信息
                    setTimeout(() => {
                        document.getElementById('progressLoading').style.display = 'none';
                        
                        // 模拟有少量学生数据
                        const mockStudents = [
                            {
                                name: '李小明',
                                studentId: '2024001',
                                scenarios: [
                                    { id: 1, status: 'completed', score: 95 },
                                    { id: 2, status: 'not_started', score: null },
                                    { id: 3, status: 'not_started', score: null },
                                    { id: 4, status: 'not_started', score: null },
                                    { id: 5, status: 'not_started', score: null },
                                    { id: 6, status: 'not_started', score: null },
                                    { id: 7, status: 'not_started', score: null },
                                    { id: 8, status: 'not_started', score: null },
                                    { id: 9, status: 'not_started', score: null },
                                    { id: 10, status: 'not_started', score: null },
                                    { id: 11, status: 'not_started', score: null },
                                    { id: 12, status: 'not_started', score: null }
                                ]
                            },
                            {
                                name: '王小红',
                                studentId: '2024002',
                                scenarios: [
                                    { id: 1, status: 'not_started', score: null },
                                    { id: 2, status: 'not_started', score: null },
                                    { id: 3, status: 'not_started', score: null },
                                    { id: 4, status: 'not_started', score: null },
                                    { id: 5, status: 'not_started', score: null },
                                    { id: 6, status: 'not_started', score: null },
                                    { id: 7, status: 'not_started', score: null },
                                    { id: 8, status: 'not_started', score: null },
                                    { id: 9, status: 'not_started', score: null },
                                    { id: 10, status: 'not_started', score: null },
                                    { id: 11, status: 'not_started', score: null },
                                    { id: 12, status: 'not_started', score: null }
                                ]
                            }
                        ];

                        if (mockStudents.length > 0) {
                            this.renderProgressMatrix(mockStudents);
                            document.getElementById('progressMatrix').style.display = 'block';
                        } else {
                            document.getElementById('noDataMessage').style.display = 'block';
                        }
                    }, 1500);

                } catch (error) {
                    console.error('加载进度数据失败:', error);
                    document.getElementById('progressLoading').style.display = 'none';
                    document.getElementById('noDataMessage').style.display = 'block';
                }
            }

            renderProgressMatrix(students) {
                const tbody = document.getElementById('progressTableBody');
                tbody.innerHTML = '';

                students.forEach(student => {
                    const row = document.createElement('tr');
                    
                    // 学生姓名列
                    const nameCell = document.createElement('td');
                    nameCell.className = 'student-name';
                    nameCell.innerHTML = `
                        <div style="font-weight: 500;">${student.name}</div>
                        <div style="font-size: 12px; color: #8D99AE;">${student.studentId}</div>
                    `;
                    row.appendChild(nameCell);

                    // 场景进度列
                    student.scenarios.forEach(scenario => {
                        const cell = document.createElement('td');
                        const statusMap = {
                            'completed': { label: '✅', class: 'completed' },
                            'in_progress': { label: '🟡', class: 'in-progress' },
                            'not_started': { label: '⚪', class: 'not-started' }
                        };
                        
                        const statusInfo = statusMap[scenario.status];
                        cell.innerHTML = `
                            <div>${statusInfo.label}</div>
                            ${scenario.score !== null ? `<div style="font-size: 11px; color: #7673FF;">${scenario.score}分</div>` : ''}
                        `;
                        row.appendChild(cell);
                    });

                    // 总进度列
                    const totalCell = document.createElement('td');
                    const completedCount = student.scenarios.filter(s => s.status === 'completed').length;
                    const totalProgress = Math.round((completedCount / 12) * 100);
                    totalCell.innerHTML = `
                        <div style="font-weight: 500; color: #7673FF;">${totalProgress}%</div>
                        <div style="font-size: 11px; color: #8D99AE;">${completedCount}/12</div>
                    `;
                    row.appendChild(totalCell);

                    tbody.appendChild(row);
                });
            }

            logout() {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = 'teacher-login.html';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new TeacherDashboard();
        });
    </script>
</body>
</html>