# RPA实训平台 - 开发规范文档

## 📁 项目结构规范

```
rpa-training-platform/
├── api/                    # 后端API模块
│   ├── controllers/        # 控制器层
│   ├── middleware/         # 中间件
│   ├── routes/            # 路由定义
│   └── utils/             # 工具函数
├── cloudbase/             # 云开发相关
│   └── database/          # 数据库配置
├── frontend/              # 前端资源
│   ├── scenarios/         # 场景页面
│   │   ├── module1/       # 第一模块：基础操作
│   │   ├── module2/       # 第二模块：流程整合
│   │   └── module3/       # 第三模块：智能应用
│   ├── components/        # 共享组件
│   ├── assets/           # 静态资源
│   └── utils/            # 前端工具函数
├── tests/                 # 测试文件
│   ├── unit/             # 单元测试
│   ├── integration/      # 集成测试
│   └── e2e/              # 端到端测试
├── docs/                  # 项目文档
├── scripts/              # 构建脚本
└── config/               # 配置文件
```

## 🔧 开发环境配置

### 1. 安装依赖
```bash
npm install
```

### 2. 安装代码规范工具
```bash
npm install --save-dev eslint prettier husky lint-staged
```

### 3. 初始化Git钩子
```bash
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
npx husky add .husky/pre-push "npm run test"
```

## 📝 代码规范

### JavaScript规范
- 使用ES6+语法
- 单引号字符串
- 2空格缩进
- 无分号结尾
- 驼峰命名法
- 函数和变量使用描述性命名

### 文件命名规范
- 文件名：kebab-case（连字符）
- 组件名：PascalCase
- 变量名：camelCase
- 常量名：UPPER_SNAKE_CASE

### 目录结构规范
```
frontend/scenarios/
├── module1/               # 第一模块
│   ├── bank-statement-download.html
│   ├── invoice-generation.html
│   ├── asset-verification.html
│   └── tax-reminder.html
├── module2/               # 第二模块  
│   ├── receivables-reconciliation.html
│   ├── vat-invoice-certification.html
│   ├── payroll-generation.html
│   └── financial-report-consolidation.html
└── module3/               # 第三模块
    ├── invoice-ocr-verification.html
    ├── expense-reimbursement-review.html
    ├── contract-stamp-tax-calculation.html
    └── bank-reconciliation.html
```

## 🧪 测试规范

### 测试分类
1. **单元测试** - 测试单个函数/模块
2. **集成测试** - 测试API接口
3. **端到端测试** - 测试完整用户流程

### 测试命令
```bash
npm test              # 运行所有测试
npm run test:watch    # 监视模式运行测试
npm run test:coverage # 生成覆盖率报告
```

### 测试文件命名
- 单元测试：`*.test.js`
- 集成测试：`*.integration.test.js`
- 端到端测试：`*.e2e.test.js`

## 🔄 Git工作流

### 分支策略
- `main` - 主分支，用于生产环境
- `develop` - 开发分支，用于集成开发
- `feature/xxx` - 功能分支
- `hotfix/xxx` - 热修复分支

### 提交信息规范
```
type(scope): subject

body

footer
```

**Type类型：**
- `feat` - 新功能
- `fix` - 修复bug
- `docs` - 文档更新
- `style` - 代码格式（不影响代码运行）
- `refactor` - 重构
- `test` - 测试
- `chore` - 构建过程或辅助工具的变动

**示例：**
```
feat(scenarios): 添加场景5应收账款对账功能

- 实现多数据源整合
- 添加模糊匹配算法
- 完成核销操作流程

Closes #123
```

## 🚀 部署流程

### 开发环境
```bash
npm run dev
```

### 生产构建
```bash
npm run build
```

### 代码质量检查
```bash
npm run validate  # 执行lint + test + build
```

## 📋 开发检查清单

### 功能开发前
- [ ] 创建功能分支
- [ ] 阅读相关PRD和设计文档
- [ ] 确认技术方案

### 开发过程中
- [ ] 编写单元测试
- [ ] 遵循代码规范
- [ ] 及时提交代码
- [ ] 编写有意义的提交信息

### 功能完成后
- [ ] 执行完整测试
- [ ] 代码review
- [ ] 更新文档
- [ ] 合并到develop分支

## 🛠 常用命令

```bash
# 开发
npm run dev                    # 启动开发服务器
npm run lint                   # 代码检查
npm run lint:fix              # 自动修复代码问题
npm run format                 # 格式化代码

# 测试
npm test                       # 运行测试
npm run test:watch            # 监视模式运行测试
npm run test:coverage         # 生成覆盖率报告

# 构建
npm run build                 # 生产构建
npm run validate              # 完整验证（lint + test + build）

# Git
git add .
git commit -m "feat: 添加新功能"
git push origin feature/xxx
```

## 📚 推荐资源

- [JavaScript Standard Style](https://standardjs.com/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Jest Testing Framework](https://jestjs.io/)
- [ESLint Rules](https://eslint.org/docs/rules/)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)

---

**更新日期**: 2025年8月4日  
**维护者**: chanwarmsun