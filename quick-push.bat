@echo off
echo ==========================================
echo      RPA实训平台 - 快速推送到GitHub
echo ==========================================
echo.

cd /d "D:\G盘（软件）\cursor开发文件\claude-RPA实训平台3.0"

echo 🔍 检查文件变化...
git status
echo.

set /p msg="💬 请输入提交信息（直接回车使用默认信息）: "
if "%msg%"=="" set msg=update: 更新项目代码 %date% %time%

echo.
echo 📦 添加文件到暂存区...
git add .

echo 💾 提交更改...
git commit -m "%msg%"

echo 🚀 推送到GitHub...
git push origin main

echo.
if %errorlevel% equ 0 (
    echo ✅ 推送成功！
    echo 🌐 访问你的仓库: https://github.com/chanwarmsun/RPA
) else (
    echo ❌ 推送失败，请检查网络连接或认证信息
)

echo.
echo 按任意键退出...
pause > nul