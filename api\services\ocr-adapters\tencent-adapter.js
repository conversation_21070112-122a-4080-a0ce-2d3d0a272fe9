/**
 * 腾讯云OCR适配器
 * 基于腾讯云OCR服务
 */

const axios = require('axios');
const crypto = require('crypto');
const BaseOCRAdapter = require('./base-adapter');

class TencentOCRAdapter extends BaseOCRAdapter {
    constructor(config = {}) {
        super(config);
        this.name = 'tencent';
        this.priority = 3; // 第三优先级
        this.enabled = true;
        
        this.secretId = config.secretId || process.env.TENCENT_SECRET_ID;
        this.secretKey = config.secretKey || process.env.TENCENT_SECRET_KEY;
        this.region = config.region || 'ap-beijing';
        
        this.endpoints = {
            invoice: 'ocr.tencentcloudapi.com',
            general: 'ocr.tencentcloudapi.com'
        };
    }

    /**
     * 检查腾讯云适配器是否可用
     */
    isAvailable() {
        return !!(this.secretId && this.secretKey && 
                 this.secretId.trim() !== '' && this.secretKey.trim() !== '');
    }

    /**
     * 获取支持的功能
     */
    getFeatures() {
        return [
            '发票OCR',
            '通用印刷体OCR',
            '手写体识别',
            '表格识别',
            '证件识别',
            '票据识别'
        ];
    }

    /**
     * 腾讯云发票OCR识别
     */
    async recognizeInvoice(imageData, filename) {
        try {
            console.log(`☁️ 腾讯云OCR识别发票: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('腾讯云OCR未配置密钥');
            }

            const base64Image = this.preprocessImage(imageData);
            
            const params = {
                ImageBase64: base64Image
            };

            const result = await this.callTencentAPI('VatInvoiceOCR', params);
            return this.parseInvoiceResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 腾讯云OCR识别失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 腾讯云通用OCR识别
     */
    async recognizeGeneral(imageData, filename) {
        try {
            console.log(`☁️ 腾讯云通用OCR识别: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('腾讯云OCR未配置密钥');
            }

            const base64Image = this.preprocessImage(imageData);
            
            const params = {
                ImageBase64: base64Image,
                LanguageType: 'zh'
            };

            const result = await this.callTencentAPI('GeneralBasicOCR', params);
            return this.parseGeneralResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 腾讯云通用OCR失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 调用腾讯云API
     */
    async callTencentAPI(action, params) {
        const service = 'ocr';
        const version = '2018-11-19';
        const timestamp = Math.floor(Date.now() / 1000);
        const date = new Date(timestamp * 1000).toISOString().substr(0, 10);

        // 构建请求
        const payload = JSON.stringify(params);
        
        // 计算签名
        const authorization = this.generateTencentSignature(
            service, action, version, timestamp, date, payload
        );

        const headers = {
            'Authorization': authorization,
            'Content-Type': 'application/json; charset=utf-8',
            'Host': this.endpoints.invoice,
            'X-TC-Action': action,
            'X-TC-Timestamp': timestamp,
            'X-TC-Version': version,
            'X-TC-Region': this.region
        };

        const response = await axios.post(
            `https://${this.endpoints.invoice}/`,
            payload,
            { headers, timeout: 30000 }
        );

        if (response.data.Response.Error) {
            throw new Error(`腾讯云API错误: ${response.data.Response.Error.Message}`);
        }

        return response.data.Response;
    }

    /**
     * 生成腾讯云API签名
     */
    generateTencentSignature(service, action, version, timestamp, date, payload) {
        const algorithm = 'TC3-HMAC-SHA256';
        const canonicalRequest = this.buildCanonicalRequest(action, payload);
        const credentialScope = `${date}/${service}/tc3_request`;
        const stringToSign = `${algorithm}\n${timestamp}\n${credentialScope}\n${this.sha256(canonicalRequest)}`;
        
        const secretDate = this.hmacSha256(`TC3${this.secretKey}`, date);
        const secretService = this.hmacSha256(secretDate, service);
        const secretSigning = this.hmacSha256(secretService, 'tc3_request');
        const signature = this.hmacSha256(secretSigning, stringToSign, 'hex');
        
        return `${algorithm} Credential=${this.secretId}/${credentialScope}, SignedHeaders=content-type;host, Signature=${signature}`;
    }

    /**
     * 构建规范请求
     */
    buildCanonicalRequest(action, payload) {
        const canonicalUri = '/';
        const canonicalQueryString = '';
        const canonicalHeaders = `content-type:application/json; charset=utf-8\nhost:${this.endpoints.invoice}\n`;
        const signedHeaders = 'content-type;host';
        const hashedRequestPayload = this.sha256(payload);
        
        return `POST\n${canonicalUri}\n${canonicalQueryString}\n${canonicalHeaders}\n${signedHeaders}\n${hashedRequestPayload}`;
    }

    /**
     * SHA256散列
     */
    sha256(data) {
        return crypto.createHash('sha256').update(data, 'utf8').digest('hex');
    }

    /**
     * HMAC-SHA256
     */
    hmacSha256(key, data, encoding = null) {
        const hmac = crypto.createHmac('sha256', key);
        hmac.update(data, 'utf8');
        return encoding ? hmac.digest(encoding) : hmac.digest();
    }

    /**
     * 解析腾讯云发票识别结果
     */
    parseInvoiceResult(tencentResult, filename) {
        const vatInfo = tencentResult.VatInvoiceInfos?.[0] || {};
        
        const standardizedData = {
            invoiceNumber: vatInfo.InvoiceNum || '',
            invoiceCode: vatInfo.InvoiceCode || '',
            date: vatInfo.InvoiceDate || '',
            sellerName: vatInfo.SellerName || '',
            sellerTaxId: vatInfo.SellerTaxID || '',
            sellerAddress: vatInfo.SellerAddress || '',
            sellerPhone: vatInfo.SellerPhone || '',
            buyerName: vatInfo.BuyerName || '',
            buyerTaxId: vatInfo.BuyerTaxID || '',
            buyerAddress: vatInfo.BuyerAddress || '',
            buyerPhone: vatInfo.BuyerPhone || '',
            totalAmount: this.parseAmount(vatInfo.Total),
            amountWithoutTax: this.parseAmount(vatInfo.TotalWithoutTax),
            taxAmount: this.parseAmount(vatInfo.TotalTax),
            items: this.parseTencentInvoiceItems(vatInfo.CommodityInfos || []),
            remarks: vatInfo.Remarks || '',
            checkCode: vatInfo.CheckCode || ''
        };

        const result = this.standardizeResult({
            data: standardizedData,
            confidence: 0.87,
            rawData: tencentResult
        }, filename, 'tencent');

        console.log(`✅ 腾讯云发票识别成功: ${standardizedData.invoiceNumber}`);
        return result;
    }

    /**
     * 解析腾讯云通用OCR结果
     */
    parseGeneralResult(tencentResult, filename) {
        const textDetections = tencentResult.TextDetections || [];
        const lines = textDetections.map(item => item.DetectedText);
        
        return this.standardizeResult({
            data: {
                text: lines.join('\n'),
                lines: lines,
                lineCount: lines.length
            },
            confidence: 0.85,
            rawData: tencentResult
        }, filename, 'tencent');
    }

    /**
     * 解析腾讯云发票商品条目
     */
    parseTencentInvoiceItems(commodityInfos) {
        if (!Array.isArray(commodityInfos)) return [];
        
        return commodityInfos.map((item, index) => ({
            id: index + 1,
            name: item.Name || '',
            quantity: this.parseAmount(item.Quantity) || 1,
            unitPrice: this.parseAmount(item.Price),
            amount: this.parseAmount(item.Total)
        }));
    }
}

module.exports = TencentOCRAdapter;