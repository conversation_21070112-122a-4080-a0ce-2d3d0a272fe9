/**
 * OCR适配器基类
 * 定义统一的OCR接口规范，所有厂商适配器都必须实现这些方法
 */

class BaseOCRAdapter {
    constructor(config = {}) {
        this.config = config;
        this.name = 'base';
        this.priority = 999; // 优先级，数字越小优先级越高
        this.enabled = false;
    }

    /**
     * 检查适配器是否可用（API密钥是否配置等）
     * @returns {boolean}
     */
    isAvailable() {
        throw new Error('子类必须实现 isAvailable 方法');
    }

    /**
     * 发票OCR识别
     * @param {Buffer|string} imageData - 图片数据
     * @param {string} filename - 文件名
     * @returns {Promise<Object>} 标准化的识别结果
     */
    async recognizeInvoice(imageData, filename) {
        throw new Error('子类必须实现 recognizeInvoice 方法');
    }

    /**
     * 通用OCR识别
     * @param {Buffer|string} imageData - 图片数据
     * @param {string} filename - 文件名
     * @returns {Promise<Object>} 标准化的识别结果
     */
    async recognizeGeneral(imageData, filename) {
        throw new Error('子类必须实现 recognizeGeneral 方法');
    }

    /**
     * 获取适配器信息
     * @returns {Object}
     */
    getInfo() {
        return {
            name: this.name,
            priority: this.priority,
            enabled: this.enabled,
            available: this.isAvailable(),
            features: this.getFeatures()
        };
    }

    /**
     * 获取支持的功能列表
     * @returns {Array}
     */
    getFeatures() {
        return ['发票识别', '通用OCR'];
    }

    /**
     * 标准化识别结果格式
     * @param {Object} rawResult - 原始识别结果
     * @param {string} filename - 文件名
     * @param {string} source - 数据源标识
     * @returns {Object}
     */
    standardizeResult(rawResult, filename, source) {
        return {
            success: true,
            filename: filename,
            confidence: rawResult.confidence || 0.8,
            source: source,
            data: rawResult.data || {},
            rawData: rawResult.rawData || rawResult,
            processedAt: new Date().toISOString()
        };
    }

    /**
     * 标准化错误结果
     * @param {Error} error - 错误对象
     * @param {string} filename - 文件名
     * @returns {Object}
     */
    standardizeError(error, filename) {
        return {
            success: false,
            filename: filename,
            error: error.message,
            source: this.name,
            processedAt: new Date().toISOString()
        };
    }

    /**
     * 图片数据预处理
     * @param {Buffer|string} imageData - 图片数据
     * @returns {string} base64字符串
     */
    preprocessImage(imageData) {
        if (Buffer.isBuffer(imageData)) {
            return imageData.toString('base64');
        } else if (typeof imageData === 'string') {
            // 移除data URL前缀
            return imageData.replace(/^data:image\/[a-z]+;base64,/, '');
        }
        throw new Error('不支持的图片数据格式');
    }

    /**
     * 创建data URL
     * @param {Buffer|string} imageData - 图片数据
     * @param {string} mimeType - MIME类型
     * @returns {string}
     */
    createDataUrl(imageData, mimeType = 'image/jpeg') {
        const base64 = this.preprocessImage(imageData);
        return `data:${mimeType};base64,${base64}`;
    }

    /**
     * 解析金额字符串为数字
     * @param {string} amountStr - 金额字符串
     * @returns {number}
     */
    parseAmount(amountStr) {
        if (!amountStr) return 0;
        const cleanAmount = amountStr.toString().replace(/[^0-9.-]/g, '');
        const amount = parseFloat(cleanAmount);
        return isNaN(amount) ? 0 : amount;
    }

    /**
     * 验证发票数据完整性
     * @param {Object} invoiceData - 发票数据
     * @returns {Object}
     */
    validateInvoiceData(invoiceData) {
        const errors = [];
        const requiredFields = ['invoiceNumber', 'date', 'sellerName', 'totalAmount'];
        
        requiredFields.forEach(field => {
            if (!invoiceData[field] || invoiceData[field] === '') {
                errors.push(`缺少${field}字段`);
            }
        });

        if (invoiceData.totalAmount <= 0) {
            errors.push('发票金额必须大于零');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

module.exports = BaseOCRAdapter;