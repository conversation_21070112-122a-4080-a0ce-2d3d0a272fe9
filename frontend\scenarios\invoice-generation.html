<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量开具电子发票 - RPA实训场景2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .header .scenario-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #8D99AE;
            font-size: 14px;
        }
        
        .simulation-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .system-header {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .system-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .system-subtitle {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .step.active .step-number {
            background: #7673FF;
            color: white;
        }
        
        .step.completed .step-number {
            background: #7673FF;
            color: white;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118, 115, 255, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118, 115, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118, 115, 255, 0.4);
        }
        
        .btn-success {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118, 115, 255, 0.3);
        }
        
        .btn-success:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118, 115, 255, 0.4);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .success-message {
            background: #d1fae5;
            color: #065f46;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border-left: 4px solid #10b981;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #7673FF;
            border-radius: 3px;
            transition: width 0.3s;
            width: 0%;
        }
        
        .validation-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            display: none;
        }
        
        .validation-status.success {
            background: #7673FF;
        }
        
        .validation-status.error {
            background: #ef4444;
        }
        
        .rpa-hints {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .rpa-hints h4 {
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .rpa-hints ul {
            color: #92400e;
            margin-left: 20px;
        }

        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.05);
        }

        .file-upload-area.active {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.1);
        }

        .invoice-form {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #7673FF;
        }

        .progress-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .progress-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .progress-circle.completed {
            background: #10b981;
        }

        .progress-circle.processing {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 场景头部信息 -->
        <div class="header">
            <h1>场景2：批量开具电子发票</h1>
            <p>学习Excel数据读取和自动化开票流程，掌握批量处理和循环操作技能</p>
            <div class="scenario-info">
                <div class="info-item">
                    <span>📚</span>
                    <span>模块：基础操作</span>
                </div>
                <div class="info-item">
                    <span>⏱️</span>
                    <span>预计时间：45分钟</span>
                </div>
                <div class="info-item">
                    <span>📊</span>
                    <span>难度：初级</span>
                </div>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 仿真开票系统 -->
        <div class="simulation-container">
            <div class="system-header">
                <div class="system-logo">📧 练习电子发票开具系统</div>
                <div class="system-subtitle">Electronic Invoice System - Training Environment</div>
            </div>

            <div class="main-content">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                        <span>上传Excel</span>
                    </div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <span>数据预览</span>
                    </div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                        <span>批量开票</span>
                    </div>
                    <div class="step" id="step4">
                        <div class="step-number">4</div>
                        <span>完成确认</span>
                    </div>
                </div>

                <!-- 第一步：上传Excel文件 -->
                <div class="section active" id="uploadSection">
                    <h3>上传订单数据Excel文件</h3>
                    
                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>使用影刀RPA的文件上传组件定位上传区域（ID: file-upload-area）</li>
                            <li>选择包含订单信息的Excel文件</li>
                            <li>等待文件解析完成后进入下一步</li>
                        </ul>
                    </div>

                    <div class="file-upload-area" id="file-upload-area">
                        <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                        <h4>点击或拖拽上传Excel文件</h4>
                        <p style="color: #8D99AE; margin-top: 10px;">支持 .xlsx, .xls 格式，文件大小不超过10MB</p>
                        <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <button class="btn btn-primary" id="uploadBtn" disabled>开始解析数据</button>
                    </div>
                </div>

                <!-- 第二步：数据预览 -->
                <div class="section" id="previewSection">
                    <h3>订单数据预览与验证</h3>

                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>检查数据表格中的客户信息和商品明细（表格ID: order-preview-table）</li>
                            <li>验证必填字段是否完整（客户名称、商品、金额、税率）</li>
                            <li>确认数据无误后开始批量开票</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 15px; color: #666;">
                        解析成功！共发现 <span id="totalOrders">0</span> 条订单记录
                    </div>

                    <table class="data-table" id="order-preview-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>客户名称</th>
                                <th>纳税人识别号</th>
                                <th>商品名称</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>金额</th>
                                <th>税率</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="orderTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>

                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" id="startInvoiceBtn">📧 开始批量开票</button>
                        <button class="btn btn-secondary" id="backToUpload">🔙 重新上传</button>
                    </div>
                </div>

                <!-- 第三步：批量开票处理 -->
                <div class="section" id="processingSection">
                    <h3>批量开具发票中...</h3>

                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>系统正在逐个处理每条订单记录</li>
                            <li>RPA会自动填写发票表单并提交</li>
                            <li>观察进度指示器了解处理进度</li>
                        </ul>
                    </div>

                    <div id="processingList">
                        <!-- 动态生成处理进度 -->
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <div style="font-size: 18px; color: #7673FF;">正在处理第 <span id="currentOrder">1</span> / <span id="totalOrderCount">10</span> 条订单</div>
                        <div class="progress-bar" style="margin: 15px 0;">
                            <div class="progress-fill" id="processingProgress"></div>
                        </div>
                    </div>
                </div>

                <!-- 第四步：完成确认 -->
                <div class="section" id="completionSection">
                    <h3>批量开票完成</h3>

                    <div class="success-message">
                        <h4>✅ 任务完成！</h4>
                        <p>恭喜您成功完成批量开具电子发票场景！</p>
                        <ul style="margin-top: 10px; margin-left: 20px;">
                            <li>✓ 成功上传并解析Excel文件</li>
                            <li>✓ 验证订单数据完整性</li>
                            <li>✓ 批量处理所有订单开票</li>
                            <li>✓ 完成发票生成和编号分配</li>
                        </ul>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4>开票结果统计：</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
                            <div style="text-align: center; padding: 15px; background: #f0fdf4; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #16a34a;" id="successCount">0</div>
                                <div style="color: #16a34a;">成功开票</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: #fef2f2; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #dc2626;" id="failCount">0</div>
                                <div style="color: #dc2626;">开票失败</div>
                            </div>
                            <div style="text-align: center; padding: 15px; background: #f3f4f6; border-radius: 8px;">
                                <div style="font-size: 24px; font-weight: bold; color: #7673FF;" id="totalAmount">¥0</div>
                                <div style="color: #7673FF;">总开票金额</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button class="btn btn-primary" id="resetBtn">🔄 重新练习</button>
                        <a href="student-dashboard.html" class="btn btn-secondary">📋 返回主界面</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 验证状态提示 -->
    <div class="validation-status" id="validationStatus"></div>

    <script>
        // 场景2的JavaScript逻辑
        class InvoiceGenerationScenario {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 4;
                this.orders = [];
                this.isCompleted = false;
                
                this.init();
                this.generateMockOrders();
            }

            init() {
                // 绑定事件监听器
                document.getElementById('file-upload-area').addEventListener('click', () => {
                    document.getElementById('fileInput').click();
                });
                
                document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileUpload(e));
                document.getElementById('uploadBtn').addEventListener('click', () => this.processFile());
                document.getElementById('startInvoiceBtn').addEventListener('click', () => this.startBatchInvoicing());
                document.getElementById('backToUpload').addEventListener('click', () => this.backToUpload());
                document.getElementById('resetBtn').addEventListener('click', () => this.reset());
                
                // 拖拽支持
                const uploadArea = document.getElementById('file-upload-area');
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('active');
                });
                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('active');
                });
                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('active');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleFileSelection(files[0]);
                    }
                });
                
                // 更新进度
                this.updateProgress();
            }

            generateMockOrders() {
                const companies = ['华为技术有限公司', '腾讯科技', '阿里巴巴集团', '百度在线', '字节跳动', '京东集团', '美团', '滴滴出行', '小米科技', '网易公司'];
                const products = ['办公用品', '技术服务', '软件许可', '设备租赁', '咨询服务', '培训服务', '维护服务', '系统集成'];
                
                this.orders = [];
                for (let i = 1; i <= 10; i++) {
                    const company = companies[Math.floor(Math.random() * companies.length)];
                    const product = products[Math.floor(Math.random() * products.length)];
                    const quantity = Math.floor(Math.random() * 10) + 1;
                    const unitPrice = (Math.random() * 1000 + 100).toFixed(2);
                    const amount = (quantity * unitPrice).toFixed(2);
                    
                    this.orders.push({
                        id: i,
                        customerName: company,
                        taxNumber: '91110000' + Math.floor(Math.random() * 1000000000).toString().padStart(9, '0'),
                        productName: product,
                        quantity: quantity,
                        unitPrice: parseFloat(unitPrice),
                        amount: parseFloat(amount),
                        taxRate: '13%',
                        status: 'pending'
                    });
                }
            }

            handleFileUpload(e) {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileSelection(file);
                }
            }

            handleFileSelection(file) {
                if (!file.name.match(/\.(xlsx|xls)$/)) {
                    this.showValidationMessage('请选择Excel文件（.xlsx或.xls格式）', 'error');
                    return;
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    this.showValidationMessage('文件大小不能超过10MB', 'error');
                    return;
                }
                
                // 模拟文件选择成功
                const uploadArea = document.getElementById('file-upload-area');
                uploadArea.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 15px;">✅</div>
                    <h4>文件选择成功</h4>
                    <p style="color: #16a34a; margin-top: 10px;">文件名: ${file.name}</p>
                    <p style="color: #8D99AE;">文件大小: ${(file.size / 1024).toFixed(1)} KB</p>
                `;
                
                document.getElementById('uploadBtn').disabled = false;
                this.showValidationMessage('Excel文件选择成功，可以开始解析', 'success');
            }

            processFile() {
                this.showValidationMessage('正在解析Excel文件...', 'success');
                
                // 模拟文件处理
                setTimeout(() => {
                    this.showValidationMessage('Excel文件解析完成！', 'success');
                    this.loadOrderData();
                    this.nextStep();
                }, 2000);
            }

            loadOrderData() {
                const tbody = document.getElementById('orderTableBody');
                tbody.innerHTML = '';
                
                document.getElementById('totalOrders').textContent = this.orders.length;
                
                this.orders.forEach((order, index) => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${order.id}</td>
                        <td>${order.customerName}</td>
                        <td>${order.taxNumber}</td>
                        <td>${order.productName}</td>
                        <td>${order.quantity}</td>
                        <td>¥${order.unitPrice.toFixed(2)}</td>
                        <td>¥${order.amount.toFixed(2)}</td>
                        <td>${order.taxRate}</td>
                        <td><span style="color: #f59e0b;">待处理</span></td>
                    `;
                    tbody.appendChild(tr);
                });
            }

            startBatchInvoicing() {
                this.showValidationMessage('开始批量开具发票...', 'success');
                this.nextStep();
                this.processBatchInvoicing();
            }

            async processBatchInvoicing() {
                const processingList = document.getElementById('processingList');
                const currentOrderSpan = document.getElementById('currentOrder');
                const totalOrderSpan = document.getElementById('totalOrderCount');
                const progressBar = document.getElementById('processingProgress');
                
                totalOrderSpan.textContent = this.orders.length;
                
                for (let i = 0; i < this.orders.length; i++) {
                    const order = this.orders[i];
                    currentOrderSpan.textContent = i + 1;
                    
                    // 创建进度项
                    const progressItem = document.createElement('div');
                    progressItem.className = 'progress-indicator';
                    progressItem.innerHTML = `
                        <div class="progress-circle processing">${i + 1}</div>
                        <span>正在为 ${order.customerName} 开具发票...</span>
                    `;
                    processingList.appendChild(progressItem);
                    
                    // 模拟处理时间
                    await new Promise(resolve => setTimeout(resolve, 800));
                    
                    // 更新状态为完成
                    const circle = progressItem.querySelector('.progress-circle');
                    circle.classList.remove('processing');
                    circle.classList.add('completed');
                    circle.textContent = '✓';
                    progressItem.querySelector('span').textContent = `${order.customerName} - 发票开具成功`;
                    
                    order.status = 'completed';
                    
                    // 更新进度条
                    const progress = ((i + 1) / this.orders.length) * 100;
                    progressBar.style.width = progress + '%';
                }
                
                // 完成处理
                setTimeout(() => {
                    this.showValidationMessage('所有发票开具完成！', 'success');
                    this.showCompletionStats();
                    this.nextStep();
                    this.validateCompletion();
                }, 1000);
            }

            showCompletionStats() {
                const successCount = this.orders.filter(o => o.status === 'completed').length;
                const failCount = this.orders.length - successCount;
                const totalAmount = this.orders.reduce((sum, o) => sum + o.amount, 0);
                
                document.getElementById('successCount').textContent = successCount;
                document.getElementById('failCount').textContent = failCount;
                document.getElementById('totalAmount').textContent = '¥' + totalAmount.toFixed(2);
            }

            backToUpload() {
                this.currentStep = 1;
                this.updateStepsAndSections();
                this.reset();
            }

            nextStep() {
                if (this.currentStep < this.totalSteps) {
                    // 更新步骤状态
                    document.getElementById(`step${this.currentStep}`).classList.add('completed');
                    document.getElementById(`step${this.currentStep}`).classList.remove('active');
                    
                    this.currentStep++;
                    
                    if (this.currentStep <= this.totalSteps) {
                        document.getElementById(`step${this.currentStep}`).classList.add('active');
                    }
                    
                    this.updateStepsAndSections();
                    this.updateProgress();
                }
            }

            updateStepsAndSections() {
                // 显示对应的内容区域
                document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
                
                const sections = ['uploadSection', 'previewSection', 'processingSection', 'completionSection'];
                if (sections[this.currentStep - 1]) {
                    document.getElementById(sections[this.currentStep - 1]).classList.add('active');
                }
            }

            updateProgress() {
                const progress = (this.currentStep - 1) / (this.totalSteps - 1) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
            }

            async validateCompletion() {
                try {
                    // 调用后端验证API
                    const response = await fetch('/api/validate/scenario/2', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + localStorage.getItem('token')
                        },
                        body: JSON.stringify({
                            scenario_id: 2,
                            validation_data: {
                                file_uploaded: true,
                                data_processed: true,
                                batch_invoicing_completed: true,
                                orders_count: this.orders.length,
                                steps_completed: this.currentStep
                            }
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        this.showValidationMessage(`任务验证成功！得分：${result.data.score}`, 'success');
                        this.isCompleted = true;
                    } else {
                        this.showValidationMessage('任务验证失败：' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('验证请求失败:', error);
                    this.showValidationMessage('网络连接失败，但任务已完成', 'success');
                }
            }

            showValidationMessage(message, type) {
                const status = document.getElementById('validationStatus');
                status.className = `validation-status ${type}`;
                status.textContent = message;
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }

            reset() {
                this.currentStep = 1;
                this.isCompleted = false;
                
                // 重置文件上传区域
                const uploadArea = document.getElementById('file-upload-area');
                uploadArea.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
                    <h4>点击或拖拽上传Excel文件</h4>
                    <p style="color: #8D99AE; margin-top: 10px;">支持 .xlsx, .xls 格式，文件大小不超过10MB</p>
                    <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                `;
                
                // 重新绑定文件上传事件
                document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileUpload(e));
                
                document.getElementById('uploadBtn').disabled = true;
                document.getElementById('processingList').innerHTML = '';
                
                // 重置步骤状态
                document.querySelectorAll('.step').forEach(step => {
                    step.classList.remove('active', 'completed');
                });
                document.getElementById('step1').classList.add('active');
                
                // 显示第一步
                document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
                document.getElementById('uploadSection').classList.add('active');
                
                this.updateProgress();
                this.generateMockOrders();
            }
        }

        // 初始化场景
        document.addEventListener('DOMContentLoaded', () => {
            new InvoiceGenerationScenario();
        });
    </script>
</body>
</html>