/**
 * 完整Mock服务器 - 包含API模拟
 * 当依赖安装有问题时的完整替代方案
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');

const PORT = 3000;

// Mock用户数据
const mockUsers = [
  {
    id: 'user_001',
    username: 'teacher1',
    password: 'password123',
    name: '张老师',
    email: '<EMAIL>',
    role: 'teacher',
    department: '财务学院',
    status: 'active'
  },
  {
    id: 'user_002',
    username: 'student1',
    password: 'password123',
    name: '李小明',
    studentId: '2024001',
    email: '<EMAIL>',
    role: 'student',
    class_id: 'class1',
    status: 'active'
  },
  {
    id: 'user_003',
    username: 'student2',
    password: 'password123',
    name: '王小红',
    studentId: '2024002',
    email: '<EMAIL>',
    role: 'student',
    class_id: 'class1',
    status: 'active'
  }
];

// Mock场景数据
const mockScenarios = {
  module1: [
    {
      id: 1,
      title: '企业网银流水查询下载',
      code: 'bank-statement-download',
      description: '学习企业网银操作流程，掌握流水查询和批量下载技能',
      module: 1,
      module_name: '基础操作',
      difficulty: 'beginner',
      estimated_time: 30,
      progress: { status: 'not_started', progress: 0, score: null },
      is_active: true
    },
    {
      id: 2,
      title: '批量开具电子发票',
      code: 'batch-invoice-creation',
      description: '从Excel读取订单信息，在开票系统网页中为每笔订单开具发票',
      module: 1,
      module_name: '基础操作',
      difficulty: 'beginner',
      estimated_time: 35,
      progress: { status: 'not_started', progress: 0, score: null },
      is_active: true
    }
  ],
  module2: [],
  module3: []
};

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon'
};

// 生成简单的JWT Token (mock)
function generateToken(user) {
  const payload = {
    id: user.id,
    username: user.username,
    role: user.role,
    exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
  };
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

// 验证Token
function verifyToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    if (payload.exp < Date.now()) {
      return null; // Token过期
    }
    return payload;
  } catch (error) {
    return null;
  }
}

// 处理API请求
function handleAPI(req, res, pathname, parsedUrl) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 学生登录API
  if (pathname === '/api/auth/student/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const user = mockUsers.find(u => u.username === data.username && u.password === data.password);
        
        if (user) {
          const token = generateToken(user);
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: '登录成功',
            data: {
              token,
              user: {
                id: user.id,
                username: user.username,
                name: user.name,
                role: user.role,
                studentId: user.studentId
              }
            }
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          }));
        }
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          message: '请求格式错误'
        }));
      }
    });
    return;
  }

  // Token验证API
  if (pathname === '/api/auth/verify' && req.method === 'GET') {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: '未授权访问'
      }));
      return;
    }

    const token = authHeader.split(' ')[1];
    const payload = verifyToken(token);
    
    if (!payload) {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: 'Token无效或已过期'
      }));
      return;
    }

    const user = mockUsers.find(u => u.id === payload.id);
    if (user) {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        data: {
          user: {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            studentId: user.studentId
          }
        }
      }));
    } else {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: '用户不存在'
      }));
    }
    return;
  }

  // 场景列表API
  if (pathname === '/api/scenarios' && req.method === 'GET') {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: '未授权访问'
      }));
      return;
    }

    const token = authHeader.split(' ')[1];
    const payload = verifyToken(token);
    
    if (!payload) {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        message: 'Token无效或已过期'
      }));
      return;
    }

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: {
        scenarios: mockScenarios
      }
    }));
    return;
  }

  // 场景验证API
  if (pathname.startsWith('/api/validate/') && req.method === 'POST') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: '验证通过！',
      data: {
        score: 85,
        feedback: '恭喜！您已成功完成本场景的所有步骤。',
        completed_steps: ['login', 'query', 'download'],
        suggestions: ['可以尝试更高级的查询条件']
      }
    }));
    return;
  }

  // 健康检查
  if (pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'OK',
      timestamp: new Date().toISOString(),
      version: '1.1.0-mock',
      environment: 'development'
    }));
    return;
  }

  // API未找到
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    success: false,
    message: 'API endpoint not found',
    path: pathname
  }));
}

// 处理静态文件
function handleStatic(req, res, pathname) {
  // 处理根路径
  if (pathname === '/') {
    pathname = '/scenarios/login.html';
  }
  
  // 处理scenarios目录下的相对路径（登录后跳转）
  if (pathname.indexOf('/scenarios/') !== 0 && !pathname.startsWith('/assets/')) {
    // 如果不是以/scenarios/开头且不是assets资源，假设是scenarios下的页面
    if (pathname.endsWith('.html') || pathname.indexOf('.') === -1) {
      pathname = '/scenarios' + pathname;
    }
  }

  const filePath = path.join(__dirname, 'frontend', pathname);
  
  // 检查文件是否存在，如果不存在尝试添加.html扩展名
  let finalFilePath = filePath;
  if (!fs.existsSync(filePath)) {
    // 尝试添加.html扩展名
    if (!path.extname(filePath)) {
      finalFilePath = filePath + '.html';
    }
  }
  
  // 如果还是不存在，返回404
  if (!fs.existsSync(finalFilePath)) {
    console.log(`404: 文件未找到 - 原始路径: ${pathname}, 文件路径: ${filePath}, 最终路径: ${finalFilePath}`);
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <html>
        <head><title>404 - 页面未找到</title></head>
        <body style="font-family: Arial, sans-serif; padding: 50px;">
          <h1>404 - 页面未找到</h1>
          <p>请求的页面 ${pathname} 不存在</p>
          <p>尝试查找的文件路径: ${finalFilePath}</p>
          <p><a href="/scenarios/login.html">返回登录页</a></p>
          <p><a href="/scenarios/student-dashboard.html">学生门户</a></p>
          <hr>
          <p><small>Mock Server v1.1.0 - Debug Info</small></p>
        </body>
      </html>
    `);
    return;
  }

  // 获取文件扩展名和对应的MIME类型
  const ext = path.extname(finalFilePath);
  const mimeType = mimeTypes[ext] || 'text/plain';
  
  // 读取并返回文件
  fs.readFile(finalFilePath, (err, data) => {
    if (err) {
      res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end('<h1>500 - 服务器错误</h1>');
      return;
    }
    
    res.writeHead(200, { 
      'Content-Type': mimeType + '; charset=utf-8'
    });
    res.end(data);
  });
}

// 创建服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
  
  // 处理API请求
  if (pathname.startsWith('/api/') || pathname === '/health') {
    handleAPI(req, res, pathname, parsedUrl);
  } else {
    // 处理静态文件
    handleStatic(req, res, pathname);
  }
});

server.listen(PORT, () => {
  console.log(`🚀 完整Mock RPA教学平台已启动`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`🔐 演示账号:`);
  console.log(`   学生: student1 / password123`);
  console.log(`   学生: student2 / password123`);
  console.log(`   教师: teacher1 / password123`);
  console.log(`✨ 功能: 完整的登录+场景展示+API模拟`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭Mock服务器...');
  server.close(() => {
    console.log('Mock服务器已关闭');
    process.exit(0);
  });
});