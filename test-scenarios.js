/**
 * 场景测试脚本 - 验证8个场景的代码完整性
 */

const fs = require('fs');
const path = require('path');

// 测试配置
const testConfig = {
  scenariosPath: './frontend/scenarios',
  expectedScenarios: [
    'bank-statement-download.html',
    'invoice-generation.html', 
    'asset-verification.html',
    'tax-reminder.html',
    'receivables-reconciliation.html',
    'vat-invoice-authentication.html',
    'payroll-calculation.html',
    'financial-report-consolidation.html'
  ],
  requiredElements: {
    1: ['login-form', 'query-form', 'download-btn'],
    2: ['excel-upload', 'invoice-form', 'batch-process'],
    3: ['asset-query', 'data-compare', 'excel-update'],
    4: ['tax-login', 'calendar-scrape', 'status-check'],
    5: ['data-integration', 'matching', 'reconciliation'],
    6: ['csv-read', 'batch-select', 'authentication'],
    7: ['data-merge', 'calculation', 'file-generation'],
    8: ['multi-login', 'data-extract', 'consolidation']
  }
};

// 测试结果收集
const testResults = {
  passed: 0,
  failed: 0,
  details: []
};

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * 读取HTML文件内容
 */
function readHTMLFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return content;
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 检查HTML文件中的关键元素
 */
function checkRequiredElements(content, scenarioId, fileName) {
  const requiredElements = testConfig.requiredElements[scenarioId];
  if (!requiredElements) {
    return { valid: true, message: '未定义必需元素检查' };
  }

  const missingElements = [];
  
  // 检查每个必需元素是否在HTML中存在（通过ID或类名）
  requiredElements.forEach(element => {
    const hasId = content.includes(`id="${element}"`);
    const hasClass = content.includes(`class="${element}"`);
    const hasDataAttr = content.includes(`data-step="${element}"`);
    const hasComment = content.includes(`<!-- ${element} -->`);
    
    if (!hasId && !hasClass && !hasDataAttr && !hasComment) {
      missingElements.push(element);
    }
  });

  if (missingElements.length > 0) {
    return {
      valid: false,
      message: `缺少必需元素: ${missingElements.join(', ')}`
    };
  }

  return { valid: true, message: '所有必需元素检查通过' };
}

/**
 * 检查JavaScript功能
 */
function checkJavaScriptFunctionality(content, scenarioId) {
  const checks = {
    hasEventListeners: content.includes('addEventListener'),
    hasValidationLogic: content.includes('validate') || content.includes('submitValidation'),
    hasProgressTracking: content.includes('progress') || content.includes('completedSteps'),
    hasAPICall: content.includes('fetch(') || content.includes('XMLHttpRequest'),
    hasErrorHandling: content.includes('try') && content.includes('catch')
  };

  const issues = [];
  
  if (!checks.hasEventListeners) issues.push('缺少事件监听器');
  if (!checks.hasValidationLogic) issues.push('缺少验证逻辑');
  if (!checks.hasProgressTracking) issues.push('缺少进度跟踪');
  if (!checks.hasAPICall) issues.push('缺少API调用');
  if (!checks.hasErrorHandling) issues.push('缺少错误处理');

  return {
    valid: issues.length === 0,
    message: issues.length > 0 ? `JavaScript问题: ${issues.join(', ')}` : 'JavaScript功能检查通过',
    details: checks
  };
}

/**
 * 检查CSS样式
 */
function checkCSSStyles(content) {
  const checks = {
    hasResponsiveDesign: content.includes('@media') || content.includes('grid') || content.includes('flex'),
    hasTransitions: content.includes('transition'),
    hasGradient: content.includes('gradient'),
    hasBoxShadow: content.includes('box-shadow'),
    hasHoverEffects: content.includes(':hover')
  };

  const score = Object.values(checks).filter(Boolean).length;
  
  return {
    valid: score >= 3,
    message: `CSS样式检查 (${score}/5): ${score >= 3 ? '通过' : '需要改进'}`,
    details: checks
  };
}

/**
 * 测试单个场景
 */
function testScenario(fileName, scenarioId) {
  const filePath = path.join(testConfig.scenariosPath, fileName);
  
  console.log(`\n测试场景${scenarioId}: ${fileName}`);
  console.log('='.repeat(50));
  
  // 检查文件存在
  if (!checkFileExists(filePath)) {
    const result = {
      scenarioId,
      fileName,
      passed: false,
      error: '文件不存在'
    };
    testResults.details.push(result);
    testResults.failed++;
    console.log('❌ 文件不存在');
    return result;
  }

  // 读取文件内容
  const content = readHTMLFile(filePath);
  if (!content) {
    const result = {
      scenarioId,
      fileName,
      passed: false,
      error: '文件读取失败'
    };
    testResults.details.push(result);
    testResults.failed++;
    console.log('❌ 文件读取失败');
    return result;
  }

  // 执行各项检查
  const elementCheck = checkRequiredElements(content, scenarioId, fileName);
  const jsCheck = checkJavaScriptFunctionality(content, scenarioId);
  const cssCheck = checkCSSStyles(content);

  console.log(`📋 元素检查: ${elementCheck.valid ? '✅' : '❌'} ${elementCheck.message}`);
  console.log(`🔧 JavaScript: ${jsCheck.valid ? '✅' : '❌'} ${jsCheck.message}`);
  console.log(`🎨 CSS样式: ${cssCheck.valid ? '✅' : '❌'} ${cssCheck.message}`);

  const passed = elementCheck.valid && jsCheck.valid && cssCheck.valid;
  
  const result = {
    scenarioId,
    fileName,
    passed,
    checks: {
      elements: elementCheck,
      javascript: jsCheck,
      css: cssCheck
    },
    contentLength: content.length
  };

  testResults.details.push(result);
  
  if (passed) {
    testResults.passed++;
    console.log('🎉 场景测试通过');
  } else {
    testResults.failed++;
    console.log('⚠️ 场景测试失败');
  }

  return result;
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  console.log('\n\n📊 测试报告');
  console.log('='.repeat(60));
  console.log(`总计: ${testResults.passed + testResults.failed} 个场景`);
  console.log(`通过: ${testResults.passed} 个`);
  console.log(`失败: ${testResults.failed} 个`);
  console.log(`成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  console.log('\n📋 详细结果:');
  testResults.details.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} 场景${result.scenarioId}: ${result.fileName}`);
    
    if (!result.passed && result.error) {
      console.log(`   错误: ${result.error}`);
    }
    
    if (result.checks) {
      console.log(`   - 元素: ${result.checks.elements.valid ? '✅' : '❌'}`);
      console.log(`   - JS功能: ${result.checks.javascript.valid ? '✅' : '❌'}`);
      console.log(`   - CSS样式: ${result.checks.css.valid ? '✅' : '❌'}`);
    }
    
    if (result.contentLength) {
      console.log(`   - 文件大小: ${(result.contentLength / 1024).toFixed(1)}KB`);
    }
  });

  // 生成改进建议
  console.log('\n💡 改进建议:');
  const failedScenarios = testResults.details.filter(r => !r.passed);
  
  if (failedScenarios.length === 0) {
    console.log('🎉 所有场景都通过了测试！');
  } else {
    failedScenarios.forEach(scenario => {
      console.log(`\n场景${scenario.scenarioId} (${scenario.fileName}):`);
      
      if (scenario.checks) {
        if (!scenario.checks.elements.valid) {
          console.log(`  - 添加缺失的UI元素: ${scenario.checks.elements.message}`);
        }
        if (!scenario.checks.javascript.valid) {
          console.log(`  - 完善JavaScript功能: ${scenario.checks.javascript.message}`);
        }
        if (!scenario.checks.css.valid) {
          console.log(`  - 改进CSS样式: ${scenario.checks.css.message}`);
        }
      }
    });
  }

  return testResults;
}

/**
 * 主测试函数
 */
function runTests() {
  console.log('🧪 开始场景代码质量测试');
  console.log('测试时间:', new Date().toLocaleString());
  
  // 测试每个场景
  testConfig.expectedScenarios.forEach((fileName, index) => {
    const scenarioId = index + 1;
    testScenario(fileName, scenarioId);
  });

  // 生成测试报告
  const results = generateTestReport();
  
  // 返回测试结果
  return results;
}

// 如果直接执行此脚本
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testScenario,
  generateTestReport
};