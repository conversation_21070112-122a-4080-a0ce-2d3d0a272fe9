/**
 * 场景验证路由
 * 处理学生任务完成的验证和评分
 */

const express = require('express');
// 动态导入数据库配置
let dbConfig;
try {
  if (process.env.TCB_ENV_ID && process.env.TCB_SECRET_ID && process.env.TCB_SECRET_KEY) {
    dbConfig = require('../../cloudbase/database/db');
  } else {
    dbConfig = require('../../cloudbase/database/db-mock');
  }
} catch (error) {
  dbConfig = require('../../cloudbase/database/db-mock');
}
const { db, COLLECTIONS } = dbConfig;
const { authMiddleware, studentOnlyMiddleware } = require('../middleware/auth');

const router = express.Router();

/**
 * 验证场景完成情况
 * POST /api/validate/scenario/:id
 */
router.post('/scenario/:id', authMiddleware, studentOnlyMiddleware, async (req, res) => {
  try {
    const scenarioId = parseInt(req.params.id);
    const userId = req.user.userId;
    const { validation_data } = req.body;

    // 获取场景信息
    const scenarioResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        id: scenarioId,
        is_active: true
      })
      .get();

    if (!scenarioResult.data.length) {
      return res.status(404).json({
        success: false,
        message: '场景不存在'
      });
    }

    const scenario = scenarioResult.data[0];
    
    // 执行场景特定的验证逻辑
    const validationResult = await validateScenario(scenarioId, validation_data, scenario.validation_rules);

    if (!validationResult.valid) {
      return res.status(400).json({
        success: false,
        message: '验证失败：' + validationResult.message,
        data: validationResult
      });
    }

    // 更新学生学习记录
    await updateStudentProgress(userId, scenarioId, validationResult);

    // 记录提交信息
    await recordSubmission(userId, scenarioId, validation_data, validationResult);

    res.json({
      success: true,
      message: '场景验证成功！',
      data: {
        score: validationResult.score,
        feedback: validationResult.feedback,
        completion_time: new Date().toISOString(),
        next_scenario: getNextScenario(scenarioId)
      }
    });

    console.log(`学生 ${req.user.username} 完成场景${scenarioId}，得分：${validationResult.score}`);

  } catch (error) {
    console.error('场景验证失败:', error);
    res.status(500).json({
      success: false,
      message: '验证失败，请稍后重试'
    });
  }
});

/**
 * 获取学生所有场景的完成状态
 * GET /api/validate/progress
 */
router.get('/progress', authMiddleware, studentOnlyMiddleware, async (req, res) => {
  try {
    const userId = req.user.userId;

    // 获取学生的所有学习记录
    const progressResult = await db
      .collection(COLLECTIONS.PROGRESS)
      .where({
        student_id: userId
      })
      .get();

    // 获取所有场景
    const scenariosResult = await db
      .collection(COLLECTIONS.SCENARIOS)
      .where({
        is_active: true
      })
      .orderBy('id', 'asc')
      .get();

    const scenarios = scenariosResult.data;
    const progressMap = {};
    
    progressResult.data.forEach(p => {
      progressMap[p.scenario_id] = p;
    });

    // 计算总体进度
    const totalScenarios = scenarios.length;
    const completedScenarios = progressResult.data.filter(p => p.status === 'completed').length;
    const averageScore = progressResult.data
      .filter(p => p.score !== null)
      .reduce((sum, p, _, arr) => sum + p.score / arr.length, 0);

    const progressSummary = {
      total_scenarios: totalScenarios,
      completed_scenarios: completedScenarios,
      completion_rate: (completedScenarios / totalScenarios * 100).toFixed(1),
      average_score: averageScore.toFixed(1),
      total_time_spent: progressResult.data.reduce((sum, p) => sum + (p.time_spent || 0), 0),
      scenarios: scenarios.map(s => ({
        ...s,
        progress: progressMap[s.id] || {
          status: 'not_started',
          progress: 0,
          score: null
        }
      }))
    };

    res.json({
      success: true,
      data: progressSummary
    });

  } catch (error) {
    console.error('获取学习进度失败:', error);
    res.status(500).json({
      success: false,
      message: '获取学习进度失败'
    });
  }
});

/**
 * 场景特定的验证逻辑
 */
async function validateScenario(scenarioId, validationData, validationRules) {
  switch (scenarioId) {
    case 1: // 企业网银流水查询下载
      return validateBankStatementDownload(validationData, validationRules);
    
    case 2: // 批量开具电子发票
      return validateBatchInvoiceCreation(validationData, validationRules);
    
    // 其他场景的验证逻辑...
    default:
      return {
        valid: true,
        score: 85,
        feedback: '场景完成',
        details: {}
      };
  }
}

/**
 * 场景1：企业网银流水查询下载验证
 */
function validateBankStatementDownload(data, rules) {
  const result = {
    valid: false,
    score: 0,
    feedback: '',
    details: {}
  };

  let totalScore = 0;
  const scoreRules = rules.score_rules || { login: 20, query: 30, download: 50 };

  // 验证登录步骤
  if (data.login_completed) {
    totalScore += scoreRules.login;
    result.details.login = '✅ 登录步骤完成';
  } else {
    result.details.login = '❌ 登录步骤未完成';
  }

  // 验证查询步骤
  if (data.query_completed) {
    totalScore += scoreRules.query;
    result.details.query = '✅ 查询条件设置正确';
  } else {
    result.details.query = '❌ 查询步骤未完成';
  }

  // 验证下载步骤
  if (data.export_completed) {
    totalScore += scoreRules.download;
    result.details.download = '✅ Excel文件下载成功';
  } else {
    result.details.download = '❌ 文件下载未完成';
  }

  // 验证步骤完整性
  if (data.steps_completed >= 4) {
    result.details.completion = '✅ 所有步骤已完成';
  } else {
    result.details.completion = `❌ 仅完成${data.steps_completed}/4个步骤`;
  }

  result.score = totalScore;
  result.valid = totalScore >= 60; // 60分及格

  if (result.valid) {
    result.feedback = `恭喜完成场景1！您掌握了企业网银流水查询下载的基本操作流程。得分：${totalScore}分`;
  } else {
    result.feedback = `场景1未完全完成，请检查各个操作步骤。当前得分：${totalScore}分`;
  }

  return result;
}

/**
 * 场景2：批量开具电子发票验证
 */
function validateBatchInvoiceCreation(data, rules) {
  const result = {
    valid: false,
    score: 0,
    feedback: '',
    details: {}
  };

  let totalScore = 0;
  const scoreRules = rules.score_rules || { upload: 20, process: 40, complete: 40 };

  // 验证文件上传步骤
  if (data.file_uploaded) {
    totalScore += scoreRules.upload;
    result.details.upload = '✅ Excel文件上传成功';
  } else {
    result.details.upload = '❌ Excel文件上传未完成';
  }

  // 验证数据处理步骤
  if (data.data_processed) {
    totalScore += scoreRules.process;
    result.details.process = '✅ 订单数据解析成功';
  } else {
    result.details.process = '❌ 数据处理步骤未完成';
  }

  // 验证批量开票步骤
  if (data.batch_invoicing_completed) {
    totalScore += scoreRules.complete;
    result.details.invoicing = '✅ 批量开票处理完成';
  } else {
    result.details.invoicing = '❌ 批量开票未完成';
  }

  // 验证订单数量
  if (data.orders_count && data.orders_count >= 5) {
    result.details.quantity = `✅ 成功处理${data.orders_count}条订单`;
  } else {
    result.details.quantity = '❌ 处理订单数量不足';
  }

  // 验证步骤完整性
  if (data.steps_completed >= 4) {
    result.details.completion = '✅ 所有步骤已完成';
  } else {
    result.details.completion = `❌ 仅完成${data.steps_completed}/4个步骤`;
  }

  result.score = totalScore;
  result.valid = totalScore >= 60; // 60分及格

  if (result.valid) {
    result.feedback = `恭喜完成场景2！您掌握了批量开具电子发票的自动化流程。得分：${totalScore}分`;
  } else {
    result.feedback = `场景2未完全完成，请检查各个操作步骤。当前得分：${totalScore}分`;
  }

  return result;
}

/**
 * 更新学生学习进度
 */
async function updateStudentProgress(studentId, scenarioId, validationResult) {
  const progressData = {
    student_id: studentId,
    scenario_id: scenarioId,
    status: validationResult.valid ? 'completed' : 'in_progress',
    progress: validationResult.valid ? 100 : 80,
    score: validationResult.score,
    completed_at: validationResult.valid ? new Date() : null,
    last_access_time: new Date(),
    updated_at: new Date()
  };

  // 查找现有记录
  const existingResult = await db
    .collection(COLLECTIONS.PROGRESS)
    .where({
      student_id: studentId,
      scenario_id: scenarioId
    })
    .get();

  if (existingResult.data.length > 0) {
    // 更新现有记录
    await db
      .collection(COLLECTIONS.PROGRESS)
      .doc(existingResult.data[0]._id)
      .update(progressData);
  } else {
    // 创建新记录
    await db
      .collection(COLLECTIONS.PROGRESS)
      .add({
        ...progressData,
        first_access_time: new Date(),
        created_at: new Date(),
        time_spent: 0
      });
  }
}

/**
 * 记录学生提交信息
 */
async function recordSubmission(studentId, scenarioId, validationData, validationResult) {
  const submissionData = {
    student_id: studentId,
    scenario_id: scenarioId,
    submission_content: JSON.stringify(validationData),
    submission_time: new Date(),
    status: 'graded',
    score: validationResult.score,
    feedback: validationResult.feedback,
    graded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  };

  await db.collection(COLLECTIONS.SUBMISSIONS).add(submissionData);
}

/**
 * 获取下一个建议学习的场景
 */
function getNextScenario(currentScenarioId) {
  const nextId = currentScenarioId + 1;
  if (nextId <= 12) {
    return {
      id: nextId,
      suggestion: `建议继续学习场景${nextId}`
    };
  }
  return null;
}

module.exports = router;