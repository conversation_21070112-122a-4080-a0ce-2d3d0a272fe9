/**
 * OCR适配器管理器
 * 统一管理所有OCR厂商适配器，提供统一的调用接口
 */

const DoubaoOCRAdapter = require('./ocr-adapters/doubao-adapter');
const BaiduOCRAdapter = require('./ocr-adapters/baidu-adapter');
const TencentOCRAdapter = require('./ocr-adapters/tencent-adapter');
const AliyunOCRAdapter = require('./ocr-adapters/aliyun-adapter');
const MockOCRAdapter = require('./ocr-adapters/mock-adapter');

class OCRAdapterManager {
    constructor() {
        this.adapters = new Map();
        this.initializeAdapters();
        
        console.log('🔄 OCR适配器管理器初始化完成');
    }

    /**
     * 初始化所有适配器
     */
    initializeAdapters() {
        // 初始化各厂商适配器
        const adapterClasses = [
            DoubaoOCRAdapter,
            BaiduOCRAdapter, 
            TencentOCRAdapter,
            AliyunOCRAdapter,
            MockOCRAdapter // Mock适配器始终最后，兜底方案
        ];

        adapterClasses.forEach(AdapterClass => {
            const adapter = new AdapterClass();
            this.adapters.set(adapter.name, adapter);
            
            const status = adapter.isAvailable() ? '✅ 可用' : '⚠️  未配置';
            console.log(`📦 ${adapter.name.toUpperCase()} OCR适配器: ${status} (优先级: ${adapter.priority})`);
        });
    }

    /**
     * 获取按优先级排序的可用适配器
     */
    getAvailableAdapters() {
        return Array.from(this.adapters.values())
            .filter(adapter => adapter.enabled)
            .sort((a, b) => a.priority - b.priority);
    }

    /**
     * 获取最佳可用适配器
     */
    getBestAdapter() {
        const availableAdapters = this.getAvailableAdapters();
        
        // 优先返回已配置且可用的适配器
        const configuredAdapter = availableAdapters.find(adapter => adapter.isAvailable());
        if (configuredAdapter) {
            return configuredAdapter;
        }

        // 如果没有配置的适配器，返回Mock适配器
        const mockAdapter = availableAdapters.find(adapter => adapter.name === 'mock');
        return mockAdapter;
    }

    /**
     * 根据名称获取特定适配器
     */
    getAdapter(adapterName) {
        return this.adapters.get(adapterName);
    }

    /**
     * 发票OCR识别 - 自动选择最佳适配器
     */
    async recognizeInvoice(imageData, filename = '', preferredAdapter = null) {
        const startTime = Date.now();
        
        try {
            // 如果指定了首选适配器，先尝试使用
            if (preferredAdapter) {
                const adapter = this.getAdapter(preferredAdapter);
                if (adapter && adapter.isAvailable()) {
                    console.log(`🎯 使用指定适配器: ${preferredAdapter.toUpperCase()}`);
                    const result = await adapter.recognizeInvoice(imageData, filename);
                    if (result.success) {
                        this.logPerformance(preferredAdapter, startTime, true);
                        return result;
                    }
                }
            }

            // 按优先级尝试所有可用适配器
            const availableAdapters = this.getAvailableAdapters();
            let lastError = null;

            for (const adapter of availableAdapters) {
                if (!adapter.isAvailable()) continue;

                try {
                    console.log(`🔄 尝试 ${adapter.name.toUpperCase()} OCR适配器`);
                    const result = await adapter.recognizeInvoice(imageData, filename);
                    
                    if (result.success) {
                        this.logPerformance(adapter.name, startTime, true);
                        return result;
                    }
                } catch (error) {
                    lastError = error;
                    console.warn(`⚠️  ${adapter.name.toUpperCase()} 适配器失败: ${error.message}`);
                    continue;
                }
            }

            // 所有适配器都失败，返回Mock结果
            console.log('🎭 所有配置的适配器都失败，使用Mock适配器');
            const mockAdapter = this.getAdapter('mock');
            const result = await mockAdapter.recognizeInvoice(imageData, filename);
            this.logPerformance('mock', startTime, false);
            return result;

        } catch (error) {
            console.error('❌ OCR识别完全失败:', error);
            this.logPerformance('error', startTime, false);
            
            // 返回错误结果
            return {
                success: false,
                filename: filename,
                error: error.message,
                source: 'adapter-manager',
                processedAt: new Date().toISOString()
            };
        }
    }

    /**
     * 通用OCR识别 - 自动选择最佳适配器
     */
    async recognizeGeneral(imageData, filename = '', preferredAdapter = null) {
        const startTime = Date.now();
        
        try {
            // 如果指定了首选适配器，先尝试使用
            if (preferredAdapter) {
                const adapter = this.getAdapter(preferredAdapter);
                if (adapter && adapter.isAvailable()) {
                    console.log(`🎯 使用指定适配器: ${preferredAdapter.toUpperCase()}`);
                    const result = await adapter.recognizeGeneral(imageData, filename);
                    if (result.success) {
                        this.logPerformance(preferredAdapter, startTime, true);
                        return result;
                    }
                }
            }

            // 按优先级尝试所有可用适配器
            const availableAdapters = this.getAvailableAdapters();
            let lastError = null;

            for (const adapter of availableAdapters) {
                if (!adapter.isAvailable()) continue;

                try {
                    console.log(`🔄 尝试 ${adapter.name.toUpperCase()} 通用OCR`);
                    const result = await adapter.recognizeGeneral(imageData, filename);
                    
                    if (result.success) {
                        this.logPerformance(adapter.name, startTime, true);
                        return result;
                    }
                } catch (error) {
                    lastError = error;
                    console.warn(`⚠️  ${adapter.name.toUpperCase()} 适配器失败: ${error.message}`);
                    continue;
                }
            }

            // 所有适配器都失败，使用Mock结果
            console.log('🎭 所有配置的适配器都失败，使用Mock适配器');
            const mockAdapter = this.getAdapter('mock');
            const result = await mockAdapter.recognizeGeneral(imageData, filename);
            this.logPerformance('mock', startTime, false);
            return result;

        } catch (error) {
            console.error('❌ 通用OCR识别完全失败:', error);
            this.logPerformance('error', startTime, false);
            
            return {
                success: false,
                filename: filename,
                error: error.message,
                source: 'adapter-manager',
                processedAt: new Date().toISOString()
            };
        }
    }

    /**
     * 批量识别发票
     */
    async batchRecognizeInvoices(imageDataList, preferredAdapter = null) {
        console.log(`📄 开始批量识别 ${imageDataList.length} 张发票`);
        
        const results = [];
        const startTime = Date.now();
        
        for (let i = 0; i < imageDataList.length; i++) {
            const { data, filename } = imageDataList[i];
            
            try {
                const result = await this.recognizeInvoice(data, filename, preferredAdapter);
                results.push(result);
                
                // 添加延时，避免API限流
                if (i < imageDataList.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
            } catch (error) {
                console.error(`❌ 批量识别失败 (${filename}):`, error.message);
                results.push({
                    success: false,
                    filename: filename,
                    error: error.message
                });
            }
        }
        
        const totalTime = Date.now() - startTime;
        const successCount = results.filter(r => r.success).length;
        
        console.log(`✅ 批量识别完成: ${successCount}/${imageDataList.length} 成功, 用时: ${totalTime}ms`);
        return results;
    }

    /**
     * 获取所有适配器状态信息
     */
    getAdaptersStatus() {
        const adapters = Array.from(this.adapters.values()).map(adapter => {
            const info = adapter.getInfo();
            return {
                name: adapter.name,
                displayName: this.getDisplayName(adapter.name),
                ...info,
                configured: adapter.isAvailable(),
                description: this.getAdapterDescription(adapter.name)
            };
        });

        const bestAdapter = this.getBestAdapter();
        
        return {
            adapters: adapters.sort((a, b) => a.priority - b.priority),
            currentBest: bestAdapter ? bestAdapter.name : 'none',
            summary: {
                total: adapters.length,
                configured: adapters.filter(a => a.configured).length,
                enabled: adapters.filter(a => a.enabled).length
            }
        };
    }

    /**
     * 获取适配器显示名称
     */
    getDisplayName(adapterName) {
        const displayNames = {
            'doubao': '豆包OCR',
            'baidu': '百度智能云OCR',
            'tencent': '腾讯云OCR',
            'aliyun': '阿里云OCR',
            'mock': 'Mock演示模式'
        };
        return displayNames[adapterName] || adapterName.toUpperCase();
    }

    /**
     * 获取适配器描述
     */
    getAdapterDescription(adapterName) {
        const descriptions = {
            'doubao': '基于火山引擎豆包大模型，智能理解发票内容',
            'baidu': '百度智能云OCR，专业发票识别服务',
            'tencent': '腾讯云OCR，稳定可靠的文字识别',
            'aliyun': '阿里云OCR，企业级文字识别服务',
            'mock': '演示模式，无需API配置即可体验完整功能'
        };
        return descriptions[adapterName] || '第三方OCR服务';
    }

    /**
     * 记录性能指标
     */
    logPerformance(adapterName, startTime, success) {
        const duration = Date.now() - startTime;
        const status = success ? '✅' : '❌';
        console.log(`${status} ${adapterName.toUpperCase()} OCR耗时: ${duration}ms`);
    }

    /**
     * 动态添加适配器
     */
    addAdapter(adapter) {
        if (!adapter.name) {
            throw new Error('适配器必须有name属性');
        }
        
        this.adapters.set(adapter.name, adapter);
        console.log(`📦 动态添加适配器: ${adapter.name.toUpperCase()}`);
    }

    /**
     * 禁用/启用适配器
     */
    setAdapterEnabled(adapterName, enabled) {
        const adapter = this.getAdapter(adapterName);
        if (adapter) {
            adapter.enabled = enabled;
            const status = enabled ? '启用' : '禁用';
            console.log(`🔧 ${status}适配器: ${adapterName.toUpperCase()}`);
        }
    }

    /**
     * 验证OCR结果
     */
    validateOCRResult(ocrResult) {
        if (!ocrResult || !ocrResult.data) {
            return { isValid: false, errors: ['识别结果为空'] };
        }

        const { data } = ocrResult;
        const errors = [];

        // 基础字段检查
        const requiredFields = ['invoiceNumber', 'date', 'sellerName', 'totalAmount'];
        requiredFields.forEach(field => {
            if (!data[field] || data[field] === '') {
                errors.push(`缺少${field}字段`);
            }
        });

        // 金额合理性检查
        if (data.totalAmount <= 0) {
            errors.push('发票金额必须大于零');
        }

        // 金额计算检查
        const calculatedTotal = (data.amountWithoutTax || 0) + (data.taxAmount || 0);
        if (data.totalAmount && calculatedTotal > 0) {
            const diff = Math.abs(data.totalAmount - calculatedTotal);
            if (diff > 0.01) {
                errors.push('金额计算不匹配');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            confidence: ocrResult.confidence || 0
        };
    }
}

// 导出单例
module.exports = new OCRAdapterManager();