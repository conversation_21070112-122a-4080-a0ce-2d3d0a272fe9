<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会计凭证批量录入 - RPA实训场景9</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .scenario-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .finance-system {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .left-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .right-panel {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #7673FF;
        }
        
        .upload-area {
            border: 2px dashed #7673FF;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #5a5bff;
            background: #f0f1ff;
        }
        
        .upload-area.dragover {
            border-color: #5a5bff;
            background: #e6e7ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #7673FF;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #7673FF 0%, #9C69FF 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(118,115,255,0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40,167,69,0.4);
        }
        
        .file-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .file-info.show {
            display: block;
        }
        
        .voucher-preview {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        
        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .voucher-table th,
        .voucher-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        .voucher-table th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .voucher-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .processing-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .processing-status.show {
            display: block;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #7673FF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-number {
            font-size: 24px;
            font-weight: bold;
            color: #7673FF;
        }
        
        .status-label {
            color: #666;
            font-size: 14px;
        }
        
        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            display: none;
        }
        
        .success-message.show {
            display: block;
        }
        
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }
        
        .template-downloads {
            margin-bottom: 20px;
        }
        
        .template-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .template-link {
            background: #e9ecef;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            color: #495057;
            font-size: 13px;
            transition: all 0.3s ease;
        }
        
        .template-link:hover {
            background: #7673FF;
            color: white;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #7673FF 0%, #9C69FF 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .finance-system {
                grid-template-columns: 1fr;
            }
            
            .scenario-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="student-dashboard.html" class="back-button">← 返回场景列表</a>
    
    <div class="container">
        <div class="header">
            <h1>🧾 会计凭证批量录入系统</h1>
            <p style="color: #666; margin-top: 10px;">模拟财务系统 - 凭证管理模块</p>
            <div class="scenario-info">
                <div class="info-item">
                    <span>📊</span>
                    <span>模块：第二模块 - 流程整合</span>
                </div>
                <div class="info-item">
                    <span>⏱️</span>
                    <span>预计用时：25分钟</span>
                </div>
                <div class="info-item">
                    <span>🎯</span>
                    <span>难度：中级</span>
                </div>
                <div class="info-item">
                    <span>💼</span>
                    <span>场景9</span>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="finance-system">
                <div class="left-panel">
                    <div class="panel-title">📂 数据上传区</div>
                    
                    <div class="template-downloads">
                        <h4 style="margin-bottom: 10px;">📄 Excel模板下载：</h4>
                        <div class="template-links">
                            <a href="#" class="template-link" onclick="downloadTemplate('sales')">销售收入模板</a>
                            <a href="#" class="template-link" onclick="downloadTemplate('purchase')">采购支出模板</a>
                            <a href="#" class="template-link" onclick="downloadTemplate('expense')">费用报销模板</a>
                        </div>
                    </div>
                    
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📤</div>
                        <h3>拖拽Excel文件到此处</h3>
                        <p style="margin: 10px 0; color: #666;">或点击选择文件</p>
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            选择Excel文件
                        </button>
                        <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" onchange="handleFileSelect(event)">
                    </div>
                    
                    <div id="fileInfo" class="file-info">
                        <h4>📁 文件信息</h4>
                        <p id="fileName"></p>
                        <p id="fileSize"></p>
                        <p id="dataCount"></p>
                    </div>
                    
                    <div id="processingStatus" class="processing-status">
                        <h4>⚙️ 处理状态</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="processingText">准备处理数据...</p>
                    </div>
                </div>
                
                <div class="right-panel">
                    <div class="panel-title">📋 凭证预览</div>
                    
                    <div id="voucherPreview" class="voucher-preview">
                        <p style="text-align: center; color: #666; margin: 50px 0;">
                            请上传Excel文件以预览生成的凭证
                        </p>
                    </div>
                    
                    <div style="margin-top: 20px; text-align: center;">
                        <button id="submitBtn" class="btn btn-success" style="display: none;" onclick="submitVouchers()">
                            📝 批量提交凭证
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="statusGrid" class="status-grid" style="display: none;">
                <div class="status-card">
                    <div class="status-number" id="totalCount">0</div>
                    <div class="status-label">总凭证数</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="validCount">0</div>
                    <div class="status-label">验证通过</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="errorCount">0</div>
                    <div class="status-label">存在错误</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="balanceCheck">✓</div>
                    <div class="status-label">借贷平衡</div>
                </div>
            </div>
            
            <div id="successMessage" class="success-message">
                <h4>✅ 凭证提交成功！</h4>
                <p id="successDetails"></p>
            </div>
            
            <div id="errorMessage" class="error-message">
                <h4>❌ 处理失败</h4>
                <p id="errorDetails"></p>
            </div>
        </div>
    </div>

    <script>
        let uploadedData = null;
        let generatedVouchers = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupDragAndDrop();
            console.log('场景9：会计凭证批量录入 - 页面已加载');
        });

        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight(e) {
                uploadArea.classList.add('dragover');
            }

            function unhighlight(e) {
                uploadArea.classList.remove('dragover');
            }

            uploadArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            handleFiles(event.target.files);
        }

        // 处理文件
        function handleFiles(files) {
            if (files.length === 0) return;
            
            const file = files[0];
            if (!file.name.match(/\.(xlsx|xls)$/)) {
                showError('请选择Excel文件（.xlsx或.xls格式）');
                return;
            }

            displayFileInfo(file);
            processFile(file);
        }

        // 显示文件信息
        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = `文件名：${file.name}`;
            document.getElementById('fileSize').textContent = `文件大小：${(file.size / 1024).toFixed(1)} KB`;
            document.getElementById('fileInfo').classList.add('show');
        }

        // 处理文件
        function processFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // 模拟读取Excel数据
                    const data = simulateExcelParsing(file.name);
                    uploadedData = data;
                    
                    document.getElementById('dataCount').textContent = `数据行数：${data.length} 条`;
                    
                    showProcessingStatus();
                    generateVouchers(data);
                } catch (error) {
                    showError('文件解析失败：' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        // 模拟Excel数据解析
        function simulateExcelParsing(fileName) {
            // 根据文件名判断数据类型
            let dataType = 'sales';
            if (fileName.includes('采购') || fileName.includes('purchase')) {
                dataType = 'purchase';
            } else if (fileName.includes('费用') || fileName.includes('expense')) {
                dataType = 'expense';
            }

            // 生成模拟数据
            const mockData = [];
            const recordCount = Math.floor(Math.random() * 8) + 3; // 3-10条记录

            for (let i = 0; i < recordCount; i++) {
                if (dataType === 'sales') {
                    mockData.push({
                        type: 'sales',
                        date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
                        customer: `客户${String.fromCharCode(65 + i)}`,
                        amount: (Math.random() * 50000 + 5000).toFixed(2),
                        taxRate: 0.13,
                        description: `销售商品${i + 1}`
                    });
                } else if (dataType === 'purchase') {
                    mockData.push({
                        type: 'purchase',
                        date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
                        supplier: `供应商${String.fromCharCode(65 + i)}`,
                        amount: (Math.random() * 30000 + 3000).toFixed(2),
                        taxRate: 0.13,
                        description: `采购原材料${i + 1}`
                    });
                } else {
                    mockData.push({
                        type: 'expense',
                        date: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
                        department: `部门${String.fromCharCode(65 + i)}`,
                        amount: (Math.random() * 5000 + 500).toFixed(2),
                        category: ['差旅费', '办公费', '通讯费', '培训费'][Math.floor(Math.random() * 4)],
                        description: `${['差旅费', '办公费', '通讯费', '培训费'][Math.floor(Math.random() * 4)]}报销${i + 1}`
                    });
                }
            }

            return mockData;
        }

        // 显示处理状态
        function showProcessingStatus() {
            document.getElementById('processingStatus').classList.add('show');
            updateProgress(0, '开始解析数据...');
            
            setTimeout(() => updateProgress(30, '生成会计分录...'), 500);
            setTimeout(() => updateProgress(60, '验证借贷平衡...'), 1000);
            setTimeout(() => updateProgress(90, '检查科目编码...'), 1500);
            setTimeout(() => updateProgress(100, '处理完成！'), 2000);
        }

        // 更新进度
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('processingText').textContent = text;
        }

        // 生成凭证
        function generateVouchers(data) {
            setTimeout(() => {
                generatedVouchers = [];
                
                data.forEach((record, index) => {
                    const voucher = generateSingleVoucher(record, index + 1);
                    generatedVouchers.push(voucher);
                });

                displayVoucherPreview();
                updateStatusGrid();
                document.getElementById('submitBtn').style.display = 'inline-block';
            }, 2500);
        }

        // 生成单个凭证
        function generateSingleVoucher(record, voucherNo) {
            const voucher = {
                voucherNo: `记${new Date().getFullYear()}${String(voucherNo).padStart(4, '0')}`,
                date: record.date,
                description: record.description,
                entries: []
            };

            if (record.type === 'sales') {
                // 销售收入分录
                const taxAmount = (parseFloat(record.amount) * record.taxRate).toFixed(2);
                const netAmount = (parseFloat(record.amount) - parseFloat(taxAmount)).toFixed(2);
                
                voucher.entries = [
                    { subject: '1122 应收账款', subjectName: '应收账款', debit: record.amount, credit: '0.00' },
                    { subject: '6001 主营业务收入', subjectName: '主营业务收入', debit: '0.00', credit: netAmount },
                    { subject: '2221 应交增值税-销项税额', subjectName: '应交税费-应交增值税(销项税额)', debit: '0.00', credit: taxAmount }
                ];
            } else if (record.type === 'purchase') {
                // 采购支出分录
                const taxAmount = (parseFloat(record.amount) * record.taxRate).toFixed(2);
                const netAmount = (parseFloat(record.amount) - parseFloat(taxAmount)).toFixed(2);
                
                voucher.entries = [
                    { subject: '1405 原材料', subjectName: '原材料', debit: netAmount, credit: '0.00' },
                    { subject: '2221 应交增值税-进项税额', subjectName: '应交税费-应交增值税(进项税额)', debit: taxAmount, credit: '0.00' },
                    { subject: '2202 应付账款', subjectName: '应付账款', debit: '0.00', credit: record.amount }
                ];
            } else {
                // 费用报销分录
                voucher.entries = [
                    { subject: '6602 管理费用', subjectName: '管理费用', debit: record.amount, credit: '0.00' },
                    { subject: '1001 库存现金', subjectName: '库存现金', debit: '0.00', credit: record.amount }
                ];
            }

            return voucher;
        }

        // 显示凭证预览
        function displayVoucherPreview() {
            const previewContainer = document.getElementById('voucherPreview');
            let html = '<table class="voucher-table"><thead><tr>';
            html += '<th>凭证号</th><th>日期</th><th>科目编码</th><th>科目名称</th><th>借方金额</th><th>贷方金额</th><th>摘要</th>';
            html += '</tr></thead><tbody>';

            generatedVouchers.forEach(voucher => {
                voucher.entries.forEach((entry, index) => {
                    html += '<tr>';
                    if (index === 0) {
                        html += `<td rowspan="${voucher.entries.length}">${voucher.voucherNo}</td>`;
                        html += `<td rowspan="${voucher.entries.length}">${voucher.date}</td>`;
                    }
                    html += `<td>${entry.subject.split(' ')[0]}</td>`;
                    html += `<td>${entry.subjectName}</td>`;
                    html += `<td style="text-align: right; color: ${entry.debit !== '0.00' ? '#d73027' : '#666'}">${entry.debit}</td>`;
                    html += `<td style="text-align: right; color: ${entry.credit !== '0.00' ? '#1a9850' : '#666'}">${entry.credit}</td>`;
                    if (index === 0) {
                        html += `<td rowspan="${voucher.entries.length}">${voucher.description}</td>`;
                    }
                    html += '</tr>';
                });
            });

            html += '</tbody></table>';
            previewContainer.innerHTML = html;
        }

        // 更新状态网格
        function updateStatusGrid() {
            document.getElementById('statusGrid').style.display = 'grid';
            document.getElementById('totalCount').textContent = generatedVouchers.length;
            document.getElementById('validCount').textContent = generatedVouchers.length;
            document.getElementById('errorCount').textContent = '0';
            document.getElementById('balanceCheck').textContent = '✓';
        }

        // 提交凭证
        function submitVouchers() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.textContent = '提交中...';
            submitBtn.disabled = true;

            // 模拟提交过程
            setTimeout(() => {
                // 调用后端API验证场景完成
                fetch('/api/scenarios/9/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('token')
                    },
                    body: JSON.stringify({
                        action: 'submit_vouchers',
                        completedSteps: ['upload', 'parse', 'generate', 'submit'],
                        voucher_count: generatedVouchers.length,
                        total_amount: generatedVouchers.reduce((sum, v) => {
                            return sum + v.entries.reduce((entrySum, e) => {
                                return entrySum + parseFloat(e.debit);
                            }, 0);
                        }, 0),
                        balance_check: true,
                        data_source: uploadedData[0]?.type || 'unknown'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(`成功提交 ${generatedVouchers.length} 张凭证！所有凭证均通过借贷平衡验证。`);
                    } else {
                        showError('提交失败：' + data.message);
                    }
                })
                .catch(error => {
                    showError('网络错误：' + error.message);
                })
                .finally(() => {
                    submitBtn.textContent = '📝 批量提交凭证';
                    submitBtn.disabled = false;
                });
            }, 2000);
        }

        // 下载模板
        function downloadTemplate(type) {
            const templates = {
                sales: '销售收入模板.xlsx',
                purchase: '采购支出模板.xlsx',
                expense: '费用报销模板.xlsx'
            };

            // 模拟下载
            alert(`正在下载：${templates[type]}\n\n模板包含以下字段：\n- 日期\n- ${type === 'sales' ? '客户名称' : type === 'purchase' ? '供应商名称' : '部门'}\n- 金额\n- 税率\n- 摘要说明`);
        }

        // 显示成功消息
        function showSuccess(message) {
            document.getElementById('successDetails').textContent = message;
            document.getElementById('successMessage').classList.add('show');
            document.getElementById('errorMessage').classList.remove('show');
        }

        // 显示错误消息
        function showError(message) {
            document.getElementById('errorDetails').textContent = message;
            document.getElementById('errorMessage').classList.add('show');
            document.getElementById('successMessage').classList.remove('show');
        }
    </script>
</body>
</html>