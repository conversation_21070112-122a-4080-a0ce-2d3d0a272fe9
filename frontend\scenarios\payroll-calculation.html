<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景7：月度工资条数据计算与生成系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #8D99AE;
            font-size: 16px;
        }

        .workflow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .step-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
        }

        .step-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(118, 115, 255, 0.25);
        }

        .step-card.active {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.05);
        }

        .step-card.completed {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.05);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-card.completed .step-number {
            background: #27AE60;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .step-content {
            margin-bottom: 20px;
        }

        .file-upload-area {
            border: 2px dashed #7673FF;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            background: rgba(118, 115, 255, 0.05);
            margin: 15px 0;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #5C7BFF;
            background: rgba(118, 115, 255, 0.1);
        }

        .file-upload-area.dragover {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            color: #7673FF;
            margin-bottom: 15px;
        }

        .calculation-rules {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .rule-section {
            margin-bottom: 20px;
        }

        .rule-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .rule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(118, 115, 255, 0.05);
            border-radius: 6px;
            margin: 5px 0;
            font-size: 14px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
        }

        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
            font-size: 13px;
        }

        .data-table tr:hover {
            background: rgba(118, 115, 255, 0.05);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(118, 115, 255, 0.4);
        }

        .btn-success {
            background: #27AE60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #F39C12;
            color: white;
        }

        .btn-warning:hover {
            background: #E67E22;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #E9ECEF;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 11px;
            color: #8D99AE;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .payroll-preview {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }

        .employee-payslip {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }

        .payslip-header {
            text-align: center;
            border-bottom: 2px solid #7673FF;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .payslip-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .payslip-period {
            font-size: 14px;
            color: #8D99AE;
            margin-top: 5px;
        }

        .payslip-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .payslip-section {
            margin-bottom: 15px;
        }

        .section-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .payslip-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            font-size: 14px;
        }

        .payslip-total {
            border-top: 2px solid #7673FF;
            padding-top: 10px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 16px;
            color: #2c3e50;
        }

        .success-message {
            background: rgba(39, 174, 96, 0.1);
            border: 2px solid #27AE60;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .file-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(118, 115, 255, 0.05);
            border-radius: 6px;
            margin: 5px 0;
        }

        .file-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .file-size {
            color: #8D99AE;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>月度工资条数据计算与生成系统</h1>
            <p>整合考勤表和绩效表，根据规则计算薪酬，为每人生成独立工资条文件</p>
        </div>

        <div class="progress-indicator">
            <h3>任务进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            <p id="progressText">准备开始 - 请上传考勤数据</p>
        </div>

        <div class="workflow">
            <!-- 步骤1：上传考勤数据 -->
            <div class="step-card active" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">上传考勤数据</div>
                </div>
                <div class="step-content">
                    <div class="file-upload-area" id="attendanceUploadArea">
                        <div class="upload-icon">📊</div>
                        <p><strong>点击或拖拽上传考勤表Excel文件</strong></p>
                        <p>支持格式：.xlsx, .xls</p>
                        <p style="font-size: 12px; color: #8D99AE; margin-top: 10px;">
                            包含：员工姓名、工号、出勤天数、迟到次数、早退次数、请假天数
                        </p>
                        <input type="file" id="attendanceFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                    <button class="btn btn-primary" id="processAttendanceBtn" disabled>处理考勤数据</button>
                </div>
            </div>

            <!-- 步骤2：上传绩效数据 -->
            <div class="step-card" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">上传绩效数据</div>
                </div>
                <div class="step-content">
                    <div class="file-upload-area" id="performanceUploadArea">
                        <div class="upload-icon">📈</div>
                        <p><strong>点击或拖拽上传绩效表Excel文件</strong></p>
                        <p>支持格式：.xlsx, .xls</p>
                        <p style="font-size: 12px; color: #8D99AE; margin-top: 10px;">
                            包含：员工姓名、工号、绩效评分、奖金系数、特殊津贴
                        </p>
                        <input type="file" id="performanceFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                    <button class="btn btn-primary" id="processPerformanceBtn" disabled>处理绩效数据</button>
                </div>
            </div>

            <!-- 步骤3：工资计算引擎 -->
            <div class="step-card" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">工资计算</div>
                </div>
                <div class="step-content">
                    <div class="calculation-rules">
                        <h4>薪酬计算规则</h4>
                        <div class="rule-section">
                            <div class="rule-title">基本薪酬</div>
                            <div class="rule-item">
                                <span>基本工资</span>
                                <span>5000元/月</span>
                            </div>
                            <div class="rule-item">
                                <span>全勤奖</span>
                                <span>200元（无迟到早退）</span>
                            </div>
                        </div>
                        <div class="rule-section">
                            <div class="rule-title">扣除项目</div>
                            <div class="rule-item">
                                <span>迟到扣款</span>
                                <span>50元/次</span>
                            </div>
                            <div class="rule-item">
                                <span>早退扣款</span>
                                <span>50元/次</span>
                            </div>
                            <div class="rule-item">
                                <span>请假扣款</span>
                                <span>200元/天</span>
                            </div>
                        </div>
                        <div class="rule-section">
                            <div class="rule-title">绩效奖金</div>
                            <div class="rule-item">
                                <span>绩效基数</span>
                                <span>1000元</span>
                            </div>
                            <div class="rule-item">
                                <span>计算公式</span>
                                <span>基数 × 奖金系数</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-success" id="calculateSalaryBtn" disabled>开始计算工资</button>
                </div>
            </div>

            <!-- 步骤4：批量生成工资条 -->
            <div class="step-card" id="step4">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <div class="step-title">生成工资条</div>
                </div>
                <div class="step-content">
                    <div class="summary-stats" id="calculationStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="totalEmployees">0</div>
                            <div class="stat-label">员工总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalPayroll">¥0</div>
                            <div class="stat-label">工资总额</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="avgSalary">¥0</div>
                            <div class="stat-label">平均工资</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="generatedFiles">0</div>
                            <div class="stat-label">生成文件</div>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="generatePayslipsBtn" disabled>批量生成工资条</button>
                    <button class="btn btn-warning" id="exportSummaryBtn" disabled>导出汇总表</button>
                </div>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div id="dataDisplayArea" style="display: none;">
            <!-- 计算结果表格 -->
            <div class="step-card">
                <h3>工资计算结果</h3>
                <div class="loading" id="calculationLoading">
                    <div class="spinner"></div>
                    <p>正在计算工资数据...</p>
                </div>
                <table class="data-table" id="salaryTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>工号</th>
                            <th>基本工资</th>
                            <th>全勤奖</th>
                            <th>绩效奖金</th>
                            <th>扣款</th>
                            <th>应发工资</th>
                            <th>个税</th>
                            <th>实发工资</th>
                        </tr>
                    </thead>
                    <tbody id="salaryTableBody">
                    </tbody>
                </table>
            </div>

            <!-- 工资条预览 -->
            <div class="step-card" id="payslipPreviewArea" style="display: none;">
                <h3>工资条预览</h3>
                <div class="payroll-preview" id="payrollPreview">
                </div>
            </div>

            <!-- 生成的文件列表 -->
            <div class="step-card" id="fileListArea" style="display: none;">
                <h3>生成的工资条文件</h3>
                <div class="file-list" id="generatedFileList">
                </div>
            </div>
        </div>

        <!-- 成功提示 -->
        <div class="success-message" id="successMessage">
            <h3>🎉 任务完成！</h3>
            <p>您已成功完成月度工资条数据计算与生成操作，系统已记录您的学习进度。</p>
        </div>
    </div>

    <script>
        // 全局变量
        let attendanceData = [];
        let performanceData = [];
        let salaryData = [];
        let generatedPayslips = [];
        let currentStep = 1;
        let completedSteps = [];

        // 薪酬计算规则
        const salaryRules = {
            basicSalary: 5000,
            fullAttendanceBonus: 200,
            lateDeduction: 50,
            earlyLeaveDeduction: 50,
            leaveDeduction: 200,
            performanceBase: 1000,
            taxRate: 0.1 // 简化税率
        };

        // 模拟考勤数据
        const mockAttendanceData = [
            { name: '张三', employeeId: 'EMP001', workDays: 22, lateCount: 1, earlyLeaveCount: 0, leaveCount: 0 },
            { name: '李四', employeeId: 'EMP002', workDays: 20, lateCount: 0, earlyLeaveCount: 1, leaveCount: 2 },
            { name: '王五', employeeId: 'EMP003', workDays: 22, lateCount: 0, earlyLeaveCount: 0, leaveCount: 0 },
            { name: '赵六', employeeId: 'EMP004', workDays: 21, lateCount: 2, earlyLeaveCount: 1, leaveCount: 1 },
            { name: '刘七', employeeId: 'EMP005', workDays: 22, lateCount: 0, earlyLeaveCount: 0, leaveCount: 0 }
        ];

        // 模拟绩效数据
        const mockPerformanceData = [
            { name: '张三', employeeId: 'EMP001', score: 85, bonusCoeff: 1.2, allowance: 300 },
            { name: '李四', employeeId: 'EMP002', score: 78, bonusCoeff: 0.9, allowance: 200 },
            { name: '王五', employeeId: 'EMP003', score: 92, bonusCoeff: 1.5, allowance: 500 },
            { name: '赵六', employeeId: 'EMP004', score: 70, bonusCoeff: 0.8, allowance: 100 },
            { name: '刘七', employeeId: 'EMP005', score: 88, bonusCoeff: 1.3, allowance: 400 }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateProgress();
        });

        function initializeEventListeners() {
            // 文件上传事件
            setupFileUpload('attendanceUploadArea', 'attendanceFileInput', handleAttendanceFileUpload);
            setupFileUpload('performanceUploadArea', 'performanceFileInput', handlePerformanceFileUpload);

            // 按钮事件
            document.getElementById('processAttendanceBtn').addEventListener('click', processAttendanceData);
            document.getElementById('processPerformanceBtn').addEventListener('click', processPerformanceData);
            document.getElementById('calculateSalaryBtn').addEventListener('click', calculateSalary);
            document.getElementById('generatePayslipsBtn').addEventListener('click', generatePayslips);
            document.getElementById('exportSummaryBtn').addEventListener('click', exportSummary);
        }

        function setupFileUpload(uploadAreaId, fileInputId, handler) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);

            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handler);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handler({ target: { files: files } });
                }
            });
        }

        function handleAttendanceFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileName = file.name;
                document.querySelector('#step1 .file-upload-area p').innerHTML = 
                    `<strong>已选择文件：${fileName}</strong><br>准备处理考勤数据`;
                document.getElementById('processAttendanceBtn').disabled = false;
            }
        }

        function handlePerformanceFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileName = file.name;
                document.querySelector('#step2 .file-upload-area p').innerHTML = 
                    `<strong>已选择文件：${fileName}</strong><br>准备处理绩效数据`;
                document.getElementById('processPerformanceBtn').disabled = false;
            }
        }

        function processAttendanceData() {
            showLoading('正在处理考勤数据...');
            
            setTimeout(() => {
                attendanceData = [...mockAttendanceData];
                completedSteps.push('upload_attendance');
                completeStep(1);
                activateStep(2);
                updateProgress();
                hideLoading();
                
                console.log('考勤数据处理完成:', attendanceData);
            }, 1500);
        }

        function processPerformanceData() {
            showLoading('正在处理绩效数据...');
            
            setTimeout(() => {
                performanceData = [...mockPerformanceData];
                completedSteps.push('upload_performance');
                completeStep(2);
                activateStep(3);
                updateProgress();
                hideLoading();
                
                console.log('绩效数据处理完成:', performanceData);
            }, 1500);
        }

        function calculateSalary() {
            showLoading('正在执行工资计算...');
            document.getElementById('calculationLoading').style.display = 'block';
            document.getElementById('dataDisplayArea').style.display = 'block';
            
            setTimeout(() => {
                performSalaryCalculation();
                displaySalaryResults();
                completedSteps.push('calculation');
                completeStep(3);
                activateStep(4);
                updateProgress();
                updateCalculationStats();
                document.getElementById('calculationLoading').style.display = 'none';
                hideLoading();
            }, 2500);
        }

        function performSalaryCalculation() {
            salaryData = [];
            
            attendanceData.forEach(attendance => {
                const performance = performanceData.find(p => p.employeeId === attendance.employeeId);
                if (!performance) return;
                
                // 基本工资计算
                const basicSalary = salaryRules.basicSalary;
                
                // 全勤奖计算
                const fullAttendanceBonus = (attendance.lateCount === 0 && attendance.earlyLeaveCount === 0) 
                    ? salaryRules.fullAttendanceBonus : 0;
                
                // 扣款计算
                const lateDeduction = attendance.lateCount * salaryRules.lateDeduction;
                const earlyLeaveDeduction = attendance.earlyLeaveCount * salaryRules.earlyLeaveDeduction;
                const leaveDeduction = attendance.leaveCount * salaryRules.leaveDeduction;
                const totalDeduction = lateDeduction + earlyLeaveDeduction + leaveDeduction;
                
                // 绩效奖金计算
                const performanceBonus = salaryRules.performanceBase * performance.bonusCoeff;
                
                // 应发工资
                const grossSalary = basicSalary + fullAttendanceBonus + performanceBonus + performance.allowance - totalDeduction;
                
                // 个税计算（简化）
                const tax = Math.max(0, (grossSalary - 5000) * salaryRules.taxRate);
                
                // 实发工资
                const netSalary = grossSalary - tax;
                
                salaryData.push({
                    name: attendance.name,
                    employeeId: attendance.employeeId,
                    attendance: attendance,
                    performance: performance,
                    calculation: {
                        basicSalary,
                        fullAttendanceBonus,
                        performanceBonus,
                        allowance: performance.allowance,
                        totalDeduction,
                        grossSalary,
                        tax,
                        netSalary
                    }
                });
            });
        }

        function displaySalaryResults() {
            const tableBody = document.getElementById('salaryTableBody');
            tableBody.innerHTML = '';

            salaryData.forEach(employee => {
                const row = document.createElement('tr');
                const calc = employee.calculation;
                
                row.innerHTML = `
                    <td>${employee.name}</td>
                    <td>${employee.employeeId}</td>
                    <td>¥${calc.basicSalary.toLocaleString()}</td>
                    <td>¥${calc.fullAttendanceBonus.toLocaleString()}</td>
                    <td>¥${calc.performanceBonus.toLocaleString()}</td>
                    <td>¥${calc.totalDeduction.toLocaleString()}</td>
                    <td>¥${calc.grossSalary.toLocaleString()}</td>
                    <td>¥${calc.tax.toLocaleString()}</td>
                    <td><strong>¥${calc.netSalary.toLocaleString()}</strong></td>
                `;
                tableBody.appendChild(row);
            });

            document.getElementById('salaryTable').style.display = 'table';
        }

        function updateCalculationStats() {
            const totalEmployees = salaryData.length;
            const totalPayroll = salaryData.reduce((sum, emp) => sum + emp.calculation.netSalary, 0);
            const avgSalary = totalEmployees > 0 ? totalPayroll / totalEmployees : 0;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('totalPayroll').textContent = `¥${totalPayroll.toLocaleString()}`;
            document.getElementById('avgSalary').textContent = `¥${Math.round(avgSalary).toLocaleString()}`;
            document.getElementById('calculationStats').style.display = 'grid';
        }

        function generatePayslips() {
            showLoading('正在生成工资条文件...');
            
            setTimeout(() => {
                generatedPayslips = [];
                const currentDate = new Date();
                const period = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`;
                
                salaryData.forEach(employee => {
                    const payslip = createPayslipContent(employee, period);
                    generatedPayslips.push({
                        employeeId: employee.employeeId,
                        name: employee.name,
                        content: payslip,
                        fileName: `${employee.name}_${period}_工资条.html`
                    });
                });
                
                displayPayslipPreview();
                displayGeneratedFiles();
                completedSteps.push('generation');
                completeStep(4);
                updateProgress();
                updateGeneratedFilesCount();
                hideLoading();
                
                console.log('工资条生成完成:', generatedPayslips.length, '个文件');
            }, 2000);
        }

        function createPayslipContent(employee, period) {
            const calc = employee.calculation;
            
            return `
                <div class="employee-payslip">
                    <div class="payslip-header">
                        <div class="payslip-title">工资条</div>
                        <div class="payslip-period">${period}</div>
                    </div>
                    <div class="payslip-content">
                        <div class="payslip-section">
                            <div class="section-title">员工信息</div>
                            <div class="payslip-item">
                                <span>姓名：</span>
                                <span>${employee.name}</span>
                            </div>
                            <div class="payslip-item">
                                <span>工号：</span>
                                <span>${employee.employeeId}</span>
                            </div>
                        </div>
                        <div class="payslip-section">
                            <div class="section-title">收入项目</div>
                            <div class="payslip-item">
                                <span>基本工资：</span>
                                <span>¥${calc.basicSalary.toLocaleString()}</span>
                            </div>
                            <div class="payslip-item">
                                <span>全勤奖：</span>
                                <span>¥${calc.fullAttendanceBonus.toLocaleString()}</span>
                            </div>
                            <div class="payslip-item">
                                <span>绩效奖金：</span>
                                <span>¥${calc.performanceBonus.toLocaleString()}</span>
                            </div>
                            <div class="payslip-item">
                                <span>特殊津贴：</span>
                                <span>¥${calc.allowance.toLocaleString()}</span>
                            </div>
                        </div>
                        <div class="payslip-section">
                            <div class="section-title">扣除项目</div>
                            <div class="payslip-item">
                                <span>考勤扣款：</span>
                                <span>¥${calc.totalDeduction.toLocaleString()}</span>
                            </div>
                            <div class="payslip-item">
                                <span>个人所得税：</span>
                                <span>¥${calc.tax.toLocaleString()}</span>
                            </div>
                        </div>
                        <div class="payslip-section">
                            <div class="section-title">考勤统计</div>
                            <div class="payslip-item">
                                <span>出勤天数：</span>
                                <span>${employee.attendance.workDays}天</span>
                            </div>
                            <div class="payslip-item">
                                <span>迟到次数：</span>
                                <span>${employee.attendance.lateCount}次</span>
                            </div>
                            <div class="payslip-item">
                                <span>早退次数：</span>
                                <span>${employee.attendance.earlyLeaveCount}次</span>
                            </div>
                            <div class="payslip-item">
                                <span>请假天数：</span>
                                <span>${employee.attendance.leaveCount}天</span>
                            </div>
                        </div>
                    </div>
                    <div class="payslip-total">
                        <div class="payslip-item">
                            <span>应发工资：</span>
                            <span>¥${calc.grossSalary.toLocaleString()}</span>
                        </div>
                        <div class="payslip-item">
                            <span>实发工资：</span>
                            <span>¥${calc.netSalary.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        function displayPayslipPreview() {
            const previewArea = document.getElementById('payrollPreview');
            previewArea.innerHTML = '';
            
            // 显示前3个工资条作为预览
            const previewCount = Math.min(3, generatedPayslips.length);
            for (let i = 0; i < previewCount; i++) {
                previewArea.innerHTML += generatedPayslips[i].content;
            }
            
            if (generatedPayslips.length > 3) {
                previewArea.innerHTML += `<p style="text-align: center; color: #8D99AE; margin: 20px;">
                    还有 ${generatedPayslips.length - 3} 个工资条未显示...
                </p>`;
            }
            
            document.getElementById('payslipPreviewArea').style.display = 'block';
        }

        function displayGeneratedFiles() {
            const fileList = document.getElementById('generatedFileList');
            fileList.innerHTML = '';
            
            generatedPayslips.forEach(payslip => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div>
                        <div class="file-name">${payslip.fileName}</div>
                        <div class="file-size">HTML格式</div>
                    </div>
                    <button class="btn btn-primary" onclick="downloadPayslip('${payslip.employeeId}')">下载</button>
                `;
                fileList.appendChild(fileItem);
            });
            
            document.getElementById('fileListArea').style.display = 'block';
        }

        function updateGeneratedFilesCount() {
            document.getElementById('generatedFiles').textContent = generatedPayslips.length;
        }

        function downloadPayslip(employeeId) {
            const payslip = generatedPayslips.find(p => p.employeeId === employeeId);
            if (payslip) {
                // 模拟下载
                console.log('下载工资条:', payslip.fileName);
                alert(`正在下载 ${payslip.fileName}`);
            }
        }

        function exportSummary() {
            // 模拟导出汇总表
            const summaryData = salaryData.map(emp => ({
                姓名: emp.name,
                工号: emp.employeeId,
                基本工资: emp.calculation.basicSalary,
                全勤奖: emp.calculation.fullAttendanceBonus,
                绩效奖金: emp.calculation.performanceBonus,
                特殊津贴: emp.calculation.allowance,
                扣款: emp.calculation.totalDeduction,
                应发工资: emp.calculation.grossSalary,
                个税: emp.calculation.tax,
                实发工资: emp.calculation.netSalary
            }));
            
            console.log('导出汇总数据:', summaryData);
            alert('工资汇总表已导出到Excel文件');
            
            // 提交验证
            submitValidation();
        }

        function completeStep(stepNumber) {
            const stepCard = document.getElementById(`step${stepNumber}`);
            stepCard.classList.remove('active');
            stepCard.classList.add('completed');
        }

        function activateStep(stepNumber) {
            if (stepNumber <= 4) {
                const stepCard = document.getElementById(`step${stepNumber}`);
                stepCard.classList.add('active');
                
                // 启用对应的按钮
                switch(stepNumber) {
                    case 3:
                        document.getElementById('calculateSalaryBtn').disabled = false;
                        break;
                    case 4:
                        document.getElementById('generatePayslipsBtn').disabled = false;
                        document.getElementById('exportSummaryBtn').disabled = false;
                        break;
                }
            }
        }

        function updateProgress() {
            const totalSteps = 4;
            const progress = (completedSteps.length / totalSteps) * 100;
            
            document.getElementById('progressBar').querySelector('.progress-fill').style.width = `${progress}%`;
            
            let progressText = '';
            switch(completedSteps.length) {
                case 0:
                    progressText = '准备开始 - 请上传考勤数据';
                    break;
                case 1:
                    progressText = '考勤数据已处理 - 请上传绩效数据';
                    break;
                case 2:
                    progressText = '数据准备完成 - 请开始工资计算';
                    break;
                case 3:
                    progressText = '工资计算完成 - 请生成工资条文件';
                    break;
                case 4:
                    progressText = '任务完成 - 所有工资条已生成完毕';
                    break;
            }
            
            document.getElementById('progressText').textContent = progressText;
        }

        function showLoading(message) {
            console.log('Loading:', message);
        }

        function hideLoading() {
            // 隐藏加载提示
        }

        async function submitValidation() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const validationData = {
                    completedSteps: completedSteps,
                    attendanceDataCount: attendanceData.length,
                    performanceDataCount: performanceData.length,
                    calculatedSalaries: salaryData.length,
                    generatedPayslips: generatedPayslips.length,
                    totalPayroll: salaryData.reduce((sum, emp) => sum + emp.calculation.netSalary, 0),
                    taskCompleted: completedSteps.length === 4
                };

                const response = await fetch('/api/scenarios/7/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(validationData)
                });

                const result = await response.json();
                
                if (result.success && result.valid) {
                    document.getElementById('successMessage').classList.add('show');
                    console.log('场景7验证成功:', result);
                } else {
                    console.error('场景7验证失败:', result.feedback);
                }
            } catch (error) {
                console.error('提交验证失败:', error);
            }
        }
    </script>
</body>
</html>