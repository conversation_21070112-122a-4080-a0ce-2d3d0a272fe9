/**
 * OCR识别API路由
 * 处理发票识别、文档OCR等功能
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { authMiddleware } = require('../middleware/auth');
const ocrService = require('../services/ocr-service');

const router = express.Router();

// 配置文件上传
const storage = multer.memoryStorage(); // 使用内存存储，直接处理
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB限制
        files: 10 // 最多10个文件
    },
    fileFilter: (req, file, cb) => {
        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('不支持的文件类型，请上传JPG、PNG或PDF文件'), false);
        }
    }
});

/**
 * 发票OCR识别
 * POST /api/ocr/invoice
 */
router.post('/invoice', authMiddleware, upload.array('invoices', 10), async (req, res) => {
    try {
        const userId = req.user.userId;
        const files = req.files;

        if (!files || files.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请上传至少一个发票文件'
            });
        }

        console.log(`📄 用户 ${req.user.username} 上传了 ${files.length} 个发票文件进行OCR识别`);

        // 准备批量识别数据
        const imageDataList = files.map(file => ({
            data: file.buffer,
            filename: file.originalname,
            mimetype: file.mimetype,
            size: file.size
        }));

        // 执行批量OCR识别
        const ocrResults = await ocrService.batchRecognizeInvoices(imageDataList);

        // 统计识别结果
        const successCount = ocrResults.filter(r => r.success).length;
        const totalAmount = ocrResults
            .filter(r => r.success)
            .reduce((sum, r) => sum + (r.data.totalAmount || 0), 0);

        // 计算平均置信度
        const averageConfidence = ocrResults
            .filter(r => r.success)
            .reduce((sum, r) => sum + (r.confidence || 0), 0) / successCount;

        console.log(`✅ OCR识别完成: ${successCount}/${files.length} 成功, 总金额: ¥${totalAmount.toFixed(2)}`);

        res.json({
            success: true,
            message: `成功识别 ${successCount} 张发票`,
            data: {
                results: ocrResults,
                statistics: {
                    totalFiles: files.length,
                    successCount: successCount,
                    failureCount: files.length - successCount,
                    totalAmount: totalAmount,
                    averageConfidence: averageConfidence || 0,
                    processingTime: Date.now()
                }
            }
        });

    } catch (error) {
        console.error('❌ 发票OCR识别失败:', error);

        // 如果是Multer错误（文件上传问题）
        if (error instanceof multer.MulterError) {
            let message = '文件上传失败';
            if (error.code === 'LIMIT_FILE_SIZE') {
                message = '文件大小超过限制（最大10MB）';
            } else if (error.code === 'LIMIT_FILE_COUNT') {
                message = '文件数量超过限制（最多10个）';
            }
            
            return res.status(400).json({
                success: false,
                message: message
            });
        }

        res.status(500).json({
            success: false,
            message: error.message || 'OCR识别服务异常',
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

/**
 * 发票验证
 * POST /api/ocr/verify-invoice
 */
router.post('/verify-invoice', authMiddleware, async (req, res) => {
    try {
        const { ocrResults } = req.body;

        if (!ocrResults || !Array.isArray(ocrResults)) {
            return res.status(400).json({
                success: false,
                message: '请提供有效的OCR识别结果'
            });
        }

        console.log(`🔍 开始验证 ${ocrResults.length} 张发票`);

        // 模拟发票验证过程
        const verificationResults = await Promise.all(
            ocrResults.map(async (result, index) => {
                // 模拟验证延时
                await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

                // 验证OCR结果完整性
                const validation = ocrService.validateOCRResult(result);
                
                // 模拟税务系统验证
                const isValidInTaxSystem = Math.random() > 0.15; // 85%通过率
                const verificationStatus = validation.isValid && isValidInTaxSystem ? 'valid' : 'invalid';
                
                return {
                    ...result,
                    verification: {
                        status: verificationStatus,
                        isValid: validation.isValid,
                        errors: validation.errors,
                        taxSystemCheck: isValidInTaxSystem,
                        verifiedAt: new Date().toISOString(),
                        verificationId: `VER_${Date.now()}_${index + 1}`
                    }
                };
            })
        );

        // 统计验证结果
        const validCount = verificationResults.filter(r => r.verification.status === 'valid').length;
        const invalidCount = verificationResults.length - validCount;

        console.log(`✅ 发票验证完成: ${validCount} 张有效, ${invalidCount} 张无效`);

        res.json({
            success: true,
            message: `验证完成: ${validCount} 张有效发票`,
            data: {
                results: verificationResults,
                summary: {
                    total: verificationResults.length,
                    valid: validCount,
                    invalid: invalidCount,
                    validRate: (validCount / verificationResults.length * 100).toFixed(1) + '%'
                }
            }
        });

    } catch (error) {
        console.error('❌ 发票验证失败:', error);
        res.status(500).json({
            success: false,
            message: '发票验证服务异常'
        });
    }
});

/**
 * 生成会计分录
 * POST /api/ocr/generate-entries
 */
router.post('/generate-entries', authMiddleware, async (req, res) => {
    try {
        const { verifiedInvoices } = req.body;

        if (!verifiedInvoices || !Array.isArray(verifiedInvoices)) {
            return res.status(400).json({
                success: false,
                message: '请提供已验证的发票数据'
            });
        }

        // 过滤出有效发票
        const validInvoices = verifiedInvoices.filter(inv => 
            inv.verification && inv.verification.status === 'valid'
        );

        if (validInvoices.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有有效的发票可以生成会计分录'
            });
        }

        console.log(`📊 开始为 ${validInvoices.length} 张发票生成会计分录`);

        // 生成会计分录
        const accountingEntries = validInvoices.map((invoice, index) => {
            const { data } = invoice;
            const entryId = `ENTRY_${Date.now()}_${index + 1}`;
            
            // 基本会计分录规则
            const entries = [
                {
                    id: `${entryId}_1`,
                    account: '1403', // 原材料/库存商品
                    accountName: '原材料',
                    debit: data.amountWithoutTax,
                    credit: 0,
                    description: `采购：${data.sellerName}`
                },
                {
                    id: `${entryId}_2`,
                    account: '2221001', // 应交税费-应交增值税-进项税额
                    accountName: '应交税费-应交增值税-进项税额',
                    debit: data.taxAmount,
                    credit: 0,
                    description: `进项税：${data.invoiceNumber}`
                },
                {
                    id: `${entryId}_3`,
                    account: '2202', // 应付账款
                    accountName: '应付账款',
                    debit: 0,
                    credit: data.totalAmount,
                    description: `应付：${data.sellerName}`
                }
            ];

            return {
                invoiceId: invoice.data.invoiceNumber,
                entryId: entryId,
                date: new Date().toISOString().split('T')[0],
                description: `采购发票入账：${data.sellerName}`,
                amount: data.totalAmount,
                entries: entries,
                invoice: {
                    number: data.invoiceNumber,
                    date: data.date,
                    seller: data.sellerName,
                    amount: data.totalAmount
                },
                createdAt: new Date().toISOString()
            };
        });

        // 计算汇总信息
        const totalAmount = accountingEntries.reduce((sum, entry) => sum + entry.amount, 0);
        const totalEntries = accountingEntries.reduce((sum, entry) => sum + entry.entries.length, 0);

        console.log(`✅ 会计分录生成完成: ${accountingEntries.length} 笔业务, 总金额: ¥${totalAmount.toFixed(2)}`);

        res.json({
            success: true,
            message: `成功生成 ${accountingEntries.length} 笔会计分录`,
            data: {
                entries: accountingEntries,
                summary: {
                    invoiceCount: validInvoices.length,
                    entryCount: accountingEntries.length,
                    totalEntries: totalEntries,
                    totalAmount: totalAmount,
                    generatedAt: new Date().toISOString()
                }
            }
        });

    } catch (error) {
        console.error('❌ 生成会计分录失败:', error);
        res.status(500).json({
            success: false,
            message: '会计分录生成服务异常'
        });
    }
});

/**
 * 通用文档OCR识别
 * POST /api/ocr/document
 */
router.post('/document', authMiddleware, upload.single('document'), async (req, res) => {
    try {
        const file = req.file;

        if (!file) {
            return res.status(400).json({
                success: false,
                message: '请上传文档文件'
            });
        }

        console.log(`📄 用户 ${req.user.username} 上传文档进行OCR识别: ${file.originalname}`);

        // 执行OCR识别
        const ocrResult = await ocrService.recognizeGeneral(file.buffer, file.originalname);

        res.json({
            success: true,
            message: 'OCR识别成功',
            data: ocrResult
        });

    } catch (error) {
        console.error('❌ 文档OCR识别失败:', error);
        res.status(500).json({
            success: false,
            message: 'OCR识别服务异常'
        });
    }
});

/**
 * 获取OCR服务状态
 * GET /api/ocr/status
 */
router.get('/status', authMiddleware, async (req, res) => {
    try {
        // 获取所有适配器状态
        const adaptersStatus = ocrService.getAdaptersStatus();
        const bestAdapter = ocrService.getBestAdapter();

        res.json({
            success: true,
            data: {
                ...adaptersStatus,
                bestAdapter: bestAdapter,
                features: {
                    invoiceRecognition: true,
                    generalOCR: true,
                    batchProcessing: true,
                    invoiceVerification: true,
                    accountingEntries: true,
                    multiVendorSupport: true,
                    adapterSwitching: true,
                    aiPowered: adaptersStatus.adapters.some(a => a.name === 'doubao' && a.configured)
                },
                limits: {
                    maxFileSize: '10MB',
                    maxFiles: 10,
                    supportedFormats: ['JPG', 'PNG', 'PDF']
                }
            }
        });

    } catch (error) {
        console.error('❌ 获取OCR状态失败:', error);
        res.status(500).json({
            success: false,
            message: '获取服务状态失败'
        });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: '文件大小超过限制（最大10MB）'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: '文件数量超过限制（最多10个文件）'
            });
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                success: false,
                message: '不支持的文件字段'
            });
        }
    }

    if (error.message === '不支持的文件类型，请上传JPG、PNG或PDF文件') {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }

    // 其他错误
    console.error('OCR路由错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
});

module.exports = router;