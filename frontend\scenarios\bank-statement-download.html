<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业网银流水查询下载 - RPA实训场景1</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .header .scenario-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #8D99AE;
            font-size: 14px;
        }
        
        .simulation-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .bank-header {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .bank-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .bank-subtitle {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .step.active .step-number {
            background: #7673FF;
            color: white;
        }
        
        .step.completed .step-number {
            background: #7673FF;
            color: white;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118, 115, 255, 0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118, 115, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118, 115, 255, 0.4);
        }
        
        .btn-success {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118, 115, 255, 0.3);
        }
        
        .btn-success:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118, 115, 255, 0.4);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn:hover {
            background: #f3f4f6;
        }
        
        .page-btn.active {
            background: #7673FF;
            color: white;
            border-color: #7673FF;
        }
        
        .success-message {
            background: #d1fae5;
            color: #065f46;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border-left: 4px solid #10b981;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #7673FF;
            border-radius: 3px;
            transition: width 0.3s;
            width: 0%;
        }
        
        .validation-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            display: none;
        }
        
        .validation-status.success {
            background: #7673FF;
        }
        
        .validation-status.error {
            background: #ef4444;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rpa-hints {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .rpa-hints h4 {
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .rpa-hints ul {
            color: #92400e;
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 场景头部信息 -->
        <div class="header">
            <h1>场景1：企业网银流水查询下载</h1>
            <p>学习企业网银操作流程，掌握流水查询和批量下载技能</p>
            <div class="scenario-info">
                <div class="info-item">
                    <span>📚</span>
                    <span>模块：基础操作</span>
                </div>
                <div class="info-item">
                    <span>⏱️</span>
                    <span>预计时间：30分钟</span>
                </div>
                <div class="info-item">
                    <span>📊</span>
                    <span>难度：初级</span>
                </div>
            </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 仿真银行系统 -->
        <div class="simulation-container">
            <div class="bank-header">
                <div class="bank-logo">🏦 练习银行企业网银系统</div>
                <div class="bank-subtitle">Enterprise Online Banking System - Training Environment</div>
            </div>

            <div class="main-content">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                        <span>登录系统</span>
                    </div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <span>查询条件</span>
                    </div>
                    <div class="step" id="step3">
                        <div class="step-number">3</div>
                        <span>流水数据</span>
                    </div>
                    <div class="step" id="step4">
                        <div class="step-number">4</div>
                        <span>下载文件</span>
                    </div>
                </div>

                <!-- 第一步：登录 -->
                <div class="section active" id="loginSection">
                    <h3>企业网银登录</h3>
                    
                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>使用影刀RPA定位登录表单元素（ID: login-form）</li>
                            <li>自动填写用户名和密码</li>
                            <li>点击登录按钮完成认证</li>
                        </ul>
                    </div>

                    <form id="login-form">
                        <div class="form-group">
                            <label for="username">企业用户名</label>
                            <input type="text" id="username" class="form-control" placeholder="请输入企业用户名" required>
                        </div>
                        <div class="form-group">
                            <label for="password">登录密码</label>
                            <input type="password" id="password" class="form-control" placeholder="请输入登录密码" required>
                        </div>
                        <div class="form-group">
                            <label for="captcha">验证码</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" id="captcha" class="form-control" placeholder="请输入验证码" style="flex: 1;" required>
                                <div style="background: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">AB8C</div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="loginBtn">登录系统</button>
                    </form>
                </div>

                <!-- 第二步：查询条件 -->
                <div class="section" id="querySection">
                    <h3>流水查询条件设置</h3>

                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>定位查询表单（ID: query-form）</li>
                            <li>设置日期范围和账户筛选条件</li>
                            <li>处理日期选择器控件</li>
                        </ul>
                    </div>

                    <form id="query-form">
                        <div class="form-group">
                            <label for="account">选择账户</label>
                            <select id="account" class="form-control" required>
                                <option value="">请选择查询账户</option>
                                <option value="001">基本户 - **************** (中国银行)</option>
                                <option value="002">一般户 - **************** (工商银行)</option>
                                <option value="003">专用户 - **************** (建设银行)</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 20px;">
                            <div class="form-group" style="flex: 1;">
                                <label for="startDate">开始日期</label>
                                <input type="date" id="startDate" class="form-control" required>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <label for="endDate">结束日期</label>
                                <input type="date" id="endDate" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>交易类型筛选</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="type1" checked>
                                    <label for="type1">收入</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="type2" checked>
                                    <label for="type2">支出</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="type3">
                                    <label for="type3">转账</label>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="queryBtn">查询流水</button>
                    </form>
                </div>

                <!-- 第三步：流水数据展示 -->
                <div class="section" id="dataSection">
                    <h3>银行流水数据</h3>

                    <div class="rpa-hints">
                        <h4>🤖 RPA操作提示</h4>
                        <ul>
                            <li>处理分页数据，需要遍历所有页面</li>
                            <li>提取表格数据（表格ID: data-table）</li>
                            <li>处理动态加载的内容</li>
                        </ul>
                    </div>

                    <div style="margin-bottom: 15px; color: #666;">
                        查询结果：共找到 <span id="totalRecords">156</span> 条记录
                    </div>

                    <table class="data-table" id="data-table">
                        <thead>
                            <tr>
                                <th>交易日期</th>
                                <th>交易时间</th>
                                <th>对方账户</th>
                                <th>对方户名</th>
                                <th>摘要</th>
                                <th>支出金额</th>
                                <th>收入金额</th>
                                <th>余额</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>

                    <div class="pagination" id="pagination">
                        <!-- 分页按钮将动态生成 -->
                    </div>

                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" id="exportBtn">📥 导出Excel文件</button>
                        <button class="btn btn-secondary" id="selectAllBtn">☑️ 全选当前页</button>
                    </div>
                </div>

                <!-- 第四步：下载完成 -->
                <div class="section" id="downloadSection">
                    <h3>文件下载完成</h3>

                    <div class="success-message">
                        <h4>✅ 任务完成！</h4>
                        <p>恭喜您成功完成企业网银流水查询下载场景！</p>
                        <ul style="margin-top: 10px; margin-left: 20px;">
                            <li>✓ 成功登录企业网银系统</li>
                            <li>✓ 正确设置查询条件</li>
                            <li>✓ 处理分页数据获取</li>
                            <li>✓ 完成Excel文件下载</li>
                        </ul>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <button class="btn btn-primary" id="resetBtn">🔄 重新练习</button>
                        <a href="student-dashboard.html" class="btn btn-secondary">📋 返回主界面</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 验证状态提示 -->
    <div class="validation-status" id="validationStatus"></div>

    <script>
        // 场景1的JavaScript逻辑
        class BankStatementScenario {
            constructor() {
                this.currentStep = 1;
                this.totalSteps = 4;
                this.currentPage = 1;
                this.totalPages = 8;
                this.isCompleted = false;
                
                this.init();
                this.generateMockData();
            }

            init() {
                // 绑定事件监听器
                document.getElementById('login-form').addEventListener('submit', (e) => this.handleLogin(e));
                document.getElementById('query-form').addEventListener('submit', (e) => this.handleQuery(e));
                document.getElementById('exportBtn').addEventListener('click', () => this.handleExport());
                document.getElementById('resetBtn').addEventListener('click', () => this.reset());
                
                // 设置默认日期
                this.setDefaultDates();
                
                // 更新进度
                this.updateProgress();
            }

            setDefaultDates() {
                const today = new Date();
                const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                
                document.getElementById('startDate').value = lastMonth.toISOString().split('T')[0];
                document.getElementById('endDate').value = lastMonthEnd.toISOString().split('T')[0];
            }

            handleLogin(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const captcha = document.getElementById('captcha').value;

                // 验证表单
                if (!username || !password || !captcha) {
                    this.showValidationMessage('请填写完整的登录信息', 'error');
                    return;
                }

                if (captcha.toLowerCase() !== 'ab8c') {
                    this.showValidationMessage('验证码错误', 'error');
                    return;
                }

                // 模拟登录过程
                this.showValidationMessage('正在登录...', 'success');
                
                setTimeout(() => {
                    this.showValidationMessage('登录成功！', 'success');
                    this.nextStep();
                }, 1500);
            }

            handleQuery(e) {
                e.preventDefault();
                
                const account = document.getElementById('account').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                if (!account || !startDate || !endDate) {
                    this.showValidationMessage('请完整填写查询条件', 'error');
                    return;
                }

                // 验证日期范围
                if (new Date(startDate) > new Date(endDate)) {
                    this.showValidationMessage('开始日期不能大于结束日期', 'error');
                    return;
                }

                this.showValidationMessage('正在查询流水数据...', 'success');
                
                setTimeout(() => {
                    this.loadTableData();
                    this.nextStep();
                }, 2000);
            }

            handleExport() {
                this.showValidationMessage('正在生成Excel文件...', 'success');
                
                // 模拟文件下载
                setTimeout(() => {
                    // 创建虚拟下载
                    const link = document.createElement('a');
                    link.href = 'data:text/plain;charset=utf-8,银行流水数据导出文件';
                    link.download = '银行流水_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    link.click();
                    
                    this.showValidationMessage('Excel文件下载成功！', 'success');
                    this.nextStep();
                    
                    // 调用验证API
                    this.validateCompletion();
                }, 2000);
            }

            nextStep() {
                if (this.currentStep < this.totalSteps) {
                    // 更新步骤状态
                    document.getElementById(`step${this.currentStep}`).classList.add('completed');
                    document.getElementById(`step${this.currentStep}`).classList.remove('active');
                    
                    this.currentStep++;
                    
                    if (this.currentStep <= this.totalSteps) {
                        document.getElementById(`step${this.currentStep}`).classList.add('active');
                    }
                    
                    // 显示对应的内容区域
                    document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
                    
                    const sections = ['loginSection', 'querySection', 'dataSection', 'downloadSection'];
                    if (sections[this.currentStep - 1]) {
                        document.getElementById(sections[this.currentStep - 1]).classList.add('active');
                    }
                    
                    this.updateProgress();
                }
            }

            updateProgress() {
                const progress = (this.currentStep - 1) / (this.totalSteps - 1) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
            }

            generateMockData() {
                this.mockData = [];
                const startDate = new Date('2024-01-01');
                const companies = ['华为技术有限公司', '腾讯科技', '阿里巴巴集团', '百度在线', '字节跳动', '京东集团'];
                
                for (let i = 0; i < 156; i++) {
                    const date = new Date(startDate);
                    date.setDate(date.getDate() + i);
                    
                    const isIncome = Math.random() > 0.6;
                    const amount = (Math.random() * 100000 + 1000).toFixed(2);
                    const balance = (500000 + Math.random() * 1000000).toFixed(2);
                    
                    this.mockData.push({
                        date: date.toISOString().split('T')[0],
                        time: Math.floor(Math.random() * 24).toString().padStart(2, '0') + ':' + 
                              Math.floor(Math.random() * 60).toString().padStart(2, '0'),
                        counterpartyAccount: '6225' + Math.floor(Math.random() * *************).toString().padStart(12, '0'),
                        counterpartyName: companies[Math.floor(Math.random() * companies.length)],
                        summary: isIncome ? '货款收入' : '费用支出',
                        expense: isIncome ? '' : amount,
                        income: isIncome ? amount : '',
                        balance: balance
                    });
                }
            }

            loadTableData() {
                const tbody = document.getElementById('dataTableBody');
                const startIndex = (this.currentPage - 1) * 20;
                const endIndex = Math.min(startIndex + 20, this.mockData.length);
                
                tbody.innerHTML = '';
                
                for (let i = startIndex; i < endIndex; i++) {
                    const row = this.mockData[i];
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${row.date}</td>
                        <td>${row.time}</td>
                        <td>${row.counterpartyAccount}</td>
                        <td>${row.counterpartyName}</td>
                        <td>${row.summary}</td>
                        <td style="color: red;">${row.expense}</td>
                        <td style="color: green;">${row.income}</td>
                        <td>${row.balance}</td>
                    `;
                    tbody.appendChild(tr);
                }
                
                this.generatePagination();
            }

            generatePagination() {
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';
                
                // 上一页
                const prevBtn = document.createElement('button');
                prevBtn.className = 'page-btn';
                prevBtn.textContent = '上一页';
                prevBtn.disabled = this.currentPage === 1;
                prevBtn.onclick = () => this.goToPage(this.currentPage - 1);
                pagination.appendChild(prevBtn);
                
                // 页码
                for (let i = 1; i <= this.totalPages; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = 'page-btn' + (i === this.currentPage ? ' active' : '');
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => this.goToPage(i);
                    pagination.appendChild(pageBtn);
                }
                
                // 下一页
                const nextBtn = document.createElement('button');
                nextBtn.className = 'page-btn';
                nextBtn.textContent = '下一页';
                nextBtn.disabled = this.currentPage === this.totalPages;
                nextBtn.onclick = () => this.goToPage(this.currentPage + 1);
                pagination.appendChild(nextBtn);
            }

            goToPage(page) {
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.loadTableData();
                }
            }

            async validateCompletion() {
                try {
                    // 调用后端验证API
                    const response = await fetch('/api/validate/scenario/1', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + localStorage.getItem('token')
                        },
                        body: JSON.stringify({
                            scenario_id: 1,
                            validation_data: {
                                login_completed: true,
                                query_completed: true,
                                export_completed: true,
                                steps_completed: this.currentStep
                            }
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        this.showValidationMessage(`任务验证成功！得分：${result.data.score}`, 'success');
                        this.isCompleted = true;
                    } else {
                        this.showValidationMessage('任务验证失败：' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('验证请求失败:', error);
                    this.showValidationMessage('网络连接失败，但任务已完成', 'success');
                }
            }

            showValidationMessage(message, type) {
                const status = document.getElementById('validationStatus');
                status.className = `validation-status ${type}`;
                status.textContent = message;
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }

            reset() {
                this.currentStep = 1;
                this.currentPage = 1;
                this.isCompleted = false;
                
                // 重置所有表单
                document.getElementById('login-form').reset();
                document.getElementById('query-form').reset();
                
                // 重置步骤状态
                document.querySelectorAll('.step').forEach(step => {
                    step.classList.remove('active', 'completed');
                });
                document.getElementById('step1').classList.add('active');
                
                // 显示第一步
                document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
                document.getElementById('loginSection').classList.add('active');
                
                this.updateProgress();
                this.setDefaultDates();
            }
        }

        // 初始化场景
        document.addEventListener('DOMContentLoaded', () => {
            new BankStatementScenario();
        });
    </script>
</body>
</html>