<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景10：员工差旅费报销智能初审 (人机协作) - RPA财务机器人实训平台</title>
    
    <!-- 样式引入 -->
    <style>
        /* 薄暮天空配色系统 */
        :root {
            --primary-gradient: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            --primary-color: #7673FF;
            --primary-light: rgba(118, 115, 255, 0.15);
            --text-primary: #2c3e50;
            --text-secondary: #8D99AE;
            --bg-color: #F9FAFB;
            --card-bg: #FFFFFF;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --info-color: #3B82F6;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 10px 15px -3px rgba(118, 115, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .header {
            background: var(--primary-gradient);
            padding: 1rem 2rem;
            color: white;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scenario-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .scenario-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* 进度条 */
        .progress-container {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: #E5E7EB;
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .step {
            text-align: center;
            padding: 0.5rem;
        }

        .step-circle {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #E5E7EB;
            color: #6B7280;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 操作面板 */
        .operation-panel {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .operation-panel:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .panel-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--primary-color);
        }

        /* 报销单列表 */
        .expense-list {
            display: grid;
            gap: 1rem;
        }

        .expense-item {
            background: #F8FAFC;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .expense-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .expense-item.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .expense-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .expense-id {
            font-weight: 600;
            color: var(--text-primary);
        }

        .expense-amount {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .expense-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-weight: 500;
            color: var(--text-primary);
        }

        /* 费用明细表 */
        .expense-breakdown {
            margin-top: 1rem;
            overflow-x: auto;
        }

        .breakdown-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .breakdown-table th,
        .breakdown-table td {
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .breakdown-table th {
            background: #F3F4F6;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 审核状态标签 */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }

        .status-approved {
            background: #DCFCE7;
            color: #166534;
        }

        .status-rejected {
            background: #FEE2E2;
            color: #991B1B;
        }

        .status-review {
            background: #DBEAFE;
            color: #1E40AF;
        }

        /* 智能审核结果 */
        .audit-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid;
        }

        .audit-approved {
            background: #DCFCE7;
            border-color: var(--success-color);
            color: #166534;
        }

        .audit-rejected {
            background: #FEE2E2;
            border-color: var(--error-color);
            color: #991B1B;
        }

        .audit-review {
            background: #DBEAFE;
            border-color: var(--info-color);
            color: #1E40AF;
        }

        .audit-warnings {
            margin-top: 0.5rem;
            padding-left: 1rem;
        }

        .audit-warnings li {
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }

        /* 人工审核面板 */
        .manual-review {
            background: #FFF7ED;
            border: 2px solid var(--warning-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .review-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            color: #92400E;
            font-weight: 600;
        }

        .review-options {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .review-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
        }

        .review-option:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .review-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }

        .review-option.approve {
            border-color: var(--success-color);
        }

        .review-option.approve.selected {
            background: var(--success-color);
        }

        .review-option.reject {
            border-color: var(--error-color);
        }

        .review-option.reject.selected {
            background: var(--error-color);
        }

        .review-comments {
            margin-top: 1rem;
        }

        .review-textarea {
            width: 100%;
            min-height: 80px;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            resize: vertical;
            font-family: inherit;
        }

        /* 统计信息 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--card-bg);
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid #E5E7EB;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 提示消息 */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #DCFCE7;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .alert-info {
            background: #DBEAFE;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .alert-warning {
            background: #FEF3C7;
            color: #92400E;
            border: 1px solid #FDE68A;
        }

        .alert-error {
            background: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .progress-steps {
                grid-template-columns: repeat(2, 1fr);
            }

            .expense-details {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .header-content {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            .review-options {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <h1 class="scenario-title">场景10：员工差旅费报销智能初审 (人机协作)</h1>
            <span class="scenario-badge">智能应用模块</span>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="container">
        <!-- 进度跟踪 -->
        <div class="progress-container">
            <div class="progress-header">
                <h2 class="progress-title">任务进度</h2>
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="step" id="step1">
                    <div class="step-circle">1</div>
                    <div class="step-label">获取报销单</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div class="step-label">智能初审</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div class="step-label">人工复审</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div class="step-label">审批完成</div>
                </div>
            </div>
        </div>

        <!-- 统计看板 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalCount">0</div>
                <div class="stat-label">待审核报销单</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="autoApprovedCount">0</div>
                <div class="stat-label">智能通过</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="manualReviewCount">0</div>
                <div class="stat-label">需人工审核</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="rejectedCount">0</div>
                <div class="stat-label">已拒绝</div>
            </div>
        </div>

        <!-- 步骤1: 获取报销单列表 -->
        <div class="operation-panel" id="fetchPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M6,2A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6M6,4H13V9H18V20H6V4Z" />
                </svg>
                步骤1: 获取待审核报销单
            </h3>
            <div class="action-buttons">
                <button class="btn btn-primary" id="fetchExpensesBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M4,6H20V8H4V6M4,11H20V13H4V11M4,16H20V18H4V16Z" />
                    </svg>
                    获取报销单列表
                </button>
            </div>
        </div>

        <!-- 步骤2: 报销单列表和智能审核 -->
        <div class="operation-panel hidden" id="auditPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                </svg>
                步骤2: 智能初审系统
            </h3>
            <div class="expense-list" id="expenseList"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="startAuditBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M12,6.5A2.5,2.5 0 0,1 14.5,4A2.5,2.5 0 0,1 17,6.5C17,8.24 15.16,10.5 12,16.5C8.84,10.5 7,8.24 7,6.5A2.5,2.5 0 0,1 9.5,4A2.5,2.5 0 0,1 12,6.5Z" />
                    </svg>
                    开始智能审核
                </button>
            </div>
        </div>

        <!-- 步骤3: 人工复审 -->
        <div class="operation-panel hidden" id="reviewPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                </svg>
                步骤3: 人工复审 (需要人工决策的报销单)
            </h3>
            <div id="manualReviewList"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="completeReviewBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                    </svg>
                    完成复审
                </button>
            </div>
        </div>

        <!-- 步骤4: 最终结果 -->
        <div class="operation-panel hidden" id="resultPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                </svg>
                步骤4: 审批完成统计
            </h3>
            <div id="finalResults"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="completeTaskBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                    </svg>
                    完成任务
                </button>
                <button class="btn btn-secondary" id="resetTaskBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                    </svg>
                    重置任务
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const state = {
            currentStep: 1,
            expenses: [],
            auditResults: [],
            manualReviews: [],
            completedSteps: [],
            stats: {
                total: 0,
                autoApproved: 0,
                manualReview: 0,
                rejected: 0
            },
            startTime: Date.now()
        };

        // 模拟报销单数据
        const mockExpenseData = [
            {
                id: "EXP2024080001",
                employeeName: "张三",
                department: "销售部",
                submissionDate: "2024-08-01",
                travelDestination: "上海",
                travelPurpose: "客户拜访",
                totalAmount: 2850.00,
                items: [
                    { type: "交通费", description: "北京-上海往返机票", amount: 1800.00, receipt: true },
                    { type: "住宿费", description: "酒店住宿2晚", amount: 800.00, receipt: true },
                    { type: "餐饮费", description: "商务餐饮", amount: 250.00, receipt: true }
                ]
            },
            {
                id: "EXP2024080002", 
                employeeName: "李四",
                department: "技术部",
                submissionDate: "2024-08-02",
                travelDestination: "深圳",
                travelPurpose: "技术交流",
                totalAmount: 4200.00,
                items: [
                    { type: "交通费", description: "北京-深圳往返机票", amount: 2400.00, receipt: true },
                    { type: "住宿费", description: "酒店住宿3晚", amount: 1200.00, receipt: true },
                    { type: "餐饮费", description: "会议用餐", amount: 400.00, receipt: false },
                    { type: "其他", description: "打车费用", amount: 200.00, receipt: false }
                ]
            },
            {
                id: "EXP2024080003",
                employeeName: "王五", 
                department: "市场部",
                submissionDate: "2024-08-03",
                travelDestination: "广州",
                travelPurpose: "市场调研",
                totalAmount: 5800.00,
                items: [
                    { type: "交通费", description: "商务舱机票", amount: 3200.00, receipt: true },
                    { type: "住宿费", description: "五星级酒店4晚", amount: 2000.00, receipt: true },
                    { type: "餐饮费", description: "高档餐厅用餐", amount: 600.00, receipt: true }
                ]
            },
            {
                id: "EXP2024080004",
                employeeName: "赵六",
                department: "财务部", 
                submissionDate: "2024-08-04",
                travelDestination: "杭州",
                travelPurpose: "财务培训",
                totalAmount: 1650.00,
                items: [
                    { type: "交通费", description: "高铁往返票", amount: 550.00, receipt: true },
                    { type: "住宿费", description: "经济型酒店2晚", amount: 400.00, receipt: true },
                    { type: "餐饮费", description: "培训期间用餐", amount: 300.00, receipt: true },
                    { type: "培训费", description: "课程费用", amount: 400.00, receipt: true }
                ]
            },
            {
                id: "EXP2024080005",
                employeeName: "钱七",
                department: "人力资源部",
                submissionDate: "2024-08-05", 
                travelDestination: "成都",
                travelPurpose: "招聘活动",
                totalAmount: 3100.00,
                items: [
                    { type: "交通费", description: "北京-成都往返机票", amount: 1600.00, receipt: true },
                    { type: "住宿费", description: "酒店住宿3晚", amount: 900.00, receipt: true },
                    { type: "餐饮费", description: "招聘期间用餐", amount: 350.00, receipt: true },
                    { type: "其他", description: "会场租赁费", amount: 250.00, receipt: false }
                ]
            }
        ];

        // 报销政策规则
        const expensePolicy = {
            transportation: {
                domestic: { 
                    economyClass: 2000,
                    businessClass: 4000  // 需特殊审批
                },
                taxi: { dailyLimit: 200 }
            },
            accommodation: {
                tier1Cities: { dailyLimit: 500 }, // 一线城市
                tier2Cities: { dailyLimit: 300 }, // 二线城市
                tier3Cities: { dailyLimit: 200 }  // 其他城市
            },
            meals: {
                dailyLimit: 150,
                businessMealLimit: 300
            },
            receiptRequired: {
                singleItemLimit: 100  // 单笔超过100元需要发票
            }
        };

        // DOM元素引用
        const elements = {
            fetchExpensesBtn: document.getElementById('fetchExpensesBtn'),
            auditPanel: document.getElementById('auditPanel'),
            expenseList: document.getElementById('expenseList'),
            startAuditBtn: document.getElementById('startAuditBtn'),
            reviewPanel: document.getElementById('reviewPanel'),
            manualReviewList: document.getElementById('manualReviewList'),
            completeReviewBtn: document.getElementById('completeReviewBtn'),
            resultPanel: document.getElementById('resultPanel'),
            finalResults: document.getElementById('finalResults'),
            completeTaskBtn: document.getElementById('completeTaskBtn'),
            resetTaskBtn: document.getElementById('resetTaskBtn'),
            progressFill: document.getElementById('progressFill'),
            progressPercentage: document.getElementById('progressPercentage'),
            totalCount: document.getElementById('totalCount'),
            autoApprovedCount: document.getElementById('autoApprovedCount'),
            manualReviewCount: document.getElementById('manualReviewCount'),
            rejectedCount: document.getElementById('rejectedCount')
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateStepDisplay();
            showAlert('info', '请点击获取待审核的差旅费报销单');
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            elements.fetchExpensesBtn.addEventListener('click', fetchExpenses);
            elements.startAuditBtn.addEventListener('click', startAudit);
            elements.completeReviewBtn.addEventListener('click', completeReview);
            elements.completeTaskBtn.addEventListener('click', completeTask);
            elements.resetTaskBtn.addEventListener('click', resetTask);
        }

        // 获取报销单
        function fetchExpenses() {
            elements.fetchExpensesBtn.disabled = true;
            elements.fetchExpensesBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    获取报销单中...
                </div>
            `;

            setTimeout(() => {
                state.expenses = [...mockExpenseData];
                state.completedSteps.push('fetch');
                state.stats.total = state.expenses.length;
                
                displayExpenses();
                updateStats();
                nextStep();
                showAlert('success', `成功获取 ${state.expenses.length} 份待审核报销单`);
            }, 2000);
        }

        // 显示报销单列表
        function displayExpenses() {
            elements.expenseList.innerHTML = '';
            
            state.expenses.forEach((expense, index) => {
                const expenseCard = document.createElement('div');
                expenseCard.className = 'expense-item';
                expenseCard.innerHTML = `
                    <div class="expense-header">
                        <div class="expense-id">${expense.id}</div>
                        <div class="expense-amount">¥${expense.totalAmount.toFixed(2)}</div>
                    </div>
                    <div class="expense-details">
                        <div class="detail-item">
                            <div class="detail-label">申请人</div>
                            <div class="detail-value">${expense.employeeName}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">部门</div>
                            <div class="detail-value">${expense.department}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">出差地点</div>
                            <div class="detail-value">${expense.travelDestination}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">出差事由</div>
                            <div class="detail-value">${expense.travelPurpose}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">提交日期</div>
                            <div class="detail-value">${expense.submissionDate}</div>
                        </div>
                    </div>
                    <div class="expense-breakdown">
                        <table class="breakdown-table">
                            <thead>
                                <tr>
                                    <th>费用类型</th>
                                    <th>费用说明</th>
                                    <th>金额</th>
                                    <th>发票</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${expense.items.map(item => `
                                    <tr>
                                        <td>${item.type}</td>
                                        <td>${item.description}</td>
                                        <td>¥${item.amount.toFixed(2)}</td>
                                        <td>${item.receipt ? '✓' : '✗'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
                elements.expenseList.appendChild(expenseCard);
            });

            elements.startAuditBtn.disabled = false;
            elements.auditPanel.classList.remove('hidden');
        }

        // 开始智能审核
        function startAudit() {
            elements.startAuditBtn.disabled = true;
            elements.startAuditBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    智能审核中...
                </div>
            `;

            setTimeout(() => {
                performIntelligentAudit();
                state.completedSteps.push('audit');
                displayAuditResults();
                updateStats();
                nextStep();
                showAlert('success', '智能审核完成，请处理需人工复审的报销单');
            }, 3000);
        }

        // 执行智能审核
        function performIntelligentAudit() {
            state.auditResults = state.expenses.map(expense => {
                const auditResult = {
                    ...expense,
                    auditStatus: 'pending',
                    warnings: [],
                    recommendation: 'approve'
                };

                // 检查各项规则
                let totalWarnings = 0;
                let criticalViolations = 0;

                // 检查交通费
                expense.items.forEach(item => {
                    if (item.type === '交通费') {
                        if (item.description.includes('商务舱') && item.amount > expensePolicy.transportation.domestic.businessClass) {
                            auditResult.warnings.push(`${item.description} 超出商务舱标准，需特殊审批`);
                            criticalViolations++;
                        }
                        if (item.description.includes('机票') && item.amount > expensePolicy.transportation.domestic.economyClass && !item.description.includes('商务舱')) {
                            auditResult.warnings.push(`${item.description} 金额较高，请确认是否合理`);
                            totalWarnings++;
                        }
                    }

                    // 检查住宿费
                    if (item.type === '住宿费') {
                        const dailyRate = item.amount / getTravelDays(expense);
                        const cityTier = getCityTier(expense.travelDestination);
                        const limit = expensePolicy.accommodation[cityTier].dailyLimit;
                        
                        if (dailyRate > limit * 1.5) {
                            auditResult.warnings.push(`住宿费用 ¥${dailyRate.toFixed(0)}/天 显著超标 (限额: ¥${limit}/天)`);
                            criticalViolations++;
                        } else if (dailyRate > limit) {
                            auditResult.warnings.push(`住宿费用 ¥${dailyRate.toFixed(0)}/天 超出标准 (限额: ¥${limit}/天)`);
                            totalWarnings++;
                        }
                    }

                    // 检查餐饮费
                    if (item.type === '餐饮费') {
                        if (item.amount > expensePolicy.meals.businessMealLimit) {
                            auditResult.warnings.push(`${item.description} 金额过高，请确认是否商务用餐`);
                            totalWarnings++;
                        }
                    }

                    // 检查发票
                    if (item.amount > expensePolicy.receiptRequired.singleItemLimit && !item.receipt) {
                        auditResult.warnings.push(`${item.description} 金额超过 ¥${expensePolicy.receiptRequired.singleItemLimit}，缺少发票`);
                        criticalViolations++;
                    }
                });

                // 决定审核结果
                if (criticalViolations > 0) {
                    auditResult.auditStatus = 'review';  // 需要人工复审
                    auditResult.recommendation = 'review';
                } else if (totalWarnings > 2) {
                    auditResult.auditStatus = 'review';  // 警告过多，需人工审核
                    auditResult.recommendation = 'review';
                } else if (totalWarnings > 0) {
                    auditResult.auditStatus = 'approved';  // 有轻微问题但可通过
                    auditResult.recommendation = 'approve_with_notes';
                } else {
                    auditResult.auditStatus = 'approved';  // 完全合规
                    auditResult.recommendation = 'approve';
                }

                return auditResult;
            });
        }

        // 显示审核结果
        function displayAuditResults() {
            elements.expenseList.innerHTML = '';
            
            state.auditResults.forEach((result, index) => {
                const statusClass = result.auditStatus === 'approved' ? 'audit-approved' : 
                                  result.auditStatus === 'rejected' ? 'audit-rejected' : 'audit-review';
                const statusText = result.auditStatus === 'approved' ? '✓ 智能通过' :
                                 result.auditStatus === 'rejected' ? '✗ 自动拒绝' : '⚠ 需人工审核';

                const expenseCard = document.createElement('div');
                expenseCard.className = 'expense-item';
                expenseCard.innerHTML = `
                    <div class="expense-header">
                        <div class="expense-id">${result.id}</div>
                        <div class="expense-amount">¥${result.totalAmount.toFixed(2)}</div>
                    </div>
                    <div class="expense-details">
                        <div class="detail-item">
                            <div class="detail-label">申请人</div>
                            <div class="detail-value">${result.employeeName}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">部门</div>
                            <div class="detail-value">${result.department}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">出差地点</div>
                            <div class="detail-value">${result.travelDestination}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">审核状态</div>
                            <div class="detail-value">
                                <span class="status-badge status-${result.auditStatus}">${statusText}</span>
                            </div>
                        </div>
                    </div>
                    <div class="audit-result ${statusClass}">
                        <strong>智能审核结果: ${statusText}</strong>
                        ${result.warnings.length > 0 ? `
                            <ul class="audit-warnings">
                                ${result.warnings.map(warning => `<li>${warning}</li>`).join('')}
                            </ul>
                        ` : '<p>所有项目均符合公司政策标准</p>'}
                    </div>
                `;
                elements.expenseList.appendChild(expenseCard);
            });

            // 准备需要人工审核的项目
            state.manualReviews = state.auditResults.filter(r => r.auditStatus === 'review');
            
            if (state.manualReviews.length > 0) {
                displayManualReviewItems();
                elements.reviewPanel.classList.remove('hidden');
            } else {
                // 如果没有需要人工审核的，直接跳到结果
                skipToResults();
            }
        }

        // 显示需要人工审核的项目
        function displayManualReviewItems() {
            elements.manualReviewList.innerHTML = '';
            
            state.manualReviews.forEach((expense, index) => {
                const reviewCard = document.createElement('div');
                reviewCard.className = 'expense-item';
                reviewCard.innerHTML = `
                    <div class="expense-header">
                        <div class="expense-id">${expense.id} - ${expense.employeeName}</div>
                        <div class="expense-amount">¥${expense.totalAmount.toFixed(2)}</div>
                    </div>
                    <div class="audit-result audit-review">
                        <strong>⚠ 需要人工决策</strong>
                        <ul class="audit-warnings">
                            ${expense.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="manual-review">
                        <div class="review-header">
                            <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                            </svg>
                            请做出审批决定
                        </div>
                        <div class="review-options">
                            <div class="review-option approve" onclick="selectReviewOption('${expense.id}', 'approve', this)">
                                <div>✓ 批准</div>
                                <small>接受此报销申请</small>
                            </div>
                            <div class="review-option reject" onclick="selectReviewOption('${expense.id}', 'reject', this)">
                                <div>✗ 拒绝</div>
                                <small>拒绝此报销申请</small>
                            </div>
                        </div>
                        <div class="review-comments">
                            <label for="comments-${expense.id}">审批意见:</label>
                            <textarea class="review-textarea" id="comments-${expense.id}" placeholder="请输入审批意见（可选）"></textarea>
                        </div>
                    </div>
                `;
                elements.manualReviewList.appendChild(reviewCard);
            });
        }

        // 选择审核选项
        function selectReviewOption(expenseId, decision, element) {
            // 移除同组其他选项的选中状态
            const parent = element.parentNode;
            parent.querySelectorAll('.review-option').forEach(opt => opt.classList.remove('selected'));
            
            // 选中当前选项
            element.classList.add('selected');
            
            // 保存决定
            const expense = state.manualReviews.find(e => e.id === expenseId);
            if (expense) {
                expense.manualDecision = decision;
                expense.reviewComments = document.getElementById(`comments-${expenseId}`).value;
            }

            // 检查是否所有项目都已决定
            const allDecided = state.manualReviews.every(e => e.manualDecision);
            elements.completeReviewBtn.disabled = !allDecided;
        }

        // 完成人工复审
        function completeReview() {
            // 更新最终状态
            state.manualReviews.forEach(expense => {
                expense.finalStatus = expense.manualDecision === 'approve' ? 'approved' : 'rejected';
                expense.reviewComments = document.getElementById(`comments-${expense.id}`).value || '';
            });

            state.completedSteps.push('review');
            updateStats();
            displayFinalResults();
            nextStep();
            showAlert('success', '人工复审完成！');
        }

        // 跳过人工审核直接到结果
        function skipToResults() {
            state.completedSteps.push('review');
            updateStats();
            displayFinalResults();
            nextStep();
        }

        // 显示最终结果
        function displayFinalResults() {
            const approvedItems = [];
            const rejectedItems = [];

            // 统计自动审核通过的
            state.auditResults.forEach(result => {
                if (result.auditStatus === 'approved') {
                    approvedItems.push({ ...result, finalStatus: 'approved', reviewType: '智能审核' });
                }
            });

            // 统计人工审核的
            state.manualReviews.forEach(result => {
                if (result.finalStatus === 'approved') {
                    approvedItems.push({ ...result, reviewType: '人工审核' });
                } else if (result.finalStatus === 'rejected') {
                    rejectedItems.push({ ...result, reviewType: '人工审核' });
                }
            });

            const totalApproved = approvedItems.reduce((sum, item) => sum + item.totalAmount, 0);
            const totalRejected = rejectedItems.reduce((sum, item) => sum + item.totalAmount, 0);

            elements.finalResults.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">${approvedItems.length}</div>
                        <div class="stat-label">批准报销单</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥${totalApproved.toFixed(2)}</div>
                        <div class="stat-label">批准金额</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${rejectedItems.length}</div>
                        <div class="stat-label">拒绝报销单</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">¥${totalRejected.toFixed(2)}</div>
                        <div class="stat-label">拒绝金额</div>
                    </div>
                </div>
                
                <div class="result-card">
                    <h4>审批汇总报告</h4>
                    <div class="audit-result audit-approved">
                        <strong>✓ 批准的报销单 (${approvedItems.length}份)</strong>
                        <div style="margin-top: 1rem;">
                            ${approvedItems.map(item => `
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>${item.id} - ${item.employeeName}</span>
                                    <span>¥${item.totalAmount.toFixed(2)} (${item.reviewType})</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    ${rejectedItems.length > 0 ? `
                        <div class="audit-result audit-rejected" style="margin-top: 1rem;">
                            <strong>✗ 拒绝的报销单 (${rejectedItems.length}份)</strong>
                            <div style="margin-top: 1rem;">
                                ${rejectedItems.map(item => `
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <span>${item.id} - ${item.employeeName}</span>
                                        <span>¥${item.totalAmount.toFixed(2)} (${item.reviewType})</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;

            elements.completeTaskBtn.disabled = false;
            elements.resultPanel.classList.remove('hidden');
        }

        // 更新统计数据
        function updateStats() {
            const autoApproved = state.auditResults.filter(r => r.auditStatus === 'approved').length;
            const needsReview = state.auditResults.filter(r => r.auditStatus === 'review').length;
            const manualRejected = state.manualReviews.filter(r => r.finalStatus === 'rejected').length;

            state.stats.autoApproved = autoApproved;
            state.stats.manualReview = needsReview;
            state.stats.rejected = manualRejected;

            elements.totalCount.textContent = state.stats.total;
            elements.autoApprovedCount.textContent = state.stats.autoApproved;
            elements.manualReviewCount.textContent = state.stats.manualReview;
            elements.rejectedCount.textContent = state.stats.rejected;
        }

        // 完成任务
        function completeTask() {
            const taskData = {
                completedSteps: state.completedSteps,
                totalExpenses: state.expenses.length,
                autoApprovedCount: state.stats.autoApproved,
                manualReviewCount: state.stats.manualReview,
                rejectedCount: state.stats.rejected,
                processingAccuracy: calculateAccuracy(),
                averageProcessingTime: Math.floor((Date.now() - state.startTime) / state.expenses.length / 1000),
                humanMachineCollaboration: state.manualReviews.length > 0,
                timeSpent: Math.floor((Date.now() - state.startTime) / 1000),
                taskCompleted: true
            };

            submitTaskValidation(taskData);
        }

        // 计算处理准确率
        function calculateAccuracy() {
            // 简化的准确率计算：基于是否完成所有步骤和人机协作效果
            const baseAccuracy = 0.8;
            const completionBonus = state.completedSteps.length >= 3 ? 0.15 : 0;
            const collaborationBonus = state.manualReviews.length > 0 ? 0.05 : 0;
            
            return Math.min(baseAccuracy + completionBonus + collaborationBonus, 1.0);
        }

        // 提交任务验证
        function submitTaskValidation(taskData) {
            fetch('/api/scenarios/10/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(taskData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.valid) {
                    showAlert('success', `🎉 ${data.feedback} 得分: ${data.score}分`);
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', data.feedback || '任务验证未通过，请检查操作步骤');
                }
            })
            .catch(error => {
                console.error('任务验证失败:', error);
                // 本地验证逻辑
                if (validateLocalTask(taskData)) {
                    showAlert('success', '🎉 恭喜！您已成功完成差旅费报销智能初审任务！');
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', '任务验证未通过，请确保完成所有步骤');
                }
            });
        }

        // 本地任务验证
        function validateLocalTask(data) {
            const requiredSteps = ['fetch', 'audit', 'review'];
            const stepsCompleted = requiredSteps.every(step => 
                data.completedSteps && data.completedSteps.includes(step)
            );
            
            return stepsCompleted && 
                   data.totalExpenses >= 3 && 
                   data.processingAccuracy >= 0.8 &&
                   (data.autoApprovedCount + data.manualReviewCount + data.rejectedCount) === data.totalExpenses;
        }

        // 重置任务
        function resetTask() {
            if (confirm('确定要重置任务吗？所有进度将丢失。')) {
                location.reload();
            }
        }

        // 步骤管理
        function nextStep() {
            if (state.currentStep < 4) {
                state.currentStep++;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            const progress = (state.currentStep - 1) * 25;
            elements.progressFill.style.width = `${progress}%`;
            elements.progressPercentage.textContent = `${progress}%`;

            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                
                if (i < state.currentStep) {
                    step.classList.add('completed');
                } else if (i === state.currentStep) {
                    step.classList.add('active');
                }
            }
        }

        // 工具函数
        function getTravelDays(expense) {
            // 简化计算，基于住宿费推算天数
            const accommodation = expense.items.find(item => item.type === '住宿费');
            return accommodation ? Math.max(Math.round(accommodation.amount / 400), 1) : 1;
        }

        function getCityTier(city) {
            const tier1Cities = ['北京', '上海', '广州', '深圳'];
            const tier2Cities = ['杭州', '成都', '武汉', '南京', '西安'];
            
            if (tier1Cities.includes(city)) return 'tier1Cities';
            if (tier2Cities.includes(city)) return 'tier2Cities';
            return 'tier3Cities';
        }

        // 显示提示消息
        function showAlert(type, message) {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                </svg>
                ${message}
            `;

            document.querySelector('.container').insertBefore(alert, document.querySelector('.progress-container'));

            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // 暴露全局函数供HTML调用
        window.selectReviewOption = selectReviewOption;
    </script>
</body>
</html>