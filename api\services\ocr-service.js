/**
 * OCR识别服务 - 重构版
 * 基于适配器模式，支持多厂商OCR服务无缝切换
 * 支持：豆包、百度、腾讯云、阿里云、Mock模式
 */

const ocrAdapterManager = require('./ocr-adapter-manager');

class OCRService {
    constructor() {
        this.adapterManager = ocrAdapterManager;
        console.log('🚀 OCR服务初始化完成 - 多厂商适配器架构');
    }

    /**
     * 设置首选OCR适配器
     * @param {string} adapterName - 适配器名称 (doubao, baidu, tencent, aliyun, mock)
     */
    setPreferredAdapter(adapterName) {
        this.preferredAdapter = adapterName;
        console.log(`🎯 设置首选OCR适配器: ${adapterName.toUpperCase()}`);
    }

    /**
     * 识别增值税发票
     * @param {Buffer|string} imageData - 图片数据或base64字符串
     * @param {string} filename - 文件名
     * @returns {Object} 识别结果
     */
    async recognizeInvoice(imageData, filename = '') {
        return await this.adapterManager.recognizeInvoice(
            imageData, 
            filename, 
            this.preferredAdapter
        );
    }

    /**
     * 通用OCR识别
     * @param {Buffer|string} imageData - 图片数据
     * @param {string} filename - 文件名
     * @returns {Object} 识别结果
     */
    async recognizeGeneral(imageData, filename = '') {
        return await this.adapterManager.recognizeGeneral(
            imageData, 
            filename, 
            this.preferredAdapter
        );
    }

    /**
     * 批量识别发票
     * @param {Array} imageDataList - 图片数据数组
     * @returns {Array} 识别结果数组
     */
    async batchRecognizeInvoices(imageDataList) {
        return await this.adapterManager.batchRecognizeInvoices(
            imageDataList, 
            this.preferredAdapter
        );
    }

    /**
     * 获取所有适配器状态
     * @returns {Object} 适配器状态信息
     */
    getAdaptersStatus() {
        return this.adapterManager.getAdaptersStatus();
    }

    /**
     * 获取最佳可用适配器
     * @returns {Object} 适配器信息
     */
    getBestAdapter() {
        const adapter = this.adapterManager.getBestAdapter();
        return adapter ? adapter.getInfo() : null;
    }

    /**
     * 启用/禁用特定适配器
     * @param {string} adapterName - 适配器名称
     * @param {boolean} enabled - 是否启用
     */
    setAdapterEnabled(adapterName, enabled) {
        this.adapterManager.setAdapterEnabled(adapterName, enabled);
    }

    /**
     * 验证OCR识别结果
     * @param {Object} ocrResult - OCR识别结果
     * @returns {Object} 验证结果
     */
    validateOCRResult(ocrResult) {
        return this.adapterManager.validateOCRResult(ocrResult);
    }

    /**
     * 动态添加新的OCR适配器
     * @param {Object} adapter - 适配器实例
     */
    addAdapter(adapter) {
        this.adapterManager.addAdapter(adapter);
    }
}

module.exports = new OCRService();