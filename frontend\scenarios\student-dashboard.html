<!DOCTYPE html>
<html lang="zh-CN">  
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生门户 - RPA财务机器人实训平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #F9FAFB;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(92,123,255,0.25);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .logo p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .progress-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .progress-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            text-align: center;
        }
        
        .progress-card .number {
            font-size: 36px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 10px;
        }
        
        .progress-card .label {
            color: #8D99AE;
            font-size: 14px;
        }
        
        .scenarios-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .section-header {
            padding: 25px 30px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .section-header h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .section-header p {
            color: #8D99AE;
        }
        
        .module-tabs {
            display: flex;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
        }
        
        .tab-btn.active {
            background: white;
            color: #7673FF;
            border-bottom: 3px solid #7673FF;
            box-shadow: 0 2px 8px rgba(118,115,255,0.25);
        }
        
        .tab-btn:hover:not(.active) {
            background: #f3f4f6;
        }
        
        .module-content {
            padding: 30px;
        }
        
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .scenario-card {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.3s;
            background: white;
        }
        
        .scenario-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(118,115,255,0.15);
            border-color: #7673FF;
        }
        
        .scenario-card.completed {
            border-color: #10b981;
        }
        
        .scenario-card.in-progress {
            border-color: #f59e0b;
        }
        
        .card-header {
            padding: 20px;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .card-header.completed {
            background: #ecfdf5;
        }
        
        .card-header.in-progress {
            background: #fffbeb;
        }
        
        .scenario-title {
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }
        
        .scenario-meta {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: rgba(255,255,255,0.9);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-not-started {
            background: #f3f4f6;
            color: #374151;
        }
        
        .status-in-progress {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;  
        }
        
        .card-body {
            padding: 20px;
        }
        
        .scenario-description {
            color: #8D99AE;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .scenario-progress {
            margin-bottom: 15px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #7673FF;
            transition: width 0.3s;
        }
        
        .progress-text {
            font-size: 12px;
            color: #8D99AE;
            margin-top: 5px;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118,115,255,0.3);
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118,115,255,0.4);
        }
        
        .btn-success {
            background: #7673FF;
            color: white;
            box-shadow: 0 4px 12px rgba(118,115,255,0.3);
            border-radius: 8px;
        }
        
        .btn-success:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(118,115,255,0.4);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #7673FF;
            color: #7673FF;
        }
        
        .btn-outline:hover {
            background: rgba(118,115,255,0.1);
            border-color: #6366F1;
            color: #6366F1;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #8D99AE;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🤖 RPA实训平台</h1>
                <p>财务机器人入门与进阶实训平台</p>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">👤</div>
                <div>
                    <div id="userName">加载中...</div>
                    <div style="font-size: 12px; opacity: 0.8;" id="userClass">学生</div>
                </div>
                <button class="logout-btn" id="logoutBtn">退出登录</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
            <h2>欢迎回来！<span id="welcomeName"></span></h2>
            <p>继续您的RPA财务机器人学习之旅，掌握现代财务自动化技能</p>
        </div>

        <!-- 进度概览 -->
        <div class="progress-overview">
            <div class="progress-card">
                <div class="number" id="completedScenarios">0</div>
                <div class="label">已完成场景</div>
            </div>
            <div class="progress-card">
                <div class="number" id="totalScenarios">12</div>
                <div class="label">总场景数</div>
            </div>
            <div class="progress-card">
                <div class="number" id="averageScore">0</div>
                <div class="label">平均得分</div>
            </div>
            <div class="progress-card">
                <div class="number" id="completionRate">0%</div>
                <div class="label">完成率</div>
            </div>
        </div>

        <!-- 场景列表 -->
        <div class="scenarios-section">
            <div class="section-header">
                <h2>训练场景</h2>
                <p>按照教材顺序完成12个财务RPA场景，循序渐进掌握自动化技能</p>
            </div>

            <!-- 模块标签 -->
            <div class="module-tabs">
                <button class="tab-btn active" data-module="1">第一模块：基础操作</button>
                <button class="tab-btn" data-module="2">第二模块：流程整合</button>
                <button class="tab-btn" data-module="3">第三模块：智能应用</button>
            </div>

            <!-- 场景内容 -->
            <div class="module-content">
                <div class="loading" id="scenariosLoading">
                    <div class="spinner"></div>
                    正在加载场景数据...
                </div>
                
                <div class="scenario-grid" id="scenarioGrid">
                    <!-- 场景卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        class StudentDashboard {
            constructor() {
                this.currentModule = 1;
                this.scenarios = {};
                this.user = null;
                
                this.init();
            }

            async init() {
                // 检查登录状态
                if (!this.checkAuth()) {
                    return;
                }

                // 绑定事件
                this.bindEvents();
                
                // 加载用户信息
                await this.loadUserInfo();
                
                // 加载场景数据
                await this.loadScenarios();
            }

            checkAuth() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                if (!token || !user) {
                    window.location.href = 'login.html';
                    return false;
                }
                
                try {
                    this.user = JSON.parse(user);
                    if (this.user.role !== 'student') {
                        window.location.href = 'login.html';
                        return false;
                    }
                } catch (error) {
                    window.location.href = 'login.html';
                    return false;
                }
                
                return true;
            }

            bindEvents() {
                // 模块标签切换
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const module = parseInt(e.target.dataset.module);
                        this.switchModule(module);
                    });
                });

                // 退出登录
                document.getElementById('logoutBtn').addEventListener('click', () => {
                    this.logout();
                });
            }

            async loadUserInfo() {
                if (this.user) {
                    document.getElementById('userName').textContent = this.user.name;
                    document.getElementById('welcomeName').textContent = this.user.name;
                    document.getElementById('userClass').textContent = `学号: ${this.user.studentId}`;
                    document.getElementById('userAvatar').textContent = this.user.name.charAt(0);
                }
            }

            async loadScenarios() {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch('/api/scenarios', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (!response.ok) {
                        if (response.status === 401) {
                            this.logout();
                            return;
                        }
                        throw new Error('Failed to load scenarios');
                    }

                    const result = await response.json();
                    if (result.success) {
                        this.scenarios = result.data.scenarios;
                        this.updateProgressOverview(result.data);
                        this.renderScenarios();
                    }
                } catch (error) {
                    console.error('加载场景失败:', error);
                    document.getElementById('scenariosLoading').innerHTML = 
                        '<div style="color: #ef4444;">加载场景失败，请刷新页面重试</div>';
                }
            }

            updateProgressOverview(data) {
                // 计算总体进度
                const allScenarios = Object.values(data.scenarios).flat();
                const completed = allScenarios.filter(s => s.progress.status === 'completed').length;
                const total = allScenarios.length;
                const avgScore = allScenarios
                    .filter(s => s.progress.score !== null)
                    .reduce((sum, s, _, arr) => sum + (s.progress.score || 0) / arr.length, 0);

                document.getElementById('completedScenarios').textContent = completed;
                document.getElementById('totalScenarios').textContent = total;
                document.getElementById('averageScore').textContent = avgScore > 0 ? avgScore.toFixed(1) : '0';
                document.getElementById('completionRate').textContent = 
                    total > 0 ? (completed / total * 100).toFixed(1) + '%' : '0%';
            }

            renderScenarios() {
                const grid = document.getElementById('scenarioGrid');
                const loading = document.getElementById('scenariosLoading');
                
                loading.classList.add('hidden');
                
                // 渲染当前模块的场景
                const moduleScenarios = this.scenarios[`module${this.currentModule}`] || [];
                
                grid.innerHTML = moduleScenarios.map(scenario => this.createScenarioCard(scenario)).join('');
            }

            createScenarioCard(scenario) {
                const progress = scenario.progress || {};
                const status = progress.status || 'not_started';
                const progressPercent = progress.progress || 0;
                const score = progress.score;
                
                const statusMap = {
                    'not_started': { label: '未开始', class: 'not-started' },
                    'in_progress': { label: '进行中', class: 'in-progress' },
                    'completed': { label: '已完成', class: 'completed' }
                };
                
                const statusInfo = statusMap[status];
                const cardClass = status === 'completed' ? 'completed' : (status === 'in_progress' ? 'in-progress' : '');
                
                return `
                    <div class="scenario-card ${cardClass}">
                        <div class="card-header ${status}">
                            <div>
                                <div class="scenario-title">场景${scenario.id}：${scenario.title}</div>
                                <div class="scenario-meta">
                                    <span>📊 ${scenario.difficulty}</span>
                                    <span>⏱️ ${scenario.estimated_time}分钟</span>
                                </div>
                            </div>
                            <div class="status-badge status-${statusInfo.class}">
                                ${statusInfo.label}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="scenario-description">
                                ${scenario.description}
                            </div>
                            <div class="scenario-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progressPercent}%"></div>
                                </div>
                                <div class="progress-text">
                                    进度: ${progressPercent}% ${score ? `| 得分: ${score}分` : ''}
                                </div>
                            </div>
                            <div class="card-actions">
                                ${status === 'not_started' ? 
                                    `<a href="login.html?scenario=${scenario.id}" class="btn btn-primary">开始学习</a>` :
                                    `<a href="login.html?scenario=${scenario.id}" class="btn btn-success">继续学习</a>`
                                }
                                <button class="btn btn-outline" onclick="this.showScenarioDetails(${scenario.id})">查看详情</button>
                            </div>
                        </div>
                    </div>
                `;
            }

            switchModule(moduleNumber) {
                this.currentModule = moduleNumber;
                
                // 更新标签样式
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-module="${moduleNumber}"]`).classList.add('active');
                
                // 重新渲染场景
                this.renderScenarios();
            }

            showScenarioDetails(scenarioId) {
                // 显示场景详情模态框（简化实现）
                const allScenarios = Object.values(this.scenarios).flat();
                const scenario = allScenarios.find(s => s.id === scenarioId);
                
                if (scenario) {
                    alert(`场景${scenarioId}：${scenario.title}\n\n${scenario.description}\n\n难度：${scenario.difficulty}\n预计时间：${scenario.estimated_time}分钟`);
                }
            }

            logout() {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new StudentDashboard();
        });
    </script>
</body>
</html>