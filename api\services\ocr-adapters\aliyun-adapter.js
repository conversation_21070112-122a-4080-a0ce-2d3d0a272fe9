/**
 * 阿里云OCR适配器
 * 基于阿里云OCR服务
 */

const axios = require('axios');
const crypto = require('crypto');
const BaseOCRAdapter = require('./base-adapter');

class AliyunOCRAdapter extends BaseOCRAdapter {
    constructor(config = {}) {
        super(config);
        this.name = 'aliyun';
        this.priority = 4; // 第四优先级
        this.enabled = true;
        
        this.accessKeyId = config.accessKeyId || process.env.ALIYUN_ACCESS_KEY_ID;
        this.accessKeySecret = config.accessKeySecret || process.env.ALIYUN_ACCESS_KEY_SECRET;
        this.endpoint = config.endpoint || 'https://ocr-api.cn-hangzhou.aliyuncs.com';
    }

    /**
     * 检查阿里云适配器是否可用
     */
    isAvailable() {
        return !!(this.accessKeyId && this.accessKeySecret && 
                 this.accessKeyId.trim() !== '' && this.accessKeySecret.trim() !== '');
    }

    /**
     * 获取支持的功能
     */
    getFeatures() {
        return [
            '增值税发票识别',
            '通用文字识别',
            '表格识别',
            '证件识别',
            '票据识别',
            '行驶证识别'
        ];
    }

    /**
     * 阿里云发票OCR识别
     */
    async recognizeInvoice(imageData, filename) {
        try {
            console.log(`🔷 阿里云OCR识别发票: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('阿里云OCR未配置密钥');
            }

            const base64Image = this.preprocessImage(imageData);
            
            const params = {
                Action: 'RecognizeVATInvoice',
                Version: '2019-12-30',
                ImageURL: `data:image/jpeg;base64,${base64Image}`,
                OutputFigureTextContents: true
            };

            const result = await this.callAliyunAPI(params);
            return this.parseInvoiceResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 阿里云OCR识别失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 阿里云通用OCR识别
     */
    async recognizeGeneral(imageData, filename) {
        try {
            console.log(`🔷 阿里云通用OCR识别: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('阿里云OCR未配置密钥');
            }

            const base64Image = this.preprocessImage(imageData);
            
            const params = {
                Action: 'RecognizeCharacter',
                Version: '2019-12-30',
                ImageURL: `data:image/jpeg;base64,${base64Image}`,
                MinSize: 16,
                OutputProbability: true
            };

            const result = await this.callAliyunAPI(params);
            return this.parseGeneralResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 阿里云通用OCR失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 调用阿里云API
     */
    async callAliyunAPI(params) {
        const timestamp = new Date().toISOString();
        const nonce = Math.random().toString(36).substring(2);
        
        // 构建签名参数
        const signParams = {
            ...params,
            AccessKeyId: this.accessKeyId,
            SignatureMethod: 'HMAC-SHA1',
            SignatureNonce: nonce,
            SignatureVersion: '1.0',
            Timestamp: timestamp,
            Format: 'JSON'
        };

        // 生成签名
        const signature = this.generateAliyunSignature(signParams);
        signParams.Signature = signature;

        const response = await axios.post(this.endpoint, null, {
            params: signParams,
            timeout: 30000
        });

        if (response.data.Code && response.data.Code !== '200') {
            throw new Error(`阿里云API错误: ${response.data.Message}`);
        }

        return response.data.Data || response.data;
    }

    /**
     * 生成阿里云API签名
     */
    generateAliyunSignature(params) {
        // 排序参数
        const sortedKeys = Object.keys(params).sort();
        const sortedParams = sortedKeys.map(key => 
            `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
        ).join('&');

        const stringToSign = `POST&${encodeURIComponent('/')}&${encodeURIComponent(sortedParams)}`;
        const signature = crypto
            .createHmac('sha1', `${this.accessKeySecret}&`)
            .update(stringToSign, 'utf8')
            .digest('base64');

        return signature;
    }

    /**
     * 解析阿里云发票识别结果
     */
    parseInvoiceResult(aliyunResult, filename) {
        const content = aliyunResult.Content || {};
        const outputs = content.Outputs || [];
        
        // 提取发票信息
        const invoiceInfo = this.extractAliyunInvoiceFields(outputs);
        
        const standardizedData = {
            invoiceNumber: invoiceInfo.invoiceNumber || '',
            invoiceCode: invoiceInfo.invoiceCode || '',
            date: invoiceInfo.date || '',
            sellerName: invoiceInfo.sellerName || '',
            sellerTaxId: invoiceInfo.sellerTaxId || '',
            sellerAddress: invoiceInfo.sellerAddress || '',
            sellerPhone: invoiceInfo.sellerPhone || '',
            buyerName: invoiceInfo.buyerName || '',
            buyerTaxId: invoiceInfo.buyerTaxId || '',
            buyerAddress: invoiceInfo.buyerAddress || '',
            buyerPhone: invoiceInfo.buyerPhone || '',
            totalAmount: this.parseAmount(invoiceInfo.totalAmount),
            amountWithoutTax: this.parseAmount(invoiceInfo.amountWithoutTax),
            taxAmount: this.parseAmount(invoiceInfo.taxAmount),
            items: invoiceInfo.items || [],
            remarks: invoiceInfo.remarks || '',
            checkCode: invoiceInfo.checkCode || ''
        };

        const result = this.standardizeResult({
            data: standardizedData,
            confidence: 0.86,
            rawData: aliyunResult
        }, filename, 'aliyun');

        console.log(`✅ 阿里云发票识别成功: ${standardizedData.invoiceNumber}`);
        return result;
    }

    /**
     * 解析阿里云通用OCR结果
     */
    parseGeneralResult(aliyunResult, filename) {
        const content = aliyunResult.Content || {};
        const outputs = content.Outputs || [];
        
        const lines = outputs.map(item => item.OutputText || '').filter(text => text.trim());
        
        return this.standardizeResult({
            data: {
                text: lines.join('\n'),
                lines: lines,
                lineCount: lines.length
            },
            confidence: 0.85,
            rawData: aliyunResult
        }, filename, 'aliyun');
    }

    /**
     * 提取阿里云发票字段
     */
    extractAliyunInvoiceFields(outputs) {
        const invoiceInfo = {
            items: []
        };

        outputs.forEach(output => {
            const text = output.OutputText || '';
            const fieldType = output.OutputLabel || '';
            
            // 根据字段类型映射到标准字段
            switch (fieldType) {
                case 'invoiceNumber':
                    invoiceInfo.invoiceNumber = text;
                    break;
                case 'invoiceCode':
                    invoiceInfo.invoiceCode = text;
                    break;
                case 'invoiceDate':
                    invoiceInfo.date = text;
                    break;
                case 'sellerName':
                    invoiceInfo.sellerName = text;
                    break;
                case 'sellerTaxpayerID':
                    invoiceInfo.sellerTaxId = text;
                    break;
                case 'buyerName':
                    invoiceInfo.buyerName = text;
                    break;
                case 'buyerTaxpayerID':
                    invoiceInfo.buyerTaxId = text;
                    break;
                case 'totalAmount':
                    invoiceInfo.totalAmount = text;
                    break;
                case 'sumAmount':
                    invoiceInfo.amountWithoutTax = text;
                    break;
                case 'taxAmount':
                    invoiceInfo.taxAmount = text;
                    break;
                case 'checkCode':
                    invoiceInfo.checkCode = text;
                    break;
                case 'itemName':
                    // 处理商品信息
                    invoiceInfo.items.push({
                        id: invoiceInfo.items.length + 1,
                        name: text,
                        quantity: 1,
                        unitPrice: 0,
                        amount: 0
                    });
                    break;
            }
        });

        return invoiceInfo;
    }
}

module.exports = AliyunOCRAdapter;