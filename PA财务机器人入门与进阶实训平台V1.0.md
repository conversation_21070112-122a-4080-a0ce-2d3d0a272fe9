# RPA财务机器人实训平台 PRD V1.0

> **文档状态**：草案
> **版本**：1.1 
> **更新日期**：2025年8月3日
> **负责人**：[chanwarmsun]
> **预算**：0-20元/月
> **开发周期**：3个月
> **团队规模**：1人（教师）+ AI辅助开发

## 0. 📋 项目基本信息

### 0.1 项目概要
| 项目名称 | RPA财务机器人入门与进阶实训平台 |
| --- | --- |
| 项目代号 | RPA-EduPlatform |
| 项目类型 | 教育技术平台 |
| 目标用户 | 中高职财经专业学生及教师 |
| 预期用户量 | 500-1000学生，20-50教师 |
| 项目周期 | 2025.8-2025.11（3个月） |
| 维护周期 | 长期维护（3年） |

### 0.2 成本效益分析
| 项目 | 成本估算 | 效益评估 |
| --- | --- | --- |
| 开发成本 | 0元（自主开发） | 配套教材销售增值 |
| 运营成本 | 0-20元/月 | 教学效率提升50% |
| 维护成本 | 5小时/月 | 学生实践能力提升 |
| 总投入 | <1000元/年 | 教材推广价值 |

### 0.3 竞品分析
| 竞品 | 优势 | 劣势 | 差异化 |
| --- | --- | --- | --- |
| UiPath Academy | 官方权威 | 英文界面，通用性强 | 专为中文财务场景定制 |
| 实习僧RPA | 商业化成熟 | 收费高，场景有限 | 完全免费，教材同步 |
| 各大厂商培训 | 技术先进 | 门槛高，缺乏教学设计 | 教学导向，循序渐进 |

## 1. 🎯 文档目的

本文档旨在明确“RPA财务机器人入门与进阶实训平台”V1.1版本的核心目标、功能范围、技术架构与发展规划。它将作为产品开发、测试和上线的统一指导依据，确保平台能以极低的成本和维护精力，为《影刀RPA实战应用》教材提供一个全面、稳定、可扩展的线上实践环境。

## 2. 🚀 产品概述

### 2.1. 项目背景

为配合本人编著的《RPA财务机器人入门与进阶》教材，解决传统RPA学习中因真实业务系统多变、数据敏感、操作不可逆等因素导致的练习困难。本项目旨在为学生提供一个稳定、安全、可重复练习的线上“靶场”环境。

### 2.2. 问题陈述

* **对于学生**：缺少一个与教材案例100%一致的练习环境，难以判断问题是源于自身操作还是环境变化。
* **对于教师**：难以量化追踪学生的实操练习过程与结果，无法高效地对自动化流程作业进行统一评价与反馈。

### 2.3. 产品定位与价值

* **产品定位**：一个为影刀RPA教学量身定制的、稳定、可控、带反馈的线上“RPA靶场”。
* **核心价值**：
    * **对学生**：提供与教材完全同步、永不变更的练习靶场，让学生聚焦于RPA技术本身，所学即所练。
    * **对教师**：提供一个轻量级的教学管理后台，实现学生练习进度的自动化追踪与结果验证，极大提升教学效率。

### 2.4. 用户画像

* **主要用户（学生）**：正在学习《RPA财务机器人入门与进阶》教材的财经、管理等专业的中高职在校学生。
* **次要用户（教师）**：使用该教材进行RPA教学的授课教师。

## 3. 📝 V1.1 功能范围与场景设计

**核心原则**：集中资源跑通“教学→实践→验证”的核心业务闭环，并用12个精心设计的场景全面覆盖中职会计专业所需的核心RPA技能。

### 3.1. 系统模块

| 模块 | 功能点 | V1.1 是否包含 | 备注 |
| :--- | :--- | :--- | :--- |
| **学生端-靶场系统** | 用户注册/登录 | ✅ | 简单的学号/密码登录。 |
| | 靶场场景选择页面 | ✅ | 以列表或卡片形式展示全部12个场景，并标记完成状态。 |
| | **卡片跳转功能** | ✅ | 点击场景卡片跳转到对应的登录页面，然后进入网银仿真系统。 |
| | **第一模块：基础操作** | ✅ | 包含场景1至4。 |
| | **第二模块：流程整合** | ✅ | 包含场景5至8。 |
| | **第三模块：智能应用** | ✅ | 包含场景9至12。 |
| | 任务校验与即时反馈 | ✅ | 通过API校验关键结果，成功后页面实时显示"任务完成"提示。 |
| **教师端-管理后台** | 学生管理 | ✅ | 支持CSV/Excel批量导入学生名单，自动生成账号和初始密码。 |
| | 任务进度看板 | ✅ | 以矩阵表格形式实时展示“学生-任务”的完成状态（✅/❌/未开始）和完成时间。 |
| | 学生任务重置 | ✅ | 刚需功能，可一键重置指定学生某个任务的进度，方便其重新练习。 |
| | 任务发布/编辑 | ❌ | V1.1任务写死在代码里，后台暂不提供管理功能。 |
| | 作业详情查看/评价 | ❌ | V1.1只看结果，不看过程。 |
| **通用** | 与影刀RPA的交互 | ✅ | 通过网页元素定位（核心）和API调用（验证结果）两种方式。 |

### 3.2. 靶场场景详述 (12个)

#### 第一模块：基础操作与数据搬运

* **场景1：企业网银流水查询下载**
    * **任务**：登录模拟网银，处理分页，抓取所有流水数据存为Excel。
    * **教学点**：登录、数据抓取、分页处理。
    * **用户流程**：学生在主界面点击场景卡片 → 跳转到登录页面 → 进入网银仿真系统 → 完成操作任务。

* **场景2：批量开具电子发票**
    * **任务**：从Excel读取订单信息，在“开票系统”网页中为每笔订单开具发票。
    * **教学点**：Excel读取、循环、网页表单填写。

* **场景3：固定资产卡片信息核对**
    * **任务**：根据Excel清单，在“固资系统”查询并核对资产信息，将结果写回Excel。
    * **教学点**：数据双向同步、条件比对。

* **场景4：税务申报期提醒与状态核查**
    * **任务**：登录“电子税务局”，抓取本月申报日历，并核查上月各税种申报状态。
    * **教学点**：信息抓取、特定文本查找。

#### 第二模块：跨系统流程整合

* **场景5：应收账款对账与核销**
    * **任务**：整合“销售系统”订单和“银行流水”脏数据Excel，匹配成功后在“财务系统”中核销。
    * **教学点**：跨应用操作、数据清洗、模糊匹配。

* **场景6：增值税进项税发票认证**
    * **任务**：从CSV文件读取发票信息，在“增值税平台”进行批量勾选认证。
    * **教学点**：CSV读取、多层菜单导航、复选框操作。

* **场景7：月度工资条数据计算与生成**
    * **任务**：整合“考勤表”和“绩效表”，根据规则计算薪酬，为每人生成独立工资条文件。
    * **教学点**：多数据源整合、数据计算、文件批量生成。

* **场景8：财务报表数据自动汇总**
    * **任务**：登录多个“子公司系统”，抓取关键财务数据，汇总到“集团合并报表”模板中。
    * **教学点**：模块化设计、数据聚合。

#### 第三模块：智能审核与综合应用

* **场景9：供应商发票批量验真与入账 (OCR)**
    * **任务**：用OCR识别发票图片，提取信息录入系统验真，成功后生成会计分录。
    * **教学点**：OCR应用、非结构化数据处理、API调用。

* **场景10：员工差旅费报销智能初审 (人机协作)**
    * **任务**：根据报销制度（规则引擎）对报销单进行预审，合规通过，不合规或存疑则转交人工。
    * **教学点**：复杂业务逻辑、规则判断、人机交互。

* **场景11：合同关键信息提取与印花税计算申报 (OCR)**
    * **任务**：用OCR识别合同PDF，提取金额、类型等，根据税率表计算印花税并填报。
    * **教学点**：PDF处理、OCR、查表匹配、税务计算。

* **场景12：自动生成总账科目余额调节表**
    * **任务**：分别获取银行对账单和系统日记账余额，结合未达账项清单，自动编制余额调节表。
    * **教学点**：端到端流程、复杂数据比对、财务调节逻辑。

## 4. 🛠️ 技术架构与非功能性要求

### 4.1. 推荐技术栈（极致性价比方案）

* **前端**：`Vue.js` + `Element Plus`。
* **前端部署**：**静态网站托管**。国内推荐 `Gitee Pages` (免费) 或 `腾讯云COS`/`阿里云OSS` (有免费额度)；追求高性能可选用 `Cloudflare Pages`。
* **后端 & 数据库**：**BaaS一体化后端 (Backend as a Service)**。
    * **首选**：**LeanCloud (国内版)**。其“开发版”实例永久免费，集成了数据库、云函数和文件存储，极大地简化了开发和运维。
    * **备选**：**腾讯云云开发 (TCB)**。同样提供一体化能力和慷慨的免费额度。
* **开发语言**：前端 `JavaScript` / `TypeScript`，后端（云函数）`Node.js`。

### 4.2. 成本预估

* **目标月成本**：**0 ~ 20元**。
* **说明**：在项目初期和中期，利用BaaS平台和静态托管的免费额度，成本完全可以控制在0元。只有当学生规模和使用频率非常高时，才可能产生少量费用。这与最初预估的300-400元/月相比，是质的飞跃。

### 4.3. 非功能性要求

* **稳定性**：靶场页面的UI元素（ID, XPath等）和API接口在V1.1生命周期内绝不变更。
* **可用性**：保证99.9%的可用性，尤其是上课和考试期间。
* **响应速度**：API平均响应时间 < 200ms。
* **安全性**：对用户密码进行加密存储；做好基本的Web安全措施，如防止SQL注入（BaaS平台通常会提供基础防护）。

### 4.4. 设计规范与配色方案

#### 4.4.1. 薄暮天空配色系统

**设计理念**：采用薄暮天空般的柔和渐变，营造宁静、专业且富有科技感的学习环境。

**1. 主色调：薄暮天空渐变**
- **色彩构成**：从左侧柔和的亮蓝色 (#5C7BFF) 平滑过渡到右侧优雅的淡紫色 (#A069FF)
- **视觉感受**：如薄暮时分天空的色彩，宁静、广阔且富有层次，既保留蓝色的科技与信赖感，又融入紫色的创造力与高级感
- **应用场景**：顶部导航栏、功能卡片头部，构成页面的视觉框架

**2. 交互色：清晰柔和的引导**
- **色彩构成**：从主渐变色中提取的饱和度适中的蓝紫色 (#7673FF)
- **视觉感受**：足够醒目引导用户操作，但不刺眼，与主渐变色调完美融合
- **应用场景**：按钮、关键链接等所有交互元素

**3. 中性色：专业易读的基石**
- **主文字**：深蓝灰色 (#2c3e50) - 比纯黑色更柔和，阅读舒适
- **辅助文字**：柔和的灰蓝色 (#8D99AE) - 用于描述性文本，形成清晰层次
- **背景**：极浅的灰色 (#F9FAFB) 搭配纯白色卡片 (#FFFFFF)，创造干净明亮的空间

#### 4.4.2. 交互反馈规范

- **按钮悬停**：颜色加深至 #6366F1，轻微上移 1-2px，增强阴影效果
- **卡片悬停**：上移 4px，添加 #7673FF 色调的柔和阴影
- **焦点状态**：使用 rgba(118,115,255,0.15) 的柔和光晕效果

## 5. ⚖️ 法律合规与风险管控

### 5.1. 项目合规定位

* **项目性质**：**教学研究项目**，非商业经营性质
* **法律身份**：作为高校教师开展的教学改革与学术研究项目
* **收益模式**：通过教材版权获得收益，平台本身完全免费开放

### 5.2. 风险识别与规避策略

#### 5.2.1. 知识产权风险
* **风险点**：仿真系统可能侵犯真实财务系统的界面设计权
* **规避措施**：
    * 所有仿真界面采用完全原创设计，避免模仿任何真实系统UI
    * 系统命名使用通用描述（如"练习银行系统"、"模拟开票平台"）
    * 避免使用真实企业名称、Logo或标识
    * 所有数据完全虚构，不使用真实企业信息

#### 5.2.2. 教育合规风险
* **风险点**：平台涉及教学可能需要相关资质
* **规避措施**：
    * 明确定位为"教材配套练习工具"，非独立教育平台
    * 平台显著位置声明"仅供《RPA财务机器人入门与进阶》教材配套使用"
    * 不向学生收取任何平台使用费用
    * 申请学校教学改革项目支持，获得学术背书

#### 5.2.3. 数据安全与隐私保护
* **风险点**：学生信息收集与使用的合规性
* **规避措施**：
    * 最小化数据收集原则（仅收集学号、姓名等基本信息）
    * 明确数据使用范围仅限教学目的
    * 设定数据保存期限（建议不超过3年）
    * 制定并公示隐私保护政策

#### 5.2.4. 平台责任风险
* **风险点**：平台故障对教学造成影响的责任认定
* **规避措施**：
    * 用户协议明确声明"按现状提供服务，不保证100%可用性"
    * 提供详细的平台使用指南和故障处理手册
    * 建立多渠道用户反馈机制（QQ群、邮箱等）
    * 保持版本更新日志，展示持续改进努力

### 5.3. 开源协作策略

#### 5.3.1. 开源许可
* **许可证选择**：采用MIT开源许可证
* **代码托管**：在GitHub/Gitee公开源代码
* **文档开放**：技术文档和使用手册完全开放

#### 5.3.2. 教育共同体建设
* **合作模式**：邀请其他院校教师共同参与维护
* **学术交流**：发表相关教学研究论文，增强学术属性
* **经验分享**：组织教学研讨会，分享平台使用经验

### 5.4. 免责声明与用户协议

#### 5.4.1. 关键免责条款
* **教育用途声明**："本平台仅供RPA教学练习使用，所有模拟数据请勿用于真实业务"
* **技术限制说明**："平台基于免费云服务构建，可能存在响应延迟或短暂不可用"
* **内容准确性**："虚拟场景仅为教学设计，不代表真实业务流程"

#### 5.4.2. 用户行为规范
* 禁止将平台用于非教学目的
* 禁止恶意攻击或破坏平台功能
* 禁止传播平台账号给非授权用户

### 5.5. 学校支持与备案

#### 5.5.1. 项目备案
* **教务处备案**：作为教学改革项目向学校教务处报备
* **科研处登记**：可申请校级教学研究项目支持
* **版权登记**：申请软件著作权保护

#### 5.5.2. 学术支撑
* **论文发表**：撰写"RPA教学平台设计与应用"相关论文
* **会议交流**：在教育技术或会计教育会议上分享经验
* **同行评议**：邀请同行专家对平台进行学术评估

## 6. 🏗️ 系统架构设计

### 6.1 总体架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   学生端(Vue)   │    │   教师端(Vue)    │    │  靶场页面(HTML) │
│   Element Plus  │    │   Element Plus   │    │   模拟系统界面  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                      API Gateway (Express.js)                     │
├─────────────────────────────────┼─────────────────────────────────┤
│ 认证中间件 | 限流中间件 | 日志中间件 | 错误处理中间件                │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                       业务服务层                                   │
├──────────────────┬──────────────┬──────────────┬─────────────────┤
│   用户服务       │   场景服务   │   任务服务   │    文件服务     │
│ UserService      │ScenarioService│ TaskService  │   FileService   │
└──────────────────┴──────────────┴──────────────┴─────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                       数据持久层                                   │
├──────────────────┬──────────────┬──────────────┬─────────────────┤
│     MySQL        │     Redis    │   File Store │   Tencent COS   │
│   关系数据库     │    缓存      │   本地文件   │    云存储       │
└──────────────────┴──────────────┴──────────────┴─────────────────┘
```

### 6.2 数据库设计

#### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('student', 'teacher') NOT NULL,
    real_name VARCHAR(100),
    class_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 班级表
CREATE TABLE classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    teacher_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 场景表
CREATE TABLE scenarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    module_id INT,
    difficulty_level INT,
    estimated_time INT,
    validation_rules JSON
);

-- 任务完成记录表
CREATE TABLE task_completions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    scenario_id INT,
    status ENUM('not_started', 'in_progress', 'completed', 'failed'),
    completion_time TIMESTAMP NULL,
    submission_data JSON,
    score INT DEFAULT 0
);
```

### 6.3 API接口规范

#### 认证接口
```
POST /api/auth/login
请求: {"username": "string", "password": "string"}
响应: {"token": "jwt_token", "user": {...}}

POST /api/auth/logout
请求: Bearer Token
响应: {"message": "success"}
```

#### 场景接口
```
GET /api/scenarios
响应: [{"id": 1, "title": "...", "status": "completed"}]

POST /api/scenarios/{id}/validate
请求: {"data": "validation_data"}
响应: {"valid": true, "score": 100, "feedback": "..."}
```

### 6.4 前端组件架构
```
src/
├── components/
│   ├── common/          # 通用组件
│   │   ├── Header.vue
│   │   ├── Sidebar.vue
│   │   └── Loading.vue
│   ├── student/         # 学生端组件
│   │   ├── ScenarioCard.vue
│   │   ├── ProgressBar.vue
│   │   └── TaskResult.vue
│   └── teacher/         # 教师端组件
│       ├── StudentList.vue
│       ├── ProgressMatrix.vue
│       └── ClassManager.vue
├── views/
│   ├── student/
│   └── teacher/
├── stores/              # Pinia状态管理
├── utils/               # 工具函数
└── router/              # 路由配置
```

## 7. 🧪 测试策略

### 7.1 测试分层
| 测试类型 | 覆盖范围 | 工具 | 目标覆盖率 |
| --- | --- | --- | --- |
| 单元测试 | 业务逻辑函数 | Jest | 80% |
| 集成测试 | API接口 | Supertest | 100% |
| E2E测试 | 关键用户路径 | Cypress | 核心流程 |
| 性能测试 | 并发场景 | Artillery | 500用户 |

### 7.2 测试环境
- 开发环境：本地MySQL + Redis
- 测试环境：Docker容器化部署
- 生产环境：腾讯云服务器

## 8. 🚀 部署运维方案

### 8.1 部署架构
```
腾讯云轻量应用服务器
├── Nginx (反向代理)
├── Docker Compose
│   ├── Frontend (Vue打包后的静态文件)
│   ├── Backend (Node.js服务)
│   ├── MySQL (数据库)
│   └── Redis (缓存)
└── SSL证书 (HTTPS)
```

### 8.2 监控方案
- 服务监控：PM2进程管理
- 日志监控：Winston + 日志文件
- 性能监控：内置性能统计
- 错误监控：Sentry (可选)

### 8.3 备份策略
- 数据库：每日自动备份到COS
- 代码：Git版本控制
- 配置：环境变量管理

## 9. 🗺️ 开发与上线路线图 (Roadmap)

* **第一阶段：核心闭环开发**
    * **任务**：
        1.  选定并注册BaaS平台（如LeanCloud）。
        2.  搭建前后端项目框架。
        3.  完成数据库设计（用户表、任务完成状态表）。
        4.  **最高优先级**：开发“场景1：企业网银流水查询下载”的靶场页面和校验API。
        5.  开发学生登录功能。
    * **目标**：您自己能以学生身份，参照教材，用影刀RPA完整跑通场景一并获得“任务完成”的反馈。

* **第二阶段：内容填充与后台完善**
    * **任务**：
        1.  依次开发并上线剩余的11个靶场场景。
        2.  开发教师后台的三个核心功能（学生管理、进度看板、任务重置）。
        3.  邀请几位学生作为种子用户进行测试，收集反馈。
    * **目标**：平台功能稳定，教学内容完整，可以支撑一个班级的完整教学使用。

* **第三阶段：正式上线与持续迭代**
    * **任务**：
        1.  配合教材正式销售，向所有购书学生开放平台。
        2.  建立用户反馈渠道（如QQ群）。
        3.  根据反馈，开始规划V1.2版本，修复BUG或优化体验。
    * **目标**：产品进入良性迭代循环，持续为教学创造价值。