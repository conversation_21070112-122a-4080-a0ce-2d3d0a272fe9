/**
 * Mock OCR适配器
 * 用于演示和测试，无需真实API
 */

const BaseOCRAdapter = require('./base-adapter');

class MockOCRAdapter extends BaseOCRAdapter {
    constructor(config = {}) {
        super(config);
        this.name = 'mock';
        this.priority = 99; // 最低优先级，兜底方案
        this.enabled = true;
    }

    /**
     * Mock适配器始终可用
     */
    isAvailable() {
        return true;
    }

    /**
     * 获取支持的功能
     */
    getFeatures() {
        return [
            '完整功能演示',
            '无需配置',
            '快速响应',
            '多样化数据',
            '稳定可靠'
        ];
    }

    /**
     * Mock发票OCR识别
     */
    async recognizeInvoice(imageData, filename) {
        try {
            console.log(`🎭 Mock OCR识别发票: ${filename}`);
            
            // 模拟API调用延时
            await this.simulateApiDelay();
            
            const mockResult = this.generateMockInvoiceData(filename);
            
            console.log(`✅ Mock发票识别完成: ${mockResult.data.invoiceNumber}`);
            return mockResult;
            
        } catch (error) {
            return this.standardizeError(error, filename);
        }
    }

    /**
     * Mock通用OCR识别
     */
    async recognizeGeneral(imageData, filename) {
        try {
            console.log(`🎭 Mock通用OCR识别: ${filename}`);
            
            await this.simulateApiDelay();
            
            const mockResult = this.generateMockGeneralData(filename);
            
            console.log(`✅ Mock通用OCR完成: ${mockResult.data.lineCount}行文本`);
            return mockResult;
            
        } catch (error) {
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 生成Mock发票数据
     */
    generateMockInvoiceData(filename) {
        const mockInvoices = [
            {
                invoiceNumber: '12345678',
                invoiceCode: '144031551110',
                date: '2024-08-05',
                sellerName: '北京科技有限公司',
                sellerTaxId: '91110108MA01234567',
                sellerAddress: '北京市海淀区中关村大街1号',
                sellerPhone: '010-88888888',
                buyerName: '上海商贸有限公司',
                buyerTaxId: '91310000MA9876543X',
                buyerAddress: '上海市浦东新区陆家嘴金融区',
                buyerPhone: '021-66666666',
                totalAmount: 11300.00,
                amountWithoutTax: 10000.00,
                taxAmount: 1300.00,
                items: [
                    { id: 1, name: '办公设备', quantity: 2, unitPrice: 3000.00, amount: 6000.00 },
                    { id: 2, name: '软件服务', quantity: 1, unitPrice: 4000.00, amount: 4000.00 }
                ],
                remarks: '质保期一年',
                checkCode: '12345',
                confidence: 0.95
            },
            {
                invoiceNumber: '87654321',
                invoiceCode: '144031551111',
                date: '2024-08-04',
                sellerName: '深圳电子科技公司',
                sellerTaxId: '91440300MA5432109Y',
                sellerAddress: '深圳市南山区科技园南区',
                sellerPhone: '0755-12345678',
                buyerName: '广州贸易集团',
                buyerTaxId: '91440101MA0987654Z',
                buyerAddress: '广州市天河区珠江新城',
                buyerPhone: '020-87654321',
                totalAmount: 5650.00,
                amountWithoutTax: 5000.00,
                taxAmount: 650.00,
                items: [
                    { id: 1, name: '电子产品', quantity: 5, unitPrice: 1000.00, amount: 5000.00 }
                ],
                remarks: '货到付款',
                checkCode: '54321',
                confidence: 0.88
            },
            {
                invoiceNumber: '11223344',
                invoiceCode: '144031551112',
                date: '2024-08-03',
                sellerName: '杭州互联网科技股份有限公司',
                sellerTaxId: '91330100MA28ABCD12',
                sellerAddress: '杭州市西湖区文三路508号',
                sellerPhone: '0571-87654321',
                buyerName: '南京智能制造有限公司',
                buyerTaxId: '91320100MA1XYZE543',
                buyerAddress: '南京市江北新区研创园',
                buyerPhone: '025-12345678',
                totalAmount: 8480.00,
                amountWithoutTax: 7500.00,
                taxAmount: 980.00,
                items: [
                    { id: 1, name: '云服务器', quantity: 3, unitPrice: 1500.00, amount: 4500.00 },
                    { id: 2, name: '数据库服务', quantity: 1, unitPrice: 3000.00, amount: 3000.00 }
                ],
                remarks: '年度服务费',
                checkCode: '67890',
                confidence: 0.92
            }
        ];

        // 根据文件名或随机选择一个Mock数据
        let selectedInvoice;
        if (filename && filename.includes('001')) {
            selectedInvoice = mockInvoices[0];
        } else if (filename && filename.includes('002')) {
            selectedInvoice = mockInvoices[1];
        } else if (filename && filename.includes('003')) {
            selectedInvoice = mockInvoices[2];
        } else {
            selectedInvoice = mockInvoices[Math.floor(Math.random() * mockInvoices.length)];
        }

        const confidence = selectedInvoice.confidence;
        delete selectedInvoice.confidence;

        return this.standardizeResult({
            data: selectedInvoice,
            confidence: confidence
        }, filename, 'mock');
    }

    /**
     * 生成Mock通用OCR数据
     */
    generateMockGeneralData(filename) {
        const mockTexts = [
            {
                text: '这是一个示例文档内容\n包含多行文字识别结果\n用于演示通用OCR功能\n支持中英文混合识别\nSupports mixed Chinese and English recognition',
                lines: [
                    '这是一个示例文档内容',
                    '包含多行文字识别结果',
                    '用于演示通用OCR功能',
                    '支持中英文混合识别',
                    'Supports mixed Chinese and English recognition'
                ],
                confidence: 0.85
            },
            {
                text: '合同编号：HT-2024-001\n甲方：北京科技有限公司\n乙方：上海商贸有限公司\n签约日期：2024年8月5日\n合同金额：￥50,000.00',
                lines: [
                    '合同编号：HT-2024-001',
                    '甲方：北京科技有限公司',
                    '乙方：上海商贸有限公司',
                    '签约日期：2024年8月5日',
                    '合同金额：￥50,000.00'
                ],
                confidence: 0.90
            },
            {
                text: '产品说明书\n型号：RPA-3000\n功能特性：\n1. 智能识别\n2. 自动化处理\n3. 数据分析\n注意事项：请按说明使用',
                lines: [
                    '产品说明书',
                    '型号：RPA-3000',
                    '功能特性：',
                    '1. 智能识别',
                    '2. 自动化处理',
                    '3. 数据分析',
                    '注意事项：请按说明使用'
                ],
                confidence: 0.88
            }
        ];

        const selectedText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
        const confidence = selectedText.confidence;
        delete selectedText.confidence;

        selectedText.lineCount = selectedText.lines.length;

        return this.standardizeResult({
            data: selectedText,
            confidence: confidence
        }, filename, 'mock');
    }

    /**
     * 模拟API调用延时
     */
    async simulateApiDelay() {
        const delay = 200 + Math.random() * 800; // 200-1000ms随机延时
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * 获取Mock适配器的特殊信息
     */
    getInfo() {
        const baseInfo = super.getInfo();
        return {
            ...baseInfo,
            description: '演示模式，无需API配置',
            dataTypes: ['标准发票', '电商发票', '服务发票', '合同文档', '说明书'],
            responseTime: '200-1000ms',
            reliability: '100%'
        };
    }
}

module.exports = MockOCRAdapter;