const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME 类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

// 创建简单的HTTP服务器
const server = http.createServer((req, res) => {
  console.log(`请求: ${req.method} ${req.url}`);
  
  // 解析URL
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;
  
  // 处理根路径，重定向到学生登录页面
  if (pathname === '/') {
    pathname = '/frontend/scenarios/login.html';
  }
  
  // 构建文件路径
  let filePath = path.join(__dirname, pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回404
      res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html lang="zh">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>页面未找到 - RPA实训平台</title>
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    display: flex; 
                    justify-content: center; 
                    align-items: center; 
                    height: 100vh; 
                    margin: 0;
                    background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
                    color: white;
                }
                .error-container {
                    text-align: center;
                    background: rgba(255,255,255,0.1);
                    padding: 2rem;
                    border-radius: 1rem;
                    backdrop-filter: blur(10px);
                }
                h1 { font-size: 3rem; margin-bottom: 1rem; }
                p { font-size: 1.2rem; margin-bottom: 2rem; }
                .nav-links a {
                    color: white;
                    text-decoration: none;
                    margin: 0 1rem;
                    padding: 0.5rem 1rem;
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                }
                .nav-links a:hover {
                    background: rgba(255,255,255,0.2);
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>404</h1>
                <p>页面未找到</p>
                <div class="nav-links">
                    <a href="/frontend/scenarios/login.html">学生登录</a>
                    <a href="/frontend/scenarios/teacher-login.html">教师登录</a>
                    <a href="/frontend/scenarios/student-dashboard.html">学生Dashboard</a>
                </div>
            </div>
        </body>
        </html>
      `);
      return;
    }
    
    // 检查是否是目录
    fs.stat(filePath, (err, stats) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('服务器错误');
        return;
      }
      
      if (stats.isDirectory()) {
        // 如果是目录，尝试找index.html
        filePath = path.join(filePath, 'index.html');
      }
      
      // 读取并返回文件
      fs.readFile(filePath, (err, data) => {
        if (err) {
          res.writeHead(500, { 'Content-Type': 'text/plain' });
          res.end('服务器错误');
          return;
        }
        
        // 设置正确的MIME类型
        const ext = path.extname(filePath).toLowerCase();
        const mimeType = mimeTypes[ext] || 'application/octet-stream';
        
        res.writeHead(200, { 
          'Content-Type': mimeType + (mimeType.startsWith('text/') ? '; charset=utf-8' : ''),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end(data);
      });
    });
  });
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log('🚀 RPA财务机器人实训平台已启动！');
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log('');
  console.log('🎯 快速访问链接:');
  console.log(`   学生登录: http://localhost:${PORT}/frontend/scenarios/login.html`);
  console.log(`   教师登录: http://localhost:${PORT}/frontend/scenarios/teacher-login.html`);
  console.log(`   学生Dashboard: http://localhost:${PORT}/frontend/scenarios/student-dashboard.html`);
  console.log('');
  console.log('💡 提示: 按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭RPA实训平台服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});