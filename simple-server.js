/**
 * 最简服务器 - 纯静态文件服务
 * 当npm环境有问题时的备用方案
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon'
};

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  // 处理根路径
  if (req.url === '/') {
    req.url = '/scenarios/login.html';
  }
  
  // 移除查询参数
  const urlPath = req.url.split('?')[0];
  
  // 构建文件路径
  let filePath = path.join(__dirname, 'frontend', urlPath);
  
  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    // 尝试添加.html扩展名
    if (!path.extname(filePath)) {
      filePath += '.html';
    }
  }
  
  // 如果还是不存在，返回404
  if (!fs.existsSync(filePath)) {
    res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <html>
        <head><title>404 - 页面未找到</title></head>
        <body>
          <h1>404 - 页面未找到</h1>
          <p>请求的页面 ${req.url} 不存在</p>
          <p><a href="/scenarios/login.html">返回登录页</a></p>
        </body>
      </html>
    `);
    return;
  }
  
  // 获取文件扩展名和对应的MIME类型
  const ext = path.extname(filePath);
  const mimeType = mimeTypes[ext] || 'text/plain';
  
  // 读取并返回文件
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end('<h1>500 - 服务器错误</h1>');
      return;
    }
    
    res.writeHead(200, { 
      'Content-Type': mimeType + '; charset=utf-8',
      'Access-Control-Allow-Origin': '*'
    });
    res.end(data);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 简化版RPA教学平台已启动`);
  console.log(`📍 访问地址: http://localhost:${PORT}`);
  console.log(`📚 直接访问: http://localhost:${PORT}/scenarios/student-dashboard.html`);
  console.log(`⚠️  注意: 这是纯静态版本，不包含后端API功能`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});