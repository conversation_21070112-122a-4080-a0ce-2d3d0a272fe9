<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景12：自动生成总账科目余额调节表 - RPA财务机器人实训平台</title>
    
    <!-- 样式引入 -->
    <style>
        /* 薄暮天空配色系统 */
        :root {
            --primary-gradient: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            --primary-color: #7673FF;
            --primary-light: rgba(118, 115, 255, 0.15);
            --text-primary: #2c3e50;
            --text-secondary: #8D99AE;
            --bg-color: #F9FAFB;
            --card-bg: #FFFFFF;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --info-color: #3B82F6;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 10px 15px -3px rgba(118, 115, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .header {
            background: var(--primary-gradient);
            padding: 1rem 2rem;
            color: white;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scenario-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .scenario-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* 进度条 */
        .progress-container {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: #E5E7EB;
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .step {
            text-align: center;
            padding: 0.5rem;
        }

        .step-circle {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #E5E7EB;
            color: #6B7280;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 操作面板 */
        .operation-panel {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .operation-panel:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .panel-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--primary-color);
        }

        /* 数据源选择器 */
        .data-source-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .data-source-card {
            background: #F8FAFC;
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .data-source-card:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: translateY(-2px);
        }

        .data-source-card.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .data-source-icon {
            width: 3rem;
            height: 3rem;
            fill: var(--primary-color);
            margin: 0 auto 1rem;
        }

        .data-source-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .data-source-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* 数据表格 */
        .data-table-container {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .table-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .table-info {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: #F3F4F6;
            font-weight: 600;
            color: var(--text-primary);
        }

        .data-table tbody tr:hover {
            background: #F9FAFB;
        }

        .amount-cell {
            text-align: right;
            font-weight: 500;
        }

        .amount-positive {
            color: var(--success-color);
        }

        .amount-negative {
            color: var(--error-color);
        }

        /* 调节表 */
        .reconciliation-table {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }

        .reconciliation-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
        }

        .reconciliation-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .reconciliation-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .reconciliation-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .reconciliation-section {
            background: #F8FAFC;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }

        .section-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .balance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px dashed #E5E7EB;
        }

        .balance-item:last-child {
            border-bottom: none;
            font-weight: 600;
            background: var(--primary-light);
            margin: 0.5rem -1.5rem -1.5rem;
            padding: 1rem 1.5rem;
            border-radius: 0 0 0.5rem 0.5rem;
        }

        .balance-label {
            color: var(--text-primary);
        }

        .balance-amount {
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        /* 差异分析 */
        .difference-analysis {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .difference-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #92400E;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .difference-list {
            display: grid;
            gap: 0.75rem;
        }

        .difference-item {
            background: #FFFBEB;
            border-radius: 0.5rem;
            padding: 1rem;
            border-left: 4px solid #F59E0B;
        }

        .difference-desc {
            font-weight: 500;
            color: #92400E;
            margin-bottom: 0.25rem;
        }

        .difference-amount {
            font-size: 0.875rem;
            color: #A16207;
            font-family: 'Courier New', monospace;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid #E5E7EB;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 提示消息 */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #DCFCE7;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .alert-info {
            background: #DBEAFE;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .alert-warning {
            background: #FEF3C7;
            color: #92400E;
            border: 1px solid #FDE68A;
        }

        .alert-error {
            background: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .progress-steps {
                grid-template-columns: repeat(2, 1fr);
            }

            .data-source-grid {
                grid-template-columns: 1fr;
            }

            .reconciliation-content {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .header-content {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }

            .data-table {
                font-size: 0.75rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <h1 class="scenario-title">场景12：自动生成总账科目余额调节表</h1>
            <span class="scenario-badge">智能应用模块</span>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="container">
        <!-- 进度跟踪 -->
        <div class="progress-container">
            <div class="progress-header">
                <h2 class="progress-title">任务进度</h2>
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="step" id="step1">
                    <div class="step-circle">1</div>
                    <div class="step-label">获取账单数据</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div class="step-label">数据比对分析</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div class="step-label">生成调节表</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div class="step-label">差异核查</div>
                </div>
            </div>
        </div>

        <!-- 步骤1: 数据源选择 -->
        <div class="operation-panel" id="dataSourcePanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M4,6H20V8H4V6M4,11H20V13H4V11M4,16H20V18H4V16Z" />
                </svg>
                步骤1: 选择数据源
            </h3>
            <div class="data-source-grid">
                <div class="data-source-card" onclick="selectDataSource('bank')" id="bankSource">
                    <svg class="data-source-icon" viewBox="0 0 24 24">
                        <path d="M11.5,1L2,6V8H21V6M16,10V17H19V19H5V17H8V10H10V17H14V10M2,22H21V20H2V22Z" />
                    </svg>
                    <div class="data-source-title">银行对账单</div>
                    <div class="data-source-desc">获取银行实际流水和余额</div>
                </div>
                <div class="data-source-card" onclick="selectDataSource('ledger')" id="ledgerSource">
                    <svg class="data-source-icon" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                    <div class="data-source-title">银行日记账</div>
                    <div class="data-source-desc">获取企业记账系统的银行账余额</div>
                </div>
                <div class="data-source-card" onclick="selectDataSource('outstanding')" id="outstandingSource">
                    <svg class="data-source-icon" viewBox="0 0 24 24">
                        <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H17.5V16H6.5V17.5M6.5,13H17.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z" />
                    </svg>
                    <div class="data-source-title">未达账项清单</div>
                    <div class="data-source-desc">获取银行和企业记录时间差异的项目</div>
                </div>
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="fetchDataBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M4,6H20V8H4V6M4,11H20V13H4V11M4,16H20V18H4V16Z" />
                    </svg>
                    获取数据
                </button>
            </div>
        </div>

        <!-- 步骤2: 数据展示 -->
        <div class="operation-panel hidden" id="dataDisplayPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z" />
                </svg>
                步骤2: 数据获取结果
            </h3>
            <div id="dataDisplay"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="analyzeDataBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M1,11H3.17C3.58,9.83 4.69,9 6,9C6.65,9 7.25,9.21 7.74,9.56L9.44,7.86C9.16,7.35 9,6.77 9,6.14C9,4.95 9.95,4 11.14,4C12,4 12.75,4.5 13.09,5.25L15.92,4.63C15.97,4.42 16,4.21 16,4C16,2.89 16.89,2 18,2A2,2 0 0,1 20,4A2,2 0 0,1 18,6C17.58,6 17.19,5.85 16.88,5.61L14.05,6.23C14.03,6.31 14,6.4 14,6.5C14,7.69 13.05,8.64 11.86,8.64C11,8.64 10.27,8.15 9.92,7.39L8.22,9.09C8.5,9.6 8.67,10.18 8.67,10.81C8.67,12 7.72,12.95 6.53,12.95C5.67,12.95 4.94,12.46 4.59,11.7L2.42,12.32C2.16,12.85 2,13.42 2,14C2,15.11 2.89,16 4,16A2,2 0 0,0 6,14A2,2 0 0,0 4,12C4.42,12 4.81,12.15 5.12,12.39L7.95,11.77C7.97,11.69 8,11.6 8,11.5C8,10.31 8.95,9.36 10.14,9.36C11,9.36 11.73,9.85 12.08,10.61L13.78,8.91C13.5,8.4 13.33,7.82 13.33,7.19C13.33,6 14.28,5.05 15.47,5.05C16.33,5.05 17.06,5.54 17.41,6.3L19.58,5.68C19.84,5.15 20,4.58 20,4A2,2 0 0,0 18,2Z" />
                    </svg>
                    开始数据分析
                </button>
            </div>
        </div>

        <!-- 步骤3: 调节表生成 -->
        <div class="operation-panel hidden" id="reconciliationPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H17.5V16H6.5V17.5M6.5,13H17.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z" />
                </svg>
                步骤3: 银行存款余额调节表
            </h3>
            <div id="reconciliationTable"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="validateReconciliationBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                    </svg>
                    验证调节表
                </button>
            </div>
        </div>

        <!-- 步骤4: 差异分析 -->
        <div class="operation-panel hidden" id="differencePanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                </svg>
                步骤4: 差异分析与核查结果
            </h3>
            <div id="differenceAnalysis"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="completeTaskBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                    </svg>
                    完成任务
                </button>
                <button class="btn btn-secondary" id="resetTaskBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                    </svg>
                    重置任务
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const state = {
            currentStep: 1,
            selectedDataSources: [],
            bankStatement: [],
            ledgerData: [],
            outstandingItems: [],
            reconciliationData: {},
            differences: [],
            completedSteps: [],
            startTime: Date.now()
        };

        // 模拟银行对账单数据
        const mockBankStatement = {
            accountName: "中国银行基本户",
            accountNumber: "6214*****1234",
            statementDate: "2024-08-31",
            openingBalance: 1250000.00,
            closingBalance: 1456780.50,
            transactions: [
                { date: "2024-08-01", description: "客户回款-ABC公司", amount: 280000.00, type: "入账" },
                { date: "2024-08-03", description: "支付货款-供应商DEF", amount: -150000.00, type: "出账" },
                { date: "2024-08-05", description: "银行利息收入", amount: 3250.00, type: "入账" },
                { date: "2024-08-08", description: "代付水电费", amount: -8750.00, type: "出账" },
                { date: "2024-08-10", description: "客户回款-XYZ公司", amount: 195000.00, type: "入账" },
                { date: "2024-08-15", description: "员工工资发放", amount: -125000.00, type: "出账" },
                { date: "2024-08-20", description: "银行手续费", amount: -350.00, type: "出账" },
                { date: "2024-08-25", description: "客户回款-PQR公司", amount: 320000.00, type: "入账" },
                { date: "2024-08-28", description: "支付租金", amount: -45000.00, type: "出账" },
                { date: "2024-08-30", description: "税款代扣代缴", amount: -62630.00, type: "出账" }
            ]
        };

        // 模拟银行日记账数据
        const mockLedgerData = {
            accountName: "银行存款-中国银行",
            accountCode: "1002001",
            periodDate: "2024-08-31",
            openingBalance: 1250000.00,
            closingBalance: 1478500.50,
            transactions: [
                { date: "2024-08-01", voucher: "记-001", description: "收到ABC公司货款", amount: 280000.00, type: "借方" },
                { date: "2024-08-02", voucher: "记-002", description: "预付DEF公司货款", amount: -150000.00, type: "贷方" },
                { date: "2024-08-05", voucher: "记-005", description: "银行存款利息", amount: 3250.00, type: "借方" },
                { date: "2024-08-10", voucher: "记-010", description: "收到XYZ公司货款", amount: 195000.00, type: "借方" },
                { date: "2024-08-12", voucher: "记-012", description: "预付MNO公司货款", amount: -85000.00, type: "贷方" },
                { date: "2024-08-15", voucher: "记-015", description: "发放员工工资", amount: -125000.00, type: "贷方" },
                { date: "2024-08-18", voucher: "记-018", description: "收到客户定金", amount: 75000.00, type: "借方" },
                { date: "2024-08-25", voucher: "记-025", description: "收到PQR公司货款", amount: 320000.00, type: "借方" },
                { date: "2024-08-28", voucher: "记-028", description: "支付办公租金", amount: -45000.00, type: "贷方" },
                { date: "2024-08-29", voucher: "记-029", description: "预收STU公司定金", amount: 65000.00, type: "借方" }
            ]
        };

        // 模拟未达账项数据
        const mockOutstandingItems = [
            {
                type: "企业已付银行未付",
                items: [
                    { date: "2024-08-29", description: "支付供应商货款", amount: -85000.00, voucher: "记-029" },
                    { date: "2024-08-30", description: "支付电话费", amount: -1720.00, voucher: "记-030" }
                ]
            },
            {
                type: "银行已付企业未付", 
                items: [
                    { date: "2024-08-20", description: "银行手续费", amount: -350.00, reference: "银行扣收" },
                    { date: "2024-08-30", description: "代扣代缴税款", amount: -62630.00, reference: "税务代收" }
                ]
            },
            {
                type: "企业已收银行未收",
                items: [
                    { date: "2024-08-29", description: "收到STU公司转账", amount: 65000.00, voucher: "记-029" },
                    { date: "2024-08-31", description: "收到客户现金", amount: 12000.00, voucher: "记-031" }
                ]
            },
            {
                type: "银行已收企业未收",
                items: [
                    { date: "2024-08-30", description: "银行代收客户款项", amount: 25000.00, reference: "代收款" }
                ]
            }
        ];

        // DOM元素引用
        const elements = {
            fetchDataBtn: document.getElementById('fetchDataBtn'),
            dataDisplayPanel: document.getElementById('dataDisplayPanel'),
            dataDisplay: document.getElementById('dataDisplay'),
            analyzeDataBtn: document.getElementById('analyzeDataBtn'),
            reconciliationPanel: document.getElementById('reconciliationPanel'),
            reconciliationTable: document.getElementById('reconciliationTable'),
            validateReconciliationBtn: document.getElementById('validateReconciliationBtn'),
            differencePanel: document.getElementById('differencePanel'),
            differenceAnalysis: document.getElementById('differenceAnalysis'),
            completeTaskBtn: document.getElementById('completeTaskBtn'),
            resetTaskBtn: document.getElementById('resetTaskBtn'),
            progressFill: document.getElementById('progressFill'),
            progressPercentage: document.getElementById('progressPercentage')
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateStepDisplay();
            showAlert('info', '请选择需要获取的数据源，建议选择全部数据源以获得完整的调节表');
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            elements.fetchDataBtn.addEventListener('click', fetchData);
            elements.analyzeDataBtn.addEventListener('click', analyzeData);
            elements.validateReconciliationBtn.addEventListener('click', validateReconciliation);
            elements.completeTaskBtn.addEventListener('click', completeTask);
            elements.resetTaskBtn.addEventListener('click', resetTask);
        }

        // 选择数据源
        function selectDataSource(sourceType) {
            const sourceCard = document.getElementById(`${sourceType}Source`);
            
            if (state.selectedDataSources.includes(sourceType)) {
                // 取消选择
                state.selectedDataSources = state.selectedDataSources.filter(s => s !== sourceType);
                sourceCard.classList.remove('selected');
            } else {
                // 添加选择
                state.selectedDataSources.push(sourceType);
                sourceCard.classList.add('selected');
            }

            // 更新按钮状态
            elements.fetchDataBtn.disabled = state.selectedDataSources.length === 0;
        }

        // 获取数据
        function fetchData() {
            elements.fetchDataBtn.disabled = true;
            elements.fetchDataBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    获取数据中...
                </div>
            `;

            setTimeout(() => {
                loadSelectedData();
                state.completedSteps.push('data_source');
                displayData();
                nextStep();
                showAlert('success', `成功获取 ${state.selectedDataSources.length} 个数据源的数据`);
            }, 2500);
        }

        // 加载选定的数据
        function loadSelectedData() {
            if (state.selectedDataSources.includes('bank')) {
                state.bankStatement = mockBankStatement;
            }
            if (state.selectedDataSources.includes('ledger')) {
                state.ledgerData = mockLedgerData;
            }
            if (state.selectedDataSources.includes('outstanding')) {
                state.outstandingItems = mockOutstandingItems;
            }
        }

        // 显示数据
        function displayData() {
            let html = '';

            // 银行对账单
            if (state.bankStatement.accountName) {
                html += `
                    <div class="data-table-container">
                        <div class="table-header">
                            <div class="table-title">🏛️ ${state.bankStatement.accountName} 对账单</div>
                            <div class="table-info">期末余额: ¥${state.bankStatement.closingBalance.toLocaleString()}</div>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>摘要</th>
                                    <th>金额</th>
                                    <th>类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${state.bankStatement.transactions.slice(0, 5).map(t => `
                                    <tr>
                                        <td>${t.date}</td>
                                        <td>${t.description}</td>
                                        <td class="amount-cell ${t.amount > 0 ? 'amount-positive' : 'amount-negative'}">
                                            ${t.amount > 0 ? '+' : ''}¥${Math.abs(t.amount).toLocaleString()}
                                        </td>
                                        <td>${t.type}</td>
                                    </tr>
                                `).join('')}
                                <tr style="opacity: 0.6;">
                                    <td colspan="4" style="text-align: center; font-style: italic;">
                                        ... 共${state.bankStatement.transactions.length}条交易记录 ...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // 银行日记账
            if (state.ledgerData.accountName) {
                html += `
                    <div class="data-table-container">
                        <div class="table-header">
                            <div class="table-title">📖 ${state.ledgerData.accountName} 日记账</div>
                            <div class="table-info">期末余额: ¥${state.ledgerData.closingBalance.toLocaleString()}</div>
                        </div>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>凭证号</th>
                                    <th>摘要</th>
                                    <th>金额</th>
                                    <th>方向</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${state.ledgerData.transactions.slice(0, 5).map(t => `
                                    <tr>
                                        <td>${t.date}</td>
                                        <td>${t.voucher}</td>
                                        <td>${t.description}</td>
                                        <td class="amount-cell ${t.amount > 0 ? 'amount-positive' : 'amount-negative'}">
                                            ${t.amount > 0 ? '+' : ''}¥${Math.abs(t.amount).toLocaleString()}
                                        </td>
                                        <td>${t.type}</td>
                                    </tr>
                                `).join('')}
                                <tr style="opacity: 0.6;">
                                    <td colspan="5" style="text-align: center; font-style: italic;">
                                        ... 共${state.ledgerData.transactions.length}条记账记录 ...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // 未达账项
            if (state.outstandingItems.length > 0) {
                html += `
                    <div class="data-table-container">
                        <div class="table-header">
                            <div class="table-title">📋 未达账项清单</div>
                            <div class="table-info">共${state.outstandingItems.length}类未达项目</div>
                        </div>
                        ${state.outstandingItems.map(category => `
                            <div style="margin-bottom: 1.5rem;">
                                <h5 style="color: var(--primary-color); margin-bottom: 0.5rem;">${category.type}</h5>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>描述</th>
                                            <th>金额</th>
                                            <th>参考</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${category.items.map(item => `
                                            <tr>
                                                <td>${item.date}</td>
                                                <td>${item.description}</td>
                                                <td class="amount-cell ${item.amount > 0 ? 'amount-positive' : 'amount-negative'}">
                                                    ${item.amount > 0 ? '+' : ''}¥${Math.abs(item.amount).toLocaleString()}
                                                </td>
                                                <td>${item.voucher || item.reference || '-'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            elements.dataDisplay.innerHTML = html;
            elements.analyzeDataBtn.disabled = false;
            elements.dataDisplayPanel.classList.remove('hidden');
        }

        // 数据分析
        function analyzeData() {
            elements.analyzeDataBtn.disabled = true;
            elements.analyzeDataBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    分析中...
                </div>
            `;

            setTimeout(() => {
                performDataAnalysis();
                state.completedSteps.push('data_display');
                generateReconciliationTable();
                nextStep();
                showAlert('success', '数据分析完成，已生成银行存款余额调节表');
            }, 2000);
        }

        // 执行数据分析
        function performDataAnalysis() {
            // 计算调节前余额
            const bankBalance = state.bankStatement.closingBalance || 0;
            const ledgerBalance = state.ledgerData.closingBalance || 0;

            // 计算未达账项调整金额
            let bankAdjustments = [];
            let ledgerAdjustments = [];

            state.outstandingItems.forEach(category => {
                category.items.forEach(item => {
                    if (category.type === "企业已付银行未付" || category.type === "企业已收银行未收") {
                        // 企业已记录，银行未记录，需要调整银行余额
                        bankAdjustments.push({
                            description: item.description,
                            amount: item.amount,
                            type: category.type
                        });
                    } else {
                        // 银行已记录，企业未记录，需要调整企业余额
                        ledgerAdjustments.push({
                            description: item.description,
                            amount: item.amount,
                            type: category.type
                        });
                    }
                });
            });

            // 计算调节后余额
            const bankAdjustedBalance = bankBalance + bankAdjustments.reduce((sum, adj) => sum + adj.amount, 0);
            const ledgerAdjustedBalance = ledgerBalance + ledgerAdjustments.reduce((sum, adj) => sum + adj.amount, 0);

            state.reconciliationData = {
                bankBalance: bankBalance,
                ledgerBalance: ledgerBalance,
                bankAdjustments: bankAdjustments,
                ledgerAdjustments: ledgerAdjustments,
                bankAdjustedBalance: bankAdjustedBalance,
                ledgerAdjustedBalance: ledgerAdjustedBalance,
                isBalanced: Math.abs(bankAdjustedBalance - ledgerAdjustedBalance) < 0.01
            };
        }

        // 生成调节表
        function generateReconciliationTable() {
            const data = state.reconciliationData;
            const currentDate = new Date().toLocaleDateString('zh-CN');

            elements.reconciliationTable.innerHTML = `
                <div class="reconciliation-table">
                    <div class="reconciliation-header">
                        <div class="reconciliation-title">银行存款余额调节表</div>
                        <div class="reconciliation-subtitle">编制日期: ${currentDate} &nbsp;&nbsp; 单位: 北京科技有限公司</div>
                    </div>

                    <div class="reconciliation-content">
                        <!-- 银行对账单余额调节 -->
                        <div class="reconciliation-section">
                            <div class="section-title">银行对账单余额调节</div>
                            <div class="balance-item">
                                <span class="balance-label">银行对账单余额</span>
                                <span class="balance-amount">¥${data.bankBalance.toLocaleString()}</span>
                            </div>
                            ${data.bankAdjustments.map(adj => `
                                <div class="balance-item">
                                    <span class="balance-label">
                                        ${adj.amount > 0 ? '加' : '减'}：${adj.description}
                                    </span>
                                    <span class="balance-amount ${adj.amount > 0 ? 'amount-positive' : 'amount-negative'}">
                                        ${adj.amount > 0 ? '+' : ''}¥${Math.abs(adj.amount).toLocaleString()}
                                    </span>
                                </div>
                            `).join('')}
                            <div class="balance-item">
                                <span class="balance-label">调节后余额</span>
                                <span class="balance-amount">¥${data.bankAdjustedBalance.toLocaleString()}</span>
                            </div>
                        </div>

                        <!-- 银行日记账余额调节 -->
                        <div class="reconciliation-section">
                            <div class="section-title">银行日记账余额调节</div>
                            <div class="balance-item">
                                <span class="balance-label">银行日记账余额</span>
                                <span class="balance-amount">¥${data.ledgerBalance.toLocaleString()}</span>
                            </div>
                            ${data.ledgerAdjustments.map(adj => `
                                <div class="balance-item">
                                    <span class="balance-label">
                                        ${adj.amount > 0 ? '加' : '减'}：${adj.description}
                                    </span>
                                    <span class="balance-amount ${adj.amount > 0 ? 'amount-positive' : 'amount-negative'}">
                                        ${adj.amount > 0 ? '+' : ''}¥${Math.abs(adj.amount).toLocaleString()}
                                    </span>
                                </div>
                            `).join('')}
                            <div class="balance-item">
                                <span class="balance-label">调节后余额</span>
                                <span class="balance-amount">¥${data.ledgerAdjustedBalance.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    ${data.isBalanced ? `
                        <div style="margin-top: 2rem; padding: 1rem; background: #DCFCE7; border-radius: 0.5rem; border: 1px solid #10B981; text-align: center;">
                            <div style="color: #166534; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
                                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                                </svg>
                                调节表平衡：双方调节后余额一致
                            </div>
                            <div style="color: #166534; font-size: 0.875rem; margin-top: 0.25rem;">
                                调节后余额均为 ¥${data.bankAdjustedBalance.toLocaleString()}，表明银行存款记录准确
                            </div>
                        </div>
                    ` : `
                        <div style="margin-top: 2rem; padding: 1rem; background: #FEE2E2; border-radius: 0.5rem; border: 1px solid #EF4444; text-align: center;">
                            <div style="color: #991B1B; font-weight: 600; display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
                                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,12 0 0,0 12,2Z" />
                                </svg>
                                调节表不平衡：存在未发现的差异
                            </div>
                            <div style="color: #991B1B; font-size: 0.875rem; margin-top: 0.25rem;">
                                差异金额: ¥${Math.abs(data.bankAdjustedBalance - data.ledgerAdjustedBalance).toLocaleString()}
                            </div>
                        </div>
                    `}
                </div>
            `;

            elements.validateReconciliationBtn.disabled = false;
            elements.reconciliationPanel.classList.remove('hidden');
        }

        // 验证调节表
        function validateReconciliation() {
            elements.validateReconciliationBtn.disabled = true;
            elements.validateReconciliationBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    验证中...
                </div>
            `;

            setTimeout(() => {
                performValidation();
                state.completedSteps.push('reconciliation');
                displayDifferenceAnalysis();
                nextStep();
                showAlert('success', '调节表验证完成，生成差异分析报告');
            }, 1500);
        }

        // 执行验证
        function performValidation() {
            const data = state.reconciliationData;
            
            state.differences = [];

            // 分析调节表的合理性
            if (!data.isBalanced) {
                state.differences.push({
                    type: 'error',
                    title: '调节表不平衡',
                    description: '银行对账单调节后余额与银行日记账调节后余额不一致',
                    amount: Math.abs(data.bankAdjustedBalance - data.ledgerAdjustedBalance),
                    suggestion: '需要进一步核查未达账项或银行记录'
                });
            }

            // 分析未达账项的合理性
            let totalOutstanding = 0;
            state.outstandingItems.forEach(category => {
                category.items.forEach(item => {
                    totalOutstanding += Math.abs(item.amount);
                });
            });

            if (totalOutstanding > data.bankBalance * 0.2) {
                state.differences.push({
                    type: 'warning',
                    title: '未达账项金额较大',
                    description: '未达账项总额超过银行余额的20%，建议核实',
                    amount: totalOutstanding,
                    suggestion: '检查未达账项的真实性和时效性'
                });
            }

            // 分析大额未达项
            state.outstandingItems.forEach(category => {
                category.items.forEach(item => {
                    if (Math.abs(item.amount) > 50000) {
                        state.differences.push({
                            type: 'info',
                            title: '大额未达账项',
                            description: `${item.description} - ${category.type}`,
                            amount: Math.abs(item.amount),
                            suggestion: '建议优先核实大额未达账项'
                        });
                    }
                });
            });

            // 如果没有发现问题，添加正面结论
            if (state.differences.length === 0) {
                state.differences.push({
                    type: 'success',
                    title: '调节表验证通过',
                    description: '银行存款余额调节表编制正确，双方余额一致',
                    amount: data.bankAdjustedBalance,
                    suggestion: '银行存款记录真实、准确、完整'
                });
            }
        }

        // 显示差异分析
        function displayDifferenceAnalysis() {
            let html = `
                <div class="difference-analysis">
                    <div class="difference-header">
                        <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
                        </svg>
                        差异分析与核查结果
                    </div>
                    <div class="difference-list">
            `;

            state.differences.forEach(diff => {
                const typeClass = {
                    'success': 'success',
                    'warning': 'warning', 
                    'error': 'error',
                    'info': 'info'
                }[diff.type];

                const typeIcon = {
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌',
                    'info': 'ℹ️'
                }[diff.type];

                html += `
                    <div class="difference-item" style="border-left-color: var(--${typeClass}-color);">
                        <div class="difference-desc">
                            ${typeIcon} ${diff.title}
                        </div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 0.5rem;">
                            ${diff.description}
                        </div>
                        <div class="difference-amount">
                            涉及金额: ¥${diff.amount.toLocaleString()}
                        </div>
                        <div style="font-size: 0.875rem; color: #A16207; margin-top: 0.5rem; font-style: italic;">
                            💡 ${diff.suggestion}
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
                
                <div style="margin-top: 2rem; padding: 1.5rem; background: var(--card-bg); border-radius: 0.75rem; box-shadow: var(--shadow);">
                    <h4 style="color: var(--text-primary); margin-bottom: 1rem;">📊 调节表汇总信息</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                        <div style="text-align: center; padding: 1rem; background: #F8FAFC; border-radius: 0.5rem;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color);">
                                ${state.selectedDataSources.length}
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">数据源</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #F8FAFC; border-radius: 0.5rem;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color);">
                                ${state.outstandingItems.reduce((sum, cat) => sum + cat.items.length, 0)}
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">未达账项</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #F8FAFC; border-radius: 0.5rem;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: ${state.reconciliationData.isBalanced ? 'var(--success-color)' : 'var(--error-color)'};">
                                ${state.reconciliationData.isBalanced ? '平衡' : '不平衡'}
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">调节状态</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #F8FAFC; border-radius: 0.5rem;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color);">
                                ¥${state.reconciliationData.bankAdjustedBalance.toLocaleString()}
                            </div>
                            <div style="font-size: 0.875rem; color: var(--text-secondary);">调节后余额</div>
                        </div>
                    </div>
                </div>
            `;

            elements.differenceAnalysis.innerHTML = html;
            elements.completeTaskBtn.disabled = false;
            elements.differencePanel.classList.remove('hidden');
        }

        // 完成任务
        function completeTask() {
            state.completedSteps.push('validation');
            updateStepDisplay();
            
            // 准备提交数据
            const taskData = {
                completedSteps: state.completedSteps,
                dataSourcesUsed: state.selectedDataSources.length,
                bankBalance: state.reconciliationData.bankBalance,
                ledgerBalance: state.reconciliationData.ledgerBalance,
                adjustedBalance: state.reconciliationData.bankAdjustedBalance,
                isReconciled: state.reconciliationData.isBalanced,
                outstandingItemsCount: state.outstandingItems.reduce((sum, cat) => sum + cat.items.length, 0),
                differencesFound: state.differences.length,
                reconciliationAccuracy: state.reconciliationData.isBalanced ? 1.0 : 0.8,
                timeSpent: Math.floor((Date.now() - state.startTime) / 1000),
                taskCompleted: true
            };

            // 提交验证
            submitTaskValidation(taskData);
        }

        // 提交任务验证
        function submitTaskValidation(taskData) {
            // 模拟API调用
            fetch('/api/scenarios/12/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(taskData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.valid) {
                    showAlert('success', `🎉 ${data.feedback} 得分: ${data.score}分`);
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', data.feedback || '任务验证未通过，请检查操作步骤');
                }
            })
            .catch(error => {
                console.error('任务验证失败:', error);
                // 本地验证逻辑
                if (validateLocalTask(taskData)) {
                    showAlert('success', '🎉 恭喜！您已成功完成银行存款余额调节表生成任务！');
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', '任务验证未通过，请确保完成所有步骤');
                }
            });
        }

        // 本地任务验证
        function validateLocalTask(data) {
            const requiredSteps = ['data_source', 'data_display', 'reconciliation', 'validation'];
            const stepsCompleted = requiredSteps.every(step => 
                data.completedSteps && data.completedSteps.includes(step)
            );
            
            return stepsCompleted && 
                   data.dataSourcesUsed >= 2 && 
                   data.outstandingItemsCount >= 3 &&
                   data.reconciliationAccuracy >= 0.8 &&
                   data.adjustedBalance > 0;
        }

        // 重置任务
        function resetTask() {
            if (confirm('确定要重置任务吗？所有进度将丢失。')) {
                location.reload();
            }
        }

        // 步骤管理
        function nextStep() {
            if (state.currentStep < 4) {
                state.currentStep++;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // 更新进度条
            const progress = (state.currentStep - 1) * 25;
            elements.progressFill.style.width = `${progress}%`;
            elements.progressPercentage.textContent = `${progress}%`;

            // 更新步骤状态
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                
                if (i < state.currentStep) {
                    step.classList.add('completed');
                } else if (i === state.currentStep) {
                    step.classList.add('active');
                }
            }
        }

        // 显示提示消息
        function showAlert(type, message) {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                </svg>
                ${message}
            `;

            document.querySelector('.container').insertBefore(alert, document.querySelector('.progress-container'));

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // 暴露全局函数供HTML调用
        window.selectDataSource = selectDataSource;
    </script>
</body>
</html>