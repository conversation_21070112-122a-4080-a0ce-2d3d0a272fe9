/**
 * 百度OCR适配器
 * 基于百度智能云OCR服务
 */

const axios = require('axios');
const BaseOCRAdapter = require('./base-adapter');

class BaiduOCRAdapter extends BaseOCRAdapter {
    constructor(config = {}) {
        super(config);
        this.name = 'baidu';
        this.priority = 2; // 第二优先级
        this.enabled = true;
        
        this.apiKey = config.apiKey || process.env.BAIDU_OCR_API_KEY;
        this.secretKey = config.secretKey || process.env.BAIDU_OCR_SECRET_KEY;
        this.accessToken = null;
        this.tokenExpiry = null;
        
        this.endpoints = {
            token: 'https://aip.baidubce.com/oauth/2.0/token',
            invoice: 'https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice',
            general: 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic'
        };
    }

    /**
     * 检查百度适配器是否可用
     */
    isAvailable() {
        return !!(this.apiKey && this.secretKey && 
                 this.apiKey.trim() !== '' && this.secretKey.trim() !== '');
    }

    /**
     * 获取支持的功能
     */
    getFeatures() {
        return [
            '专业发票识别',
            '高精度OCR',
            '批量处理',
            '多格式支持',
            '通用文字识别'
        ];
    }

    /**
     * 获取百度访问令牌
     */
    async getAccessToken() {
        try {
            // 检查令牌是否还有效
            if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
                return this.accessToken;
            }

            console.log('🔑 获取百度OCR访问令牌...');

            // 获取新令牌
            const response = await axios.post(this.endpoints.token, null, {
                params: {
                    grant_type: 'client_credentials',
                    client_id: this.apiKey,
                    client_secret: this.secretKey
                }
            });

            const { access_token, expires_in } = response.data;
            
            if (!access_token) {
                throw new Error('获取访问令牌失败：' + JSON.stringify(response.data));
            }

            // 缓存令牌，提前1分钟过期
            this.accessToken = access_token;
            this.tokenExpiry = Date.now() + (expires_in * 1000) - 60000;
            
            console.log('✅ 百度OCR访问令牌获取成功');
            return access_token;
            
        } catch (error) {
            console.error('❌ 获取百度OCR令牌失败:', error.message);
            throw new Error('百度OCR服务认证失败');
        }
    }

    /**
     * 百度发票OCR识别
     */
    async recognizeInvoice(imageData, filename) {
        try {
            console.log(`🔍 百度OCR识别发票: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('百度OCR未配置API密钥');
            }

            const accessToken = await this.getAccessToken();
            const base64Image = this.preprocessImage(imageData);

            // 调用百度OCR API
            const response = await axios.post(this.endpoints.invoice, 
                `image=${encodeURIComponent(base64Image)}&probability=true`,
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    params: {
                        access_token: accessToken
                    },
                    timeout: 30000
                }
            );

            const result = response.data;
            
            if (result.error_code) {
                throw new Error(`百度OCR API错误: ${result.error_msg}`);
            }

            return this.parseInvoiceResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 百度OCR识别失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 百度通用OCR识别
     */
    async recognizeGeneral(imageData, filename) {
        try {
            console.log(`🔍 百度通用OCR识别: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('百度OCR未配置API密钥');
            }

            const accessToken = await this.getAccessToken();
            const base64Image = this.preprocessImage(imageData);

            const response = await axios.post(this.endpoints.general,
                `image=${encodeURIComponent(base64Image)}&language_type=CHN_ENG`,
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    params: {
                        access_token: accessToken
                    },
                    timeout: 30000
                }
            );

            const result = response.data;
            
            if (result.error_code) {
                throw new Error(`百度OCR API错误: ${result.error_msg}`);
            }

            return this.parseGeneralResult(result, filename);
            
        } catch (error) {
            console.error(`❌ 百度通用OCR失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 解析百度发票识别结果
     */
    parseInvoiceResult(baiduResult, filename) {
        const wordsResult = baiduResult.words_result || {};
        
        // 字段提取辅助函数
        const extractField = (field) => {
            return field && field.words ? field.words : '';
        };

        const standardizedData = {
            invoiceNumber: extractField(wordsResult.InvoiceNum),
            invoiceCode: extractField(wordsResult.InvoiceCode),
            date: extractField(wordsResult.InvoiceDate),
            sellerName: extractField(wordsResult.SellerName),
            sellerTaxId: extractField(wordsResult.SellerRegisterNum),
            sellerAddress: extractField(wordsResult.SellerAddress),
            sellerPhone: extractField(wordsResult.SellerBank),
            buyerName: extractField(wordsResult.PurchaserName),
            buyerTaxId: extractField(wordsResult.PurchaserRegisterNum),
            buyerAddress: extractField(wordsResult.PurchaserAddress),
            buyerPhone: extractField(wordsResult.PurchaserBank),
            totalAmount: this.parseAmount(extractField(wordsResult.TotalAmount)),
            amountWithoutTax: this.parseAmount(extractField(wordsResult.AmountInFiguers)),
            taxAmount: this.parseAmount(extractField(wordsResult.TotalTax)),
            items: this.parseBaiduInvoiceItems(wordsResult.CommodityName || []),
            remarks: extractField(wordsResult.Remarks),
            checkCode: extractField(wordsResult.CheckCode)
        };

        const confidence = this.calculateAverageConfidence(wordsResult);
        
        const result = this.standardizeResult({
            data: standardizedData,
            confidence: confidence,
            rawData: baiduResult
        }, filename, 'baidu');

        console.log(`✅ 百度发票识别成功: ${standardizedData.invoiceNumber}, 置信度: ${(confidence * 100).toFixed(1)}%`);
        return result;
    }

    /**
     * 解析百度通用OCR结果
     */
    parseGeneralResult(baiduResult, filename) {
        const words = baiduResult.words_result || [];
        const lines = words.map(item => item.words);
        
        return this.standardizeResult({
            data: {
                text: lines.join('\n'),
                lines: lines,
                lineCount: lines.length
            },
            confidence: 0.85,
            rawData: baiduResult
        }, filename, 'baidu');
    }

    /**
     * 解析百度发票商品条目
     */
    parseBaiduInvoiceItems(commodityNames) {
        if (!Array.isArray(commodityNames)) return [];
        
        return commodityNames.map((item, index) => ({
            id: index + 1,
            name: item.words || '',
            quantity: 1,
            unitPrice: 0,
            amount: 0
        }));
    }

    /**
     * 计算平均置信度
     */
    calculateAverageConfidence(wordsResult) {
        if (!wordsResult) return 0.8;
        
        const fields = Object.values(wordsResult);
        let totalConfidence = 0;
        let fieldCount = 0;
        
        fields.forEach(field => {
            if (field && field.probability) {
                const prob = field.probability.average || field.probability;
                totalConfidence += typeof prob === 'number' ? prob : 0.8;
                fieldCount++;
            }
        });
        
        return fieldCount > 0 ? totalConfidence / fieldCount : 0.8;
    }
}

module.exports = BaiduOCRAdapter;