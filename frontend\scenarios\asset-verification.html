<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固定资产卡片信息核对 - RPA实训场景3</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .header .scenario-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #8D99AE;
            font-size: 14px;
        }
        
        .simulation-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .system-header {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .system-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .system-subtitle {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .nav-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .nav-item {
            padding: 8px 16px;
            background: #7673FF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .nav-item:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }
        
        .nav-item.active {
            background: #4338CA;
        }
        
        .content-area {
            padding: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 25px;
            font-size: 14px;
            color: #8D99AE;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: #7673FF;
            color: white;
        }
        
        .step.completed {
            background: #10B981;
            color: white;
        }
        
        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: rgba(255,255,255,0.3);
        }
        
        .step.completed .step-number {
            background: rgba(255,255,255,0.3);
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .form-input {
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118,115,255,0.1);
        }
        
        .form-select {
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118,115,255,0.1);
        }
        
        .file-upload {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .file-upload:hover {
            border-color: #7673FF;
            background: rgba(118,115,255,0.05);
        }
        
        .file-upload.dragover {
            border-color: #7673FF;
            background: rgba(118,115,255,0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            color: #7673FF;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #8D99AE;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #8D99AE;
            font-size: 12px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(118,115,255,0.3);
        }
        
        .btn-secondary {
            background: #8D99AE;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #6c757d;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #10B981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .data-table th {
            background: #7673FF;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            color: #374151;
        }
        
        .data-table tr:hover {
            background: #f9fafb;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .status-verified {
            background: #D1FAE5;
            color: #065F46;
        }
        
        .status-error {
            background: #FEE2E2;
            color: #991B1B;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #7673FF, #A069FF);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .task-completed {
            background: #D1FAE5;
            border: 2px solid #10B981;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
        }
        
        .completion-icon {
            font-size: 48px;
            color: #10B981;
            margin-bottom: 15px;
        }
        
        .completion-text {
            font-size: 18px;
            font-weight: bold;
            color: #065F46;
            margin-bottom: 10px;
        }
        
        .completion-details {
            color: #047857;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .tooltip {
            position: relative;
            display: inline-block;
        }
        
        .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .comparison-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: #f9fafb;
        }

        .comparison-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #7673FF;
        }

        .comparison-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .comparison-item:last-child {
            border-bottom: none;
        }

        .comparison-label {
            font-weight: 500;
            color: #374151;
        }

        .comparison-value {
            color: #6b7280;
        }

        .match-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 20px;
            flex-direction: column;
            gap: 10px;
        }

        .match-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }

        .match-success {
            background: #D1FAE5;
            color: #065F46;
        }

        .match-error {
            background: #FEE2E2;
            color: #991B1B;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 场景信息头部 -->
        <div class="header">
            <h1>🏢 固定资产卡片信息核对</h1>
            <p>通过Excel清单与固资系统数据比对，实现资产信息的自动核验</p>
            <div class="scenario-info">
                <div class="info-item">
                    <span>📊</span>
                    <span>模块：基础操作</span>
                </div>
                <div class="info-item">
                    <span>⏱️</span>
                    <span>预计时间：25分钟</span>
                </div>
                <div class="info-item">
                    <span>🎯</span>
                    <span>难度：中级</span>
                </div>
            </div>
        </div>

        <!-- 模拟的固资系统界面 -->
        <div class="simulation-container">
            <div class="system-header">
                <div class="system-logo">🏢 企业固定资产管理系统</div>
                <div class="system-subtitle">Asset Management System v2.1</div>
            </div>

            <!-- 系统导航栏 -->
            <div class="nav-bar">
                <div class="nav-content">
                    <a href="#" class="nav-item active" data-tab="upload">📂 Excel导入</a>
                    <a href="#" class="nav-item" data-tab="query">🔍 资产查询</a>
                    <a href="#" class="nav-item" data-tab="compare">⚖️ 数据比对</a>
                    <a href="#" class="nav-item" data-tab="export">📊 结果导出</a>
                </div>
            </div>

            <div class="content-area">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <span>上传Excel清单</span>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <span>系统查询核对</span>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <span>比对结果分析</span>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <span>导出核对报告</span>
                    </div>
                </div>

                <!-- 步骤1：Excel文件上传 -->
                <div id="step1" class="step-content">
                    <div class="form-section">
                        <div class="section-title">
                            <span>📂</span>
                            <span>上传固定资产清单文件</span>
                        </div>
                        
                        <div class="file-upload" id="fileUpload">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击选择或拖拽Excel文件到此处</div>
                            <div class="upload-hint">支持 .xlsx, .xls 格式，文件大小不超过10MB</div>
                            <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                        </div>

                        <div id="fileInfo" class="hidden" style="margin-top: 20px;">
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
                                <div style="font-weight: bold; color: #1976d2; margin-bottom: 10px;">文件解析成功</div>
                                <div style="color: #424242;">
                                    <div>文件名：<span id="fileName"></span></div>
                                    <div>数据行数：<span id="rowCount"></span></div>
                                    <div>包含字段：资产编号、资产名称、规格型号、购置日期、原值</div>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" id="uploadBtn" disabled>
                                <span>📤</span>
                                <span>开始解析文件</span>
                            </button>
                        </div>
                    </div>

                    <!-- 解析后的数据预览 -->
                    <div id="dataPreview" class="hidden">
                        <div class="section-title">
                            <span>👀</span>
                            <span>数据预览（前5条记录）</span>
                        </div>
                        
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>资产编号</th>
                                    <th>资产名称</th>
                                    <th>规格型号</th>
                                    <th>购置日期</th>
                                    <th>原值（万元）</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="previewTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>

                        <div class="action-buttons">
                            <button class="btn btn-success" id="proceedBtn">
                                <span>▶️</span>
                                <span>继续下一步</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：系统查询核对 -->
                <div id="step2" class="step-content hidden">
                    <div class="form-section">
                        <div class="section-title">
                            <span>🔍</span>
                            <span>系统自动查询核对中...</span>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        
                        <div style="text-align: center; color: #8D99AE; margin: 20px 0;">
                            <div id="progressText">正在连接固定资产数据库...</div>
                        </div>

                        <div id="queryResults" class="hidden">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>资产编号</th>
                                        <th>查询状态</th>
                                        <th>系统中名称</th>
                                        <th>系统中型号</th>
                                        <th>系统中原值</th>
                                        <th>匹配结果</th>
                                    </tr>
                                </thead>
                                <tbody id="queryTableBody">
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：比对结果分析 -->
                <div id="step3" class="step-content hidden">
                    <div class="form-section">
                        <div class="section-title">
                            <span>⚖️</span>
                            <span>数据比对结果分析</span>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: #D1FAE5; padding: 20px; border-radius: 10px; text-align: center;">
                                <div style="font-size: 32px; font-weight: bold; color: #065F46;" id="matchCount">0</div>
                                <div style="color: #047857; font-size: 14px;">完全匹配</div>
                            </div>
                            <div style="background: #FEF3C7; padding: 20px; border-radius: 10px; text-align: center;">
                                <div style="font-size: 32px; font-weight: bold; color: #92400E;" id="partialCount">0</div>
                                <div style="color: #B45309; font-size: 14px;">部分差异</div>
                            </div>
                            <div style="background: #FEE2E2; padding: 20px; border-radius: 10px; text-align: center;">
                                <div style="font-size: 32px; font-weight: bold; color: #991B1B;" id="errorCount">0</div>
                                <div style="color: #B91C1C; font-size: 14px;">无法匹配</div>
                            </div>
                        </div>

                        <!-- 详细比对结果 -->
                        <div id="comparisonDetails">
                            <!-- 动态生成比对详情 -->
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" id="exportBtn">
                                <span>📊</span>
                                <span>生成核对报告</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤4：任务完成 -->
                <div id="step4" class="step-content hidden">
                    <div class="task-completed">
                        <div class="completion-icon">🎉</div>
                        <div class="completion-text">固定资产核对任务完成！</div>
                        <div class="completion-details">
                            已成功核对 <span id="finalCount">0</span> 条资产记录，
                            生成详细的比对分析报告
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-secondary" onclick="location.href='student-dashboard.html'">
                            <span>🏠</span>
                            <span>返回主页</span>
                        </button>
                        <button class="btn btn-primary" onclick="window.location.reload()">
                            <span>🔄</span>
                            <span>重新练习</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // 模拟的固定资产数据
        const mockAssetData = [
            { code: 'ZC001', name: '联想ThinkPad笔记本', model: 'E14-20RA', date: '2021-03-15', value: 0.8 },
            { code: 'ZC002', name: '惠普激光打印机', model: 'LaserJet Pro M404n', date: '2021-05-20', value: 0.35 },
            { code: 'ZC003', name: '海尔商用空调', model: 'KFR-35GW/03DIB23A', date: '2020-08-10', value: 1.2 },
            { code: 'ZC004', name: '办公桌椅套装', model: 'MD-2021-A', date: '2021-01-25', value: 0.15 },
            { code: 'ZC005', name: '戴尔显示器', model: 'U2419H', date: '2021-04-12', value: 0.28 }
        ];

        let uploadedData = [];
        let queryResults = [];
        let currentStep = 1;

        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // 文件上传相关
            const fileUpload = document.getElementById('fileUpload');
            const fileInput = document.getElementById('excelFile');
            
            fileUpload.addEventListener('click', () => fileInput.click());
            fileUpload.addEventListener('dragover', handleDragOver);
            fileUpload.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
            
            // 按钮事件
            document.getElementById('uploadBtn').addEventListener('click', parseExcelFile);
            document.getElementById('proceedBtn').addEventListener('click', () => goToStep(2));
            document.getElementById('exportBtn').addEventListener('click', generateReport);
            
            // 导航栏事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', handleNavClick);
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            e.target.closest('.file-upload').classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            e.target.closest('.file-upload').classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            if (e.target.files.length > 0) {
                processFile(e.target.files[0]);
            }
        }

        function processFile(file) {
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            
            // 验证文件类型
            if (!fileName.match(/\.(xlsx|xls)$/)) {
                alert('请选择Excel文件（.xlsx或.xls格式）');
                return;
            }
            
            // 验证文件大小
            if (file.size > 10 * 1024 * 1024) {
                alert('文件大小不能超过10MB');
                return;
            }
            
            // 显示文件信息
            document.getElementById('fileName').textContent = fileName;
            document.getElementById('rowCount').textContent = '5条记录';
            document.getElementById('fileInfo').classList.remove('hidden');
            document.getElementById('uploadBtn').disabled = false;
            
            // 模拟解析Excel数据
            uploadedData = [
                { code: 'ZC001', name: '联想ThinkPad笔记本', model: 'E14-20RA', date: '2021-03-15', value: 0.8 },
                { code: 'ZC002', name: '惠普激光打印机', model: 'LaserJet Pro M404n', date: '2021-05-20', value: 0.35 },
                { code: 'ZC003', name: '海尔商用空调', model: 'KFR-35GW/03DIB23A', date: '2020-08-10', value: 1.2 },
                { code: 'ZC004', name: '办公桌椅套装', model: 'MD-2021-A', date: '2021-01-25', value: 0.15 },
                { code: 'ZC005', name: '戴尔显示器', model: 'U2419H', date: '2021-04-12', value: 0.28 }
            ];
        }

        function parseExcelFile() {
            const btn = document.getElementById('uploadBtn');
            btn.innerHTML = '<div class="loading"></div><span>解析中...</span>';
            btn.disabled = true;
            
            setTimeout(() => {
                showDataPreview();
                btn.innerHTML = '<span>📤</span><span>解析完成</span>';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-success');
            }, 2000);
        }

        function showDataPreview() {
            const tbody = document.getElementById('previewTableBody');
            tbody.innerHTML = '';
            
            uploadedData.forEach(item => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td id="asset-code-${item.code}">${item.code}</td>
                    <td id="asset-name-${item.code}">${item.name}</td>
                    <td id="asset-model-${item.code}">${item.model}</td>
                    <td id="asset-date-${item.code}">${item.date}</td>
                    <td id="asset-value-${item.code}">${item.value}</td>
                    <td><span class="status-badge status-pending">待核对</span></td>
                `;
            });
            
            document.getElementById('dataPreview').classList.remove('hidden');
            document.getElementById('dataPreview').classList.add('fade-in');
        }

        function goToStep(step) {
            // 隐藏当前步骤
            document.getElementById(`step${currentStep}`).classList.add('hidden');
            
            // 更新步骤指示器
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('completed');
            
            // 显示新步骤
            currentStep = step;
            document.getElementById(`step${step}`).classList.remove('hidden');
            document.getElementById(`step${step}`).classList.add('fade-in');
            document.querySelector(`.step[data-step="${step}"]`).classList.add('active');
            
            // 根据步骤执行相应操作
            if (step === 2) {
                startAssetQuery();
            } else if (step === 3) {
                showComparisonResults();
            }
        }

        function startAssetQuery() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            let progress = 0;
            
            const steps = [
                '正在连接固定资产数据库...',
                '正在查询资产编号 ZC001...',
                '正在查询资产编号 ZC002...',
                '正在查询资产编号 ZC003...',
                '正在查询资产编号 ZC004...',
                '正在查询资产编号 ZC005...',
                '正在进行数据比对分析...',
                '查询完成，准备结果展示...'
            ];
            
            const interval = setInterval(() => {
                progress += 12.5;
                progressFill.style.width = progress + '%';
                progressText.textContent = steps[Math.floor(progress / 12.5)] || '查询完成';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        showQueryResults();
                    }, 1000);
                }
            }, 800);
        }

        function showQueryResults() {
            const tbody = document.getElementById('queryTableBody');
            tbody.innerHTML = '';
            
            queryResults = uploadedData.map(item => {
                const systemData = mockAssetData.find(mock => mock.code === item.code);
                const match = systemData && 
                    systemData.name === item.name && 
                    systemData.model === item.model && 
                    systemData.value === item.value;
                
                return {
                    ...item,
                    systemData: systemData,
                    matchStatus: match ? 'perfect' : (systemData ? 'partial' : 'notfound')
                };
            });
            
            queryResults.forEach(result => {
                const row = tbody.insertRow();
                const statusBadge = result.matchStatus === 'perfect' ? 
                    '<span class="status-badge status-verified">✓ 完全匹配</span>' :
                    result.matchStatus === 'partial' ?
                    '<span class="status-badge status-pending">⚠ 部分差异</span>' :
                    '<span class="status-badge status-error">✗ 未找到</span>';
                
                row.innerHTML = `
                    <td>${result.code}</td>
                    <td><span class="status-badge status-verified">查询成功</span></td>
                    <td>${result.systemData ? result.systemData.name : '未找到'}</td>
                    <td>${result.systemData ? result.systemData.model : '未找到'}</td>
                    <td>${result.systemData ? result.systemData.value : '未找到'}</td>
                    <td>${statusBadge}</td>
                `;
            });
            
            document.getElementById('queryResults').classList.remove('hidden');
            document.getElementById('queryResults').classList.add('fade-in');
            
            setTimeout(() => {
                goToStep(3);
            }, 3000);
        }

        function showComparisonResults() {
            const matchCount = queryResults.filter(r => r.matchStatus === 'perfect').length;
            const partialCount = queryResults.filter(r => r.matchStatus === 'partial').length;
            const errorCount = queryResults.filter(r => r.matchStatus === 'notfound').length;
            
            document.getElementById('matchCount').textContent = matchCount;
            document.getElementById('partialCount').textContent = partialCount;
            document.getElementById('errorCount').textContent = errorCount;
            
            // 显示详细比对结果
            const detailsContainer = document.getElementById('comparisonDetails');
            detailsContainer.innerHTML = '';
            
            queryResults.forEach(result => {
                if (result.matchStatus !== 'perfect') {
                    const comparisonDiv = document.createElement('div');
                    comparisonDiv.className = 'comparison-section';
                    comparisonDiv.innerHTML = `
                        <h4 style="color: #2c3e50; margin-bottom: 20px;">资产编号：${result.code} - 差异分析</h4>
                        <div class="comparison-grid">
                            <div class="comparison-card">
                                <div class="comparison-title">📋 Excel清单数据</div>
                                <div class="comparison-item">
                                    <span class="comparison-label">资产名称：</span>
                                    <span class="comparison-value">${result.name}</span>
                                </div>
                                <div class="comparison-item">
                                    <span class="comparison-label">规格型号：</span>
                                    <span class="comparison-value">${result.model}</span>
                                </div>
                                <div class="comparison-item">
                                    <span class="comparison-label">原值：</span>
                                    <span class="comparison-value">${result.value}万元</span>
                                </div>
                            </div>
                            <div class="comparison-card">
                                <div class="comparison-title">🏢 系统数据</div>
                                <div class="comparison-item">
                                    <span class="comparison-label">资产名称：</span>
                                    <span class="comparison-value">${result.systemData ? result.systemData.name : '未找到'}</span>
                                </div>
                                <div class="comparison-item">
                                    <span class="comparison-label">规格型号：</span>
                                    <span class="comparison-value">${result.systemData ? result.systemData.model : '未找到'}</span>
                                </div>
                                <div class="comparison-item">
                                    <span class="comparison-label">原值：</span>
                                    <span class="comparison-value">${result.systemData ? result.systemData.value + '万元' : '未找到'}</span>
                                </div>
                            </div>
                        </div>
                        <div class="match-indicator">
                            <div class="match-icon ${result.matchStatus === 'notfound' ? 'match-error' : 'match-error'}">
                                ${result.matchStatus === 'notfound' ? '✗' : '⚠'}
                            </div>
                            <div style="text-align: center; color: #6b7280; font-size: 14px;">
                                ${result.matchStatus === 'notfound' ? '系统中未找到该资产' : '数据存在差异，需要人工核查'}
                            </div>
                        </div>
                    `;
                    detailsContainer.appendChild(comparisonDiv);
                }
            });
        }

        function generateReport() {
            const btn = document.getElementById('exportBtn');
            btn.innerHTML = '<div class="loading"></div><span>生成中...</span>';
            btn.disabled = true;
            
            setTimeout(() => {
                // 调用验证API
                validateTask();
            }, 2000);
        }

        async function validateTask() {
            try {
                const response = await fetch('/api/scenarios/3/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        completedSteps: ['upload', 'query', 'compare', 'export'],
                        assetCount: uploadedData.length,
                        matchResults: {
                            perfect: queryResults.filter(r => r.matchStatus === 'perfect').length,
                            partial: queryResults.filter(r => r.matchStatus === 'partial').length,
                            notfound: queryResults.filter(r => r.matchStatus === 'notfound').length
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.valid) {
                    document.getElementById('finalCount').textContent = uploadedData.length;
                    goToStep(4);
                } else {
                    throw new Error(result.message || '验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                alert('任务验证失败，请检查操作步骤');
                
                const btn = document.getElementById('exportBtn');
                btn.innerHTML = '<span>📊</span><span>重新生成报告</span>';
                btn.disabled = false;
            }
        }

        function handleNavClick(e) {
            e.preventDefault();
            const tabName = e.target.dataset.tab;
            
            // 更新导航栏状态
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            e.target.classList.add('active');
            
            // 这里可以根据需要添加标签页切换逻辑
        }
    </script>
</body>
</html>