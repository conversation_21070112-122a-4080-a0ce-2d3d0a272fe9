/**
 * 认证路由 - 学生和教师登录
 * 基于腾讯云云开发的身份验证
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
// 动态导入数据库配置
let dbConfig;
try {
  if (process.env.TCB_ENV_ID && process.env.TCB_SECRET_ID && process.env.TCB_SECRET_KEY) {
    dbConfig = require('../../cloudbase/database/db');
  } else {
    dbConfig = require('../../cloudbase/database/db-mock');
  }
} catch (error) {
  dbConfig = require('../../cloudbase/database/db-mock');
}
const { db, COLLECTIONS } = dbConfig;

const router = express.Router();

/**
 * 学生登录
 * POST /api/auth/student/login
 */
router.post('/student/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 从云数据库查询学生
    const result = await db
      .collection(COLLECTIONS.USERS)
      .where({
        username: username,
        role: 'student',
        status: 'active'
      })
      .get();

    if (result.data.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const student = result.data[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, student.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: student._id,
        username: student.username,
        role: 'student',
        classId: student.class_id
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // 更新最后登录时间
    await db
      .collection(COLLECTIONS.USERS)
      .doc(student._id)
      .update({
        last_login: new Date()
      });

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: student._id,
          username: student.username,
          name: student.name,
          studentId: student.student_id,
          role: 'student',
          classId: student.class_id
        }
      }
    });

    console.log(`学生 ${student.name}(${student.username}) 登录成功`);

  } catch (error) {
    console.error('学生登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

/**
 * 教师登录
 * POST /api/auth/teacher/login
 */
router.post('/teacher/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 从云数据库查询教师
    const result = await db
      .collection(COLLECTIONS.USERS)
      .where({
        username: username,
        role: 'teacher',
        status: 'active'
      })
      .get();

    if (result.data.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const teacher = result.data[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, teacher.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: teacher._id,
        username: teacher.username,
        role: 'teacher'
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // 更新最后登录时间
    await db
      .collection(COLLECTIONS.USERS)
      .doc(teacher._id)
      .update({
        last_login: new Date()
      });

    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: teacher._id,
          username: teacher.username,
          name: teacher.name,
          role: 'teacher',
          department: teacher.department
        }
      }
    });

    console.log(`教师 ${teacher.name}(${teacher.username}) 登录成功`);

  } catch (error) {
    console.error('教师登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

/**
 * 登出
 * POST /api/auth/logout
 */
router.post('/logout', (req, res) => {
  // JWT是无状态的，登出主要在前端删除token
  res.json({
    success: true,
    message: '登出成功'
  });
});

/**
 * 验证token
 * GET /api/auth/verify
 */
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证token'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 从数据库获取最新用户信息
    const result = await db
      .collection(COLLECTIONS.USERS)
      .doc(decoded.userId)
      .get();

    if (!result.data.length) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = result.data[0];
    
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户已被禁用'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          name: user.name,
          role: user.role,
          ...(user.role === 'student' && { 
            studentId: user.student_id,
            classId: user.class_id 
          }),
          ...(user.role === 'teacher' && { 
            department: user.department 
          })
        }
      }
    });

  } catch (error) {
    console.error('Token验证失败:', error);
    res.status(401).json({
      success: false,
      message: 'Token无效或已过期'
    });
  }
});

module.exports = router;