<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景6：增值税进项税发票认证系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #8D99AE;
            font-size: 16px;
        }

        .workflow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .step-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
        }

        .step-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(118, 115, 255, 0.25);
        }

        .step-card.active {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.05);
        }

        .step-card.completed {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.05);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-card.completed .step-number {
            background: #27AE60;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .step-content {
            margin-bottom: 20px;
        }

        .file-upload-area {
            border: 2px dashed #7673FF;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            background: rgba(118, 115, 255, 0.05);
            margin: 15px 0;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #5C7BFF;
            background: rgba(118, 115, 255, 0.1);
        }

        .file-upload-area.dragover {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            color: #7673FF;
            margin-bottom: 15px;
        }

        .navigation-tree {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .nav-level {
            margin-left: 20px;
            margin-top: 10px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px 0;
        }

        .nav-item:hover {
            background: rgba(118, 115, 255, 0.1);
        }

        .nav-item.active {
            background: rgba(118, 115, 255, 0.2);
            font-weight: 600;
        }

        .nav-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: rgba(118, 115, 255, 0.05);
        }

        .invoice-checkbox {
            transform: scale(1.2);
            margin-right: 10px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .status-pending {
            background: #FFF3CD;
            color: #856404;
        }

        .status-selected {
            background: #D1ECF1;
            color: #0C5460;
        }

        .status-authenticated {
            background: #D4EDDA;
            color: #155724;
        }

        .status-failed {
            background: #F8D7DA;
            color: #721C24;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(118, 115, 255, 0.4);
        }

        .btn-success {
            background: #27AE60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #F39C12;
            color: white;
        }

        .btn-warning:hover {
            background: #E67E22;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #E9ECEF;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #8D99AE;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .success-message {
            background: rgba(39, 174, 96, 0.1);
            border: 2px solid #27AE60;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .batch-operations {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .operation-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>增值税进项税发票认证系统</h1>
            <p>从CSV文件读取发票信息，在增值税平台进行批量勾选认证</p>
        </div>

        <div class="progress-indicator">
            <h3>任务进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            <p id="progressText">准备开始 - 请上传发票CSV文件</p>
        </div>

        <div class="workflow">
            <!-- 步骤1：上传CSV文件 -->
            <div class="step-card active" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">上传发票数据</div>
                </div>
                <div class="step-content">
                    <div class="file-upload-area" id="csvUploadArea">
                        <div class="upload-icon">📄</div>
                        <p><strong>点击或拖拽上传发票CSV文件</strong></p>
                        <p>支持格式：.csv</p>
                        <p style="font-size: 12px; color: #8D99AE; margin-top: 10px;">
                            CSV格式要求：发票代码,发票号码,开票日期,销售方名称,金额,税额
                        </p>
                        <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
                    </div>
                    <button class="btn btn-primary" id="processCsvBtn" disabled>解析CSV数据</button>
                </div>
            </div>

            <!-- 步骤2：导航到认证模块 -->
            <div class="step-card" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">系统导航</div>
                </div>
                <div class="step-content">
                    <div class="navigation-tree">
                        <h4>增值税平台菜单导航</h4>
                        <div class="nav-item" data-nav="main">
                            <span class="nav-icon">🏠</span>
                            <span>首页</span>
                        </div>
                        <div class="nav-level">
                            <div class="nav-item" data-nav="tax-management">
                                <span class="nav-icon">📊</span>
                                <span>税务管理</span>
                            </div>
                            <div class="nav-level">
                                <div class="nav-item" data-nav="input-invoice">
                                    <span class="nav-icon">📋</span>
                                    <span>进项发票管理</span>
                                </div>
                                <div class="nav-level">
                                    <div class="nav-item" data-nav="authentication" id="authenticationNav">
                                        <span class="nav-icon">✅</span>
                                        <span>发票认证</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="navigateBtn" disabled>进入认证模块</button>
                </div>
            </div>

            <!-- 步骤3：批量勾选认证 -->
            <div class="step-card" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">批量认证</div>
                </div>
                <div class="step-content">
                    <div class="batch-operations">
                        <h4>批量操作</h4>
                        <div class="operation-buttons">
                            <button class="btn btn-warning" id="selectAllBtn" disabled>全选</button>
                            <button class="btn btn-primary" id="selectUncheckedBtn" disabled>选择未认证</button>
                            <button class="btn btn-primary" id="clearSelectionBtn" disabled>清空选择</button>
                        </div>
                    </div>
                    <div class="summary-stats" id="selectionStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="totalInvoices">0</div>
                            <div class="stat-label">发票总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="selectedInvoices">0</div>
                            <div class="stat-label">已选择</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="authenticatedInvoices">0</div>
                            <div class="stat-label">已认证</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalAmount">¥0</div>
                            <div class="stat-label">选中金额</div>
                        </div>
                    </div>
                    <button class="btn btn-success" id="authenticateBtn" disabled>执行认证</button>
                </div>
            </div>

            <!-- 步骤4：结果导出 -->
            <div class="step-card" id="step4">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <div class="step-title">结果导出</div>
                </div>
                <div class="step-content">
                    <div class="summary-stats" id="finalStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="successCount">0</div>
                            <div class="stat-label">认证成功</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="failCount">0</div>
                            <div class="stat-label">认证失败</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="successRate">0%</div>
                            <div class="stat-label">成功率</div>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="exportResultsBtn" disabled>导出认证结果</button>
                </div>
            </div>
        </div>

        <!-- 发票数据显示区域 -->
        <div id="invoiceDataArea" style="display: none;">
            <div class="step-card">
                <h3>发票认证列表</h3>
                <div class="loading" id="authenticationLoading">
                    <div class="spinner"></div>
                    <p>正在执行发票认证...</p>
                </div>
                <table class="data-table" id="invoiceTable" style="display: none;">
                    <thead>
                        <tr>
                            <th style="width: 60px;">
                                <input type="checkbox" id="selectAllCheckbox" class="invoice-checkbox">
                            </th>
                            <th>发票代码</th>
                            <th>发票号码</th>
                            <th>开票日期</th>
                            <th>销售方名称</th>
                            <th>金额</th>
                            <th>税额</th>
                            <th>认证状态</th>
                        </tr>
                    </thead>
                    <tbody id="invoiceTableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 成功提示 -->
        <div class="success-message" id="successMessage">
            <h3>🎉 任务完成！</h3>
            <p>您已成功完成增值税进项税发票认证操作，系统已记录您的学习进度。</p>
        </div>
    </div>

    <script>
        // 全局变量
        let invoiceData = [];
        let selectedInvoices = [];
        let authenticatedInvoices = [];
        let currentStep = 1;
        let completedSteps = [];

        // 模拟发票数据
        const mockInvoiceData = [
            { code: '3100202130', number: '12345678', date: '2024-01-15', seller: '北京科技有限公司', amount: 10000, tax: 1300, status: 'pending' },
            { code: '3100202130', number: '12345679', date: '2024-01-16', seller: '上海贸易公司', amount: 8500, tax: 1105, status: 'pending' },
            { code: '3100202130', number: '12345680', date: '2024-01-17', seller: '深圳制造企业', amount: 15600, tax: 2028, status: 'pending' },
            { code: '3100202130', number: '12345681', date: '2024-01-18', seller: '广州投资集团', amount: 22000, tax: 2860, status: 'pending' },
            { code: '3100202130', number: '12345682', date: '2024-01-19', seller: '杭州网络科技', amount: 6800, tax: 884, status: 'pending' }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateProgress();
        });

        function initializeEventListeners() {
            // 文件上传事件
            setupFileUpload('csvUploadArea', 'csvFileInput', handleCsvFileUpload);

            // 按钮事件
            document.getElementById('processCsvBtn').addEventListener('click', processCsvData);
            document.getElementById('navigateBtn').addEventListener('click', navigateToAuthentication);
            document.getElementById('selectAllBtn').addEventListener('click', selectAllInvoices);
            document.getElementById('selectUncheckedBtn').addEventListener('click', selectUncheckedInvoices);
            document.getElementById('clearSelectionBtn').addEventListener('click', clearSelection);
            document.getElementById('authenticateBtn').addEventListener('click', executeAuthentication);
            document.getElementById('exportResultsBtn').addEventListener('click', exportResults);

            // 导航菜单事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', handleNavigation);
            });

            // 全选复选框事件
            document.getElementById('selectAllCheckbox').addEventListener('change', handleSelectAllCheckbox);
        }

        function setupFileUpload(uploadAreaId, fileInputId, handler) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);

            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handler);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handler({ target: { files: files } });
                }
            });
        }

        function handleCsvFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const fileName = file.name;
                document.querySelector('#step1 .file-upload-area p').innerHTML = 
                    `<strong>已选择文件：${fileName}</strong><br>准备解析CSV数据`;
                document.getElementById('processCsvBtn').disabled = false;
            }
        }

        function processCsvData() {
            showLoading('正在解析CSV数据...');
            
            setTimeout(() => {
                // 模拟CSV解析
                invoiceData = mockInvoiceData.map((invoice, index) => ({
                    ...invoice,
                    id: index + 1,
                    selected: false
                }));
                
                completedSteps.push('upload_csv');
                completeStep(1);
                activateStep(2);
                updateProgress();
                hideLoading();
                
                console.log('CSV数据解析完成:', invoiceData);
            }, 1500);
        }

        function handleNavigation(event) {
            const navItem = event.currentTarget;
            const navType = navItem.getAttribute('data-nav');
            
            // 移除其他活动状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 设置当前活动状态
            navItem.classList.add('active');
            
            if (navType === 'authentication') {
                document.getElementById('navigateBtn').disabled = false;
            }
            
            console.log('导航到:', navType);
        }

        function navigateToAuthentication() {
            showLoading('正在进入发票认证模块...');
            
            setTimeout(() => {
                completedSteps.push('navigation');
                completeStep(2);
                activateStep(3);
                updateProgress();
                displayInvoiceTable();
                hideLoading();
            }, 1000);
        }

        function displayInvoiceTable() {
            const tableBody = document.getElementById('invoiceTableBody');
            tableBody.innerHTML = '';

            invoiceData.forEach((invoice, index) => {
                const row = document.createElement('tr');
                const statusClass = getStatusClass(invoice.status);
                const statusText = getStatusText(invoice.status);
                
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="invoice-checkbox" 
                               data-index="${index}" 
                               ${invoice.selected ? 'checked' : ''}>
                    </td>
                    <td>${invoice.code}</td>
                    <td>${invoice.number}</td>
                    <td>${invoice.date}</td>
                    <td>${invoice.seller}</td>
                    <td>¥${invoice.amount.toLocaleString()}</td>
                    <td>¥${invoice.tax.toLocaleString()}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                `;
                
                // 添加复选框事件监听
                const checkbox = row.querySelector('.invoice-checkbox');
                checkbox.addEventListener('change', handleInvoiceSelection);
                
                tableBody.appendChild(row);
            });

            document.getElementById('invoiceDataArea').style.display = 'block';
            document.getElementById('invoiceTable').style.display = 'table';
            updateSelectionStats();
        }

        function getStatusClass(status) {
            switch(status) {
                case 'pending': return 'status-pending';
                case 'selected': return 'status-selected';
                case 'authenticated': return 'status-authenticated';
                case 'failed': return 'status-failed';
                default: return 'status-pending';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待认证';
                case 'selected': return '已选择';
                case 'authenticated': return '已认证';
                case 'failed': return '认证失败';
                default: return '待认证';
            }
        }

        function handleInvoiceSelection(event) {
            const checkbox = event.target;
            const index = parseInt(checkbox.getAttribute('data-index'));
            
            invoiceData[index].selected = checkbox.checked;
            invoiceData[index].status = checkbox.checked ? 'selected' : 'pending';
            
            updateRowStatus(index);
            updateSelectionStats();
        }

        function updateRowStatus(index) {
            const row = document.querySelector(`input[data-index="${index}"]`).closest('tr');
            const statusBadge = row.querySelector('.status-badge');
            const invoice = invoiceData[index];
            
            statusBadge.className = `status-badge ${getStatusClass(invoice.status)}`;
            statusBadge.textContent = getStatusText(invoice.status);
        }

        function handleSelectAllCheckbox(event) {
            const isChecked = event.target.checked;
            
            invoiceData.forEach((invoice, index) => {
                invoice.selected = isChecked;
                invoice.status = isChecked ? 'selected' : 'pending';
                
                const checkbox = document.querySelector(`input[data-index="${index}"]`);
                checkbox.checked = isChecked;
                updateRowStatus(index);
            });
            
            updateSelectionStats();
        }

        function selectAllInvoices() {
            document.getElementById('selectAllCheckbox').checked = true;
            handleSelectAllCheckbox({ target: { checked: true } });
        }

        function selectUncheckedInvoices() {
            invoiceData.forEach((invoice, index) => {
                if (invoice.status === 'pending') {
                    invoice.selected = true;
                    invoice.status = 'selected';
                    
                    const checkbox = document.querySelector(`input[data-index="${index}"]`);
                    checkbox.checked = true;
                    updateRowStatus(index);
                }
            });
            
            updateSelectionStats();
        }

        function clearSelection() {
            document.getElementById('selectAllCheckbox').checked = false;
            handleSelectAllCheckbox({ target: { checked: false } });
        }

        function updateSelectionStats() {
            const total = invoiceData.length;
            const selected = invoiceData.filter(inv => inv.selected).length;
            const authenticated = invoiceData.filter(inv => inv.status === 'authenticated').length;
            const totalAmount = invoiceData
                .filter(inv => inv.selected)
                .reduce((sum, inv) => sum + inv.amount + inv.tax, 0);

            document.getElementById('totalInvoices').textContent = total;
            document.getElementById('selectedInvoices').textContent = selected;
            document.getElementById('authenticatedInvoices').textContent = authenticated;
            document.getElementById('totalAmount').textContent = `¥${totalAmount.toLocaleString()}`;
            document.getElementById('selectionStats').style.display = 'grid';
            
            // 启用/禁用认证按钮
            document.getElementById('authenticateBtn').disabled = selected === 0;
        }

        function executeAuthentication() {
            const selectedCount = invoiceData.filter(inv => inv.selected).length;
            if (selectedCount === 0) {
                alert('请先选择要认证的发票');
                return;
            }
            
            showLoading('正在执行发票认证...');
            document.getElementById('authenticationLoading').style.display = 'block';
            
            setTimeout(() => {
                // 模拟认证过程
                let successCount = 0;
                let failCount = 0;
                
                invoiceData.forEach((invoice, index) => {
                    if (invoice.selected) {
                        // 模拟认证结果（90%成功率）
                        const isSuccess = Math.random() > 0.1;
                        invoice.status = isSuccess ? 'authenticated' : 'failed';
                        
                        if (isSuccess) {
                            successCount++;
                            authenticatedInvoices.push(invoice);
                        } else {
                            failCount++;
                        }
                        
                        updateRowStatus(index);
                    }
                });
                
                // 更新统计信息
                updateSelectionStats();
                updateFinalStats(successCount, failCount);
                
                completedSteps.push('authentication');
                completeStep(3);
                activateStep(4);
                updateProgress();
                
                document.getElementById('authenticationLoading').style.display = 'none';
                hideLoading();
                
                console.log(`认证完成 - 成功: ${successCount}, 失败: ${failCount}`);
            }, 3000);
        }

        function updateFinalStats(successCount, failCount) {
            const total = successCount + failCount;
            const successRate = total > 0 ? ((successCount / total) * 100).toFixed(1) : 0;
            
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('failCount').textContent = failCount;
            document.getElementById('successRate').textContent = `${successRate}%`;
            document.getElementById('finalStats').style.display = 'grid';
        }

        function exportResults() {
            // 模拟导出功能
            const exportData = invoiceData.map(invoice => ({
                发票代码: invoice.code,
                发票号码: invoice.number,
                开票日期: invoice.date,
                销售方名称: invoice.seller,
                金额: invoice.amount,
                税额: invoice.tax,
                认证状态: getStatusText(invoice.status)
            }));
            
            console.log('导出认证结果:', exportData);
            alert('认证结果已导出到CSV文件');
            
            // 提交验证
            submitValidation();
        }

        function completeStep(stepNumber) {
            const stepCard = document.getElementById(`step${stepNumber}`);
            stepCard.classList.remove('active');
            stepCard.classList.add('completed');
        }

        function activateStep(stepNumber) {
            if (stepNumber <= 4) {
                const stepCard = document.getElementById(`step${stepNumber}`);
                stepCard.classList.add('active');
                
                // 启用对应的按钮
                switch(stepNumber) {
                    case 3:
                        document.getElementById('selectAllBtn').disabled = false;
                        document.getElementById('selectUncheckedBtn').disabled = false;
                        document.getElementById('clearSelectionBtn').disabled = false;
                        break;
                    case 4:
                        document.getElementById('exportResultsBtn').disabled = false;
                        break;
                }
            }
        }

        function updateProgress() {
            const totalSteps = 4;
            const progress = (completedSteps.length / totalSteps) * 100;
            
            document.getElementById('progressBar').querySelector('.progress-fill').style.width = `${progress}%`;
            
            let progressText = '';
            switch(completedSteps.length) {
                case 0:
                    progressText = '准备开始 - 请上传发票CSV文件';
                    break;
                case 1:
                    progressText = 'CSV数据已解析 - 请导航到认证模块';
                    break;
                case 2:
                    progressText = '已进入认证模块 - 请选择发票并执行认证';
                    break;
                case 3:
                    progressText = '发票认证完成 - 请导出认证结果';
                    break;
                case 4:
                    progressText = '任务完成 - 所有发票认证处理完毕';
                    break;
            }
            
            document.getElementById('progressText').textContent = progressText;
        }

        function showLoading(message) {
            console.log('Loading:', message);
        }

        function hideLoading() {
            // 隐藏加载提示
        }

        async function submitValidation() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const validationData = {
                    completedSteps: completedSteps,
                    invoiceCount: invoiceData.length,
                    selectedCount: invoiceData.filter(inv => inv.selected).length,
                    authenticatedCount: authenticatedInvoices.length,
                    successRate: authenticatedInvoices.length / invoiceData.filter(inv => inv.selected).length,
                    taskCompleted: completedSteps.length === 4
                };

                const response = await fetch('/api/scenarios/6/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(validationData)
                });

                const result = await response.json();
                
                if (result.success && result.valid) {
                    document.getElementById('successMessage').classList.add('show');
                    console.log('场景6验证成功:', result);
                } else {
                    console.error('场景6验证失败:', result.feedback);
                }
            } catch (error) {
                console.error('提交验证失败:', error);
            }
        }
    </script>
</body>
</html>