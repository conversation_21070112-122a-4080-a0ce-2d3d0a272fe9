<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景11：合同关键信息提取与印花税计算申报 (OCR) - RPA财务机器人实训平台</title>
    
    <!-- 样式引入 -->
    <style>
        /* 薄暮天空配色系统 */
        :root {
            --primary-gradient: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            --primary-color: #7673FF;
            --primary-light: rgba(118, 115, 255, 0.15);
            --text-primary: #2c3e50;
            --text-secondary: #8D99AE;
            --bg-color: #F9FAFB;
            --card-bg: #FFFFFF;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --info-color: #3B82F6;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 10px 15px -3px rgba(118, 115, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .header {
            background: var(--primary-gradient);
            padding: 1rem 2rem;
            color: white;
            box-shadow: var(--shadow);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .scenario-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .scenario-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 2rem auto;  
            padding: 0 2rem;
        }

        /* 进度条 */
        .progress-container {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-percentage {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 0.5rem;
            background: #E5E7EB;
            border-radius: 0.25rem;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-steps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }

        .step {
            text-align: center;
            padding: 0.5rem;
        }

        .step-circle {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background: #E5E7EB;
            color: #6B7280;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-circle {
            background: var(--primary-color);
            color: white;
        }

        .step.completed .step-circle {
            background: var(--success-color);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .step.active .step-label {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 操作面板 */
        .operation-panel {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .operation-panel:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .panel-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--primary-color);
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 0.75rem;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FAFBFC;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background: var(--primary-light);
            transform: scale(1.02);
        }

        .upload-icon {
            width: 3rem;
            height: 3rem;
            fill: var(--text-secondary);
            margin: 0 auto 1rem;
        }

        .upload-text {
            font-size: 1.125rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .upload-hint {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* 文件列表 */
        .file-list {
            margin-top: 1.5rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: #F8FAFC;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .file-icon {
            width: 1.5rem;
            height: 1.5rem;
            fill: var(--error-color);
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .file-size {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* 合同信息展示 */
        .contract-info {
            margin-top: 2rem;
        }

        .contract-card {
            background: var(--card-bg);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow);
        }

        .contract-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .contract-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .confidence-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .confidence-high {
            background: #DCFCE7;
            color: #166534;
        }

        .confidence-medium {
            background: #FEF3C7;
            color: #92400E;
        }

        .confidence-low {
            background: #FEE2E2;
            color: #991B1B;
        }

        .contract-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .contract-field {
            display: flex;
            flex-direction: column;
        }

        .field-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .field-value {
            font-weight: 500;
            color: var(--text-primary);
        }

        .field-value.amount {
            font-size: 1.125rem;
            color: var(--primary-color);
            font-weight: 700;
        }

        /* 印花税计算 */
        .tax-calculation {
            background: #F8FAFC;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .calculation-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .calculation-table th,
        .calculation-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .calculation-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .calculation-table .total-row {
            background: #F3F4F6;
            font-weight: 600;
        }

        /* 税率表 */
        .tax-rate-table {
            background: #FFFBEB;
            border: 1px solid #FCD34D;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
        }

        .tax-rate-table h4 {
            color: #92400E;
            margin-bottom: 0.5rem;
        }

        .rate-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .rate-item {
            display: flex;
            justify-content: space-between;
            padding: 0.25rem 0;
        }

        /* 申报表单 */
        .declaration-form {
            background: #F0F9FF;
            border: 1px solid #0EA5E9;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .form-field {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 0.875rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-input {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .form-input[readonly] {
            background: #F9FAFB;
            color: var(--text-secondary);
        }

        /* 按钮样式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #F3F4F6;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E7EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            width: 1rem;
            height: 1rem;
            fill: currentColor;
        }

        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }

        /* 隐藏元素 */
        .hidden {
            display: none;
        }

        /* 加载状态 */
        .loading {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid #E5E7EB;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 提示消息 */
        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #DCFCE7;
            color: #166534;
            border: 1px solid #BBF7D0;
        }

        .alert-info {
            background: #DBEAFE;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
        }

        .alert-warning {
            background: #FEF3C7;
            color: #92400E;
            border: 1px solid #FDE68A;
        }

        .alert-error {
            background: #FEE2E2;
            color: #991B1B;
            border: 1px solid #FECACA;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .progress-steps {
                grid-template-columns: repeat(2, 1fr);
            }

            .contract-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .header-content {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <h1 class="scenario-title">场景11：合同关键信息提取与印花税计算申报 (OCR)</h1>
            <span class="scenario-badge">智能应用模块</span>
        </div>
    </header>

    <!-- 主容器 -->
    <div class="container">
        <!-- 进度跟踪 -->
        <div class="progress-container">
            <div class="progress-header">
                <h2 class="progress-title">任务进度</h2>
                <span class="progress-percentage" id="progressPercentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-steps">
                <div class="step" id="step1">
                    <div class="step-circle">1</div>
                    <div class="step-label">上传合同PDF</div>
                </div>
                <div class="step" id="step2">
                    <div class="step-circle">2</div>
                    <div class="step-label">OCR信息提取</div>
                </div>
                <div class="step" id="step3">
                    <div class="step-circle">3</div>
                    <div class="step-label">印花税计算</div>
                </div>
                <div class="step" id="step4">
                    <div class="step-circle">4</div>
                    <div class="step-label">税务申报</div>
                </div>
            </div>
        </div>

        <!-- 步骤1: 合同上传 -->
        <div class="operation-panel" id="uploadPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                步骤1: 上传合同PDF文件
            </h3>
            <div class="upload-area" id="uploadArea">
                <svg class="upload-icon" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <div class="upload-text">点击或拖拽上传合同PDF文件</div>
                <div class="upload-hint">支持 PDF 格式，最大 20MB</div>
                <input type="file" id="fileInput" class="hidden" accept=".pdf" multiple>
            </div>
            <div class="file-list hidden" id="fileList"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="startOCRBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M12,6.5A2.5,2.5 0 0,1 14.5,4A2.5,2.5 0 0,1 17,6.5C17,8.24 15.16,10.5 12,16.5C8.84,10.5 7,8.24 7,6.5A2.5,2.5 0 0,1 9.5,4A2.5,2.5 0 0,1 12,6.5Z" />
                    </svg>
                    开始OCR提取
                </button>
            </div>
        </div>

        <!-- 步骤2: OCR提取结果 -->
        <div class="operation-panel hidden" id="ocrPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A9,9 0 0,0 21,12A9,9 0 0,0 12,3M12,5A7,7 0 0,1 19,12A7,7 0 0,1 12,19A7,7 0 0,1 5,12A7,7 0 0,1 12,5M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z" />
                </svg>
                步骤2: 合同信息提取结果
            </h3>
            <div class="contract-info" id="contractInfo"></div>
            <div class="action-buttons">
                <button class="btn btn-primary" id="calculateTaxBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H17.5V16H6.5V17.5M6.5,13H17.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z" />
                    </svg>
                    计算印花税
                </button>
            </div>
        </div>

        <!-- 步骤3: 印花税计算 -->
        <div class="operation-panel hidden" id="calculationPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H17.5V16H6.5V17.5M6.5,13H17.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z" />
                </svg>
                步骤3: 印花税计算结果
            </h3>
            
            <!-- 税率参考表 -->
            <div class="tax-rate-table">
                <h4>📋 印花税税率表参考</h4>
                <div class="rate-list">
                    <div class="rate-item">
                        <span>购销合同</span>
                        <span>万分之三</span>
                    </div>
                    <div class="rate-item">
                        <span>加工承揽合同</span>
                        <span>万分之五</span>
                    </div>
                    <div class="rate-item">
                        <span>建设工程勘察设计合同</span>
                        <span>万分之五</span>
                    </div>
                    <div class="rate-item">
                        <span>建筑安装工程承包合同</span>
                        <span>万分之三</span>
                    </div>
                    <div class="rate-item">
                        <span>财产租赁合同</span>
                        <span>千分之一</span>
                    </div>
                    <div class="rate-item">
                        <span>货物运输合同</span>
                        <span>万分之五</span>
                    </div>
                    <div class="rate-item">
                        <span>仓储保管合同</span>
                        <span>千分之一</span>
                    </div>
                    <div class="rate-item">
                        <span>借款合同</span>
                        <span>万分之零点五</span>
                    </div>
                </div>
            </div>

            <div id="taxCalculation"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="submitDeclarationBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                    </svg>
                    提交税务申报
                </button>
            </div>
        </div>

        <!-- 步骤4: 税务申报 -->
        <div class="operation-panel hidden" id="declarationPanel">
            <h3 class="panel-title">
                <svg class="panel-icon" viewBox="0 0 24 24">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                </svg>
                步骤4: 税务申报表
            </h3>
            <div id="declarationForm"></div>
            <div class="action-buttons">
                <button class="btn btn-success" id="completeTaskBtn" disabled>
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                    </svg>
                    完成任务
                </button>
                <button class="btn btn-secondary" id="resetTaskBtn">
                    <svg class="btn-icon" viewBox="0 0 24 24">
                        <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                    </svg>
                    重置任务
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const state = {
            currentStep: 1,
            uploadedFiles: [],
            contractData: [],
            taxCalculations: [],
            declarationData: {},
            completedSteps: [],
            startTime: Date.now()
        };

        // 模拟合同OCR数据
        const mockContractData = [
            {
                filename: "purchase_contract_001.pdf",
                confidence: 0.92,
                data: {
                    contractNumber: "HT2024080001",
                    contractTitle: "设备采购合同",
                    contractType: "购销合同",
                    signingDate: "2024-08-01",
                    partyA: "北京科技有限公司",
                    partyB: "上海制造有限公司",
                    contractAmount: 1500000.00,
                    currency: "人民币",
                    validityPeriod: "2024-08-01至2024-12-31",
                    subject: "生产设备及配件",
                    key_clauses: [
                        "总价款：壹佰伍拾万元整",
                        "付款方式：合同签订后7日内支付30%预付款",
                        "交货期：合同签订后60日内交付",
                        "质保期：设备验收合格后12个月"
                    ]
                }
            },
            {
                filename: "service_contract_002.pdf",
                confidence: 0.88,
                data: {
                    contractNumber: "HT2024080002",
                    contractTitle: "软件开发服务合同",
                    contractType: "加工承揽合同",
                    signingDate: "2024-08-02",
                    partyA: "深圳软件有限公司",
                    partyB: "广州贸易有限公司",
                    contractAmount: 800000.00,
                    currency: "人民币",
                    validityPeriod: "2024-08-02至2024-11-30",
                    subject: "企业管理系统开发",
                    key_clauses: [
                        "开发费用：捌拾万元整",
                        "开发周期：120个工作日",
                        "付款进度：签约30%、中期验收40%、终验收30%",
                        "维护期：系统上线后免费维护12个月"
                    ]
                }
            },
            {
                filename: "lease_contract_003.pdf",
                confidence: 0.95,
                data: {
                    contractNumber: "HT2024080003",
                    contractTitle: "办公场所租赁合同",
                    contractType: "财产租赁合同",
                    signingDate: "2024-08-03",
                    partyA: "杭州地产有限公司",
                    partyB: "杭州创业有限公司",
                    contractAmount: 480000.00,
                    currency: "人民币",
                    validityPeriod: "2024-09-01至2025-08-31",
                    subject: "写字楼租赁",
                    key_clauses: [
                        "年租金：肆拾捌万元整",
                        "月租金：肆万元整",
                        "租赁面积：800平方米",
                        "押金：两个月租金"
                    ]
                }
            }
        ];

        // 印花税税率表
        const stampTaxRates = {
            "购销合同": 0.0003,
            "加工承揽合同": 0.0005,
            "建设工程勘察设计合同": 0.0005,
            "建筑安装工程承包合同": 0.0003,
            "财产租赁合同": 0.001,
            "货物运输合同": 0.0005,
            "仓储保管合同": 0.001,
            "借款合同": 0.00005,
            "财产保险合同": 0.001,
            "技术合同": 0.0003
        };

        // 税率名称映射
        const taxRateNames = {
            0.0003: "万分之三",
            0.0005: "万分之五", 
            0.001: "千分之一",
            0.00005: "万分之零点五"
        };

        // DOM元素引用
        const elements = {
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            fileList: document.getElementById('fileList'),
            startOCRBtn: document.getElementById('startOCRBtn'),
            ocrPanel: document.getElementById('ocrPanel'),
            contractInfo: document.getElementById('contractInfo'),
            calculateTaxBtn: document.getElementById('calculateTaxBtn'),
            calculationPanel: document.getElementById('calculationPanel'),
            taxCalculation: document.getElementById('taxCalculation'),
            submitDeclarationBtn: document.getElementById('submitDeclarationBtn'),
            declarationPanel: document.getElementById('declarationPanel'),
            declarationForm: document.getElementById('declarationForm'),
            completeTaskBtn: document.getElementById('completeTaskBtn'),
            resetTaskBtn: document.getElementById('resetTaskBtn'),
            progressFill: document.getElementById('progressFill'),
            progressPercentage: document.getElementById('progressPercentage')
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateStepDisplay();
            showAlert('info', '请上传合同PDF文件开始信息提取任务');
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            // 文件上传
            elements.uploadArea.addEventListener('click', () => elements.fileInput.click());
            elements.uploadArea.addEventListener('dragover', handleDragOver);
            elements.uploadArea.addEventListener('drop', handleFileDrop);
            elements.fileInput.addEventListener('change', handleFileSelect);

            // 按钮事件
            elements.startOCRBtn.addEventListener('click', startOCR);
            elements.calculateTaxBtn.addEventListener('click', calculateTax);
            elements.submitDeclarationBtn.addEventListener('click', submitDeclaration);
            elements.completeTaskBtn.addEventListener('click', completeTask);
            elements.resetTaskBtn.addEventListener('click', resetTask);
        }

        // 文件拖拽处理
        function handleDragOver(e) {
            e.preventDefault();
            elements.uploadArea.classList.add('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            elements.uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            handleFiles(files);
        }

        // 文件处理
        function handleFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    state.uploadedFiles.push(file);
                    displayFile(file);
                }
            });

            if (state.uploadedFiles.length > 0) {
                elements.startOCRBtn.disabled = false;
                elements.fileList.classList.remove('hidden');
            }
        }

        function validateFile(file) {
            const allowedTypes = ['application/pdf'];
            const maxSize = 20 * 1024 * 1024; // 20MB

            if (!allowedTypes.includes(file.type)) {
                showAlert('error', '不支持的文件格式，请上传 PDF 文件');
                return false;
            }

            if (file.size > maxSize) {
                showAlert('error', '文件大小超过限制，请选择小于 20MB 的文件');
                return false;
            }

            return true;
        }

        function displayFile(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <svg class="file-icon" viewBox="0 0 24 24">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                    </svg>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn btn-danger btn-sm" onclick="removeFile('${file.name}')">删除</button>
                </div>
            `;
            elements.fileList.appendChild(fileItem);
        }

        function removeFile(fileName) {
            state.uploadedFiles = state.uploadedFiles.filter(file => file.name !== fileName);
            if (state.uploadedFiles.length === 0) {
                elements.startOCRBtn.disabled = true;
                elements.fileList.classList.add('hidden');
            }
            // 重新渲染文件列表
            elements.fileList.innerHTML = '';
            state.uploadedFiles.forEach(displayFile);
        }

        // 开始OCR提取
        function startOCR() {
            elements.startOCRBtn.disabled = true;
            elements.startOCRBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    OCR提取中...
                </div>
            `;

            // 模拟OCR处理
            setTimeout(() => {
                state.contractData = mockContractData.slice(0, state.uploadedFiles.length);
                state.completedSteps.push('upload');
                displayContractInfo();
                nextStep();
                showAlert('success', `成功提取 ${state.contractData.length} 份合同信息`);
            }, 3500);
        }

        // 显示合同信息
        function displayContractInfo() {
            elements.contractInfo.innerHTML = '';
            
            state.contractData.forEach((contract, index) => {
                const confidenceClass = contract.confidence >= 0.9 ? 'confidence-high' : 
                                      contract.confidence >= 0.7 ? 'confidence-medium' : 'confidence-low';
                
                const contractCard = document.createElement('div');
                contractCard.className = 'contract-card';
                contractCard.innerHTML = `
                    <div class="contract-header">
                        <div class="contract-title">合同 ${index + 1}: ${contract.filename}</div>
                        <div class="confidence-badge ${confidenceClass}">
                            识别置信度: ${(contract.confidence * 100).toFixed(1)}%
                        </div>
                    </div>
                    <div class="contract-grid">
                        <div class="contract-field">
                            <div class="field-label">合同编号</div>
                            <div class="field-value">${contract.data.contractNumber}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">合同标题</div>
                            <div class="field-value">${contract.data.contractTitle}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">合同类型</div>
                            <div class="field-value">${contract.data.contractType}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">签订日期</div>
                            <div class="field-value">${contract.data.signingDate}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">甲方</div>
                            <div class="field-value">${contract.data.partyA}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">乙方</div>
                            <div class="field-value">${contract.data.partyB}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">合同金额</div>
                            <div class="field-value amount">¥${contract.data.contractAmount.toLocaleString()}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">合同标的</div>
                            <div class="field-value">${contract.data.subject}</div>
                        </div>
                        <div class="contract-field">
                            <div class="field-label">有效期</div>
                            <div class="field-value">${contract.data.validityPeriod}</div>
                        </div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div class="field-label">关键条款</div>
                        <ul style="margin-top: 0.5rem; padding-left: 1.5rem;">
                            ${contract.data.key_clauses.map(clause => `<li style="margin-bottom: 0.25rem;">${clause}</li>`).join('')}
                        </ul>
                    </div>
                `;
                elements.contractInfo.appendChild(contractCard);
            });

            elements.calculateTaxBtn.disabled = false;
            elements.ocrPanel.classList.remove('hidden');
        }

        // 计算印花税
        function calculateTax() {
            elements.calculateTaxBtn.disabled = true;
            elements.calculateTaxBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    计算中...
                </div>
            `;

            setTimeout(() => {
                performTaxCalculation();
                state.completedSteps.push('ocr');
                displayTaxCalculation();
                nextStep();
                showAlert('success', '印花税计算完成！');
            }, 2000);
        }

        // 执行税收计算
        function performTaxCalculation() {
            state.taxCalculations = state.contractData.map((contract, index) => {
                const contractType = contract.data.contractType;
                const contractAmount = contract.data.contractAmount;
                const taxRate = stampTaxRates[contractType] || 0.0003; // 默认税率
                const taxAmount = Math.round(contractAmount * taxRate * 100) / 100; // 保留2位小数
                const minimumTax = 5.00; // 最低税额5元
                const finalTaxAmount = Math.max(taxAmount, minimumTax);

                return {
                    ...contract,
                    taxCalculation: {
                        contractType: contractType,
                        contractAmount: contractAmount,
                        taxRate: taxRate,
                        taxRateName: taxRateNames[taxRate] || `${(taxRate * 10000).toFixed(1)}万分之`,
                        calculatedTax: taxAmount,
                        minimumTax: minimumTax,
                        finalTaxAmount: finalTaxAmount,
                        calculateDate: new Date().toISOString().split('T')[0]
                    }
                };
            });
        }

        // 显示税收计算结果
        function displayTaxCalculation() {
            const totalTax = state.taxCalculations.reduce((sum, calc) => sum + calc.taxCalculation.finalTaxAmount, 0);
            const totalContractAmount = state.taxCalculations.reduce((sum, calc) => sum + calc.data.contractAmount, 0);

            elements.taxCalculation.innerHTML = `
                <div class="tax-calculation">
                    <h4>印花税计算结果</h4>
                    <table class="calculation-table">
                        <thead>
                            <tr>
                                <th>合同编号</th>
                                <th>合同类型</th>
                                <th>合同金额</th>
                                <th>适用税率</th>
                                <th>计算税额</th>
                                <th>应缴税额</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${state.taxCalculations.map(calc => `
                                <tr>
                                    <td>${calc.data.contractNumber}</td>
                                    <td>${calc.taxCalculation.contractType}</td>
                                    <td>¥${calc.data.contractAmount.toLocaleString()}</td>
                                    <td>${calc.taxCalculation.taxRateName}</td>
                                    <td>¥${calc.taxCalculation.calculatedTax.toFixed(2)}</td>
                                    <td>¥${calc.taxCalculation.finalTaxAmount.toFixed(2)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td colspan="2"><strong>合计</strong></td>
                                <td><strong>¥${totalContractAmount.toLocaleString()}</strong></td>
                                <td colspan="2"></td>
                                <td><strong>¥${totalTax.toFixed(2)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div style="margin-top: 1rem; padding: 1rem; background: #F0F9FF; border-radius: 0.5rem; border: 1px solid #0EA5E9;">
                        <h5 style="color: #0C4A6E; margin-bottom: 0.5rem;">💡 税收计算说明</h5>
                        <ul style="color: #075985; font-size: 0.875rem; padding-left: 1.5rem;">
                            <li>印花税按合同金额和对应税率计算</li>
                            <li>每份合同最低税额不少于5元</li>
                            <li>税额计算到分，不足1分的按1分计算</li>
                            <li>同一凭证载有两个或两个以上经济事项的，按税率高的计税</li>
                        </ul>
                    </div>
                </div>
            `;

            elements.submitDeclarationBtn.disabled = false;
            elements.calculationPanel.classList.remove('hidden');
        }

        // 提交税务申报
        function submitDeclaration() {
            elements.submitDeclarationBtn.disabled = true;
            elements.submitDeclarationBtn.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    生成申报表...
                </div>
            `;

            setTimeout(() => {
                generateDeclarationForm();
                state.completedSteps.push('calculation');
                nextStep();
                showAlert('success', '税务申报表已生成！');
            }, 1500);
        }

        // 生成申报表单
        function generateDeclarationForm() {
            const totalTax = state.taxCalculations.reduce((sum, calc) => sum + calc.taxCalculation.finalTaxAmount, 0);
            const currentDate = new Date().toISOString().split('T')[0];
            const declarationNumber = `SB${Date.now()}`;

            state.declarationData = {
                declarationNumber: declarationNumber,
                declarationDate: currentDate,
                taxpayerName: "北京科技有限公司", // 示例纳税人
                taxpayerTaxId: "91110108MA01234567", // 示例税号
                declarationPeriod: currentDate.substring(0, 7), // 当前年月
                totalContracts: state.taxCalculations.length,
                totalContractAmount: state.taxCalculations.reduce((sum, calc) => sum + calc.data.contractAmount, 0),
                totalTaxAmount: totalTax,
                contractDetails: state.taxCalculations
            };

            elements.declarationForm.innerHTML = `
                <div class="declaration-form">
                    <h4>🏛️ 印花税纳税申报表</h4>
                    <div class="form-grid">
                        <div class="form-field">
                            <label class="form-label">申报表编号</label>
                            <input type="text" class="form-input" value="${state.declarationData.declarationNumber}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">申报日期</label>
                            <input type="date" class="form-input" value="${state.declarationData.declarationDate}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">纳税人名称</label>
                            <input type="text" class="form-input" value="${state.declarationData.taxpayerName}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">纳税人识别号</label>
                            <input type="text" class="form-input" value="${state.declarationData.taxpayerTaxId}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">申报期间</label>
                            <input type="text" class="form-input" value="${state.declarationData.declarationPeriod}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">合同份数</label>
                            <input type="number" class="form-input" value="${state.declarationData.totalContracts}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">合同总金额</label>
                            <input type="text" class="form-input" value="¥${state.declarationData.totalContractAmount.toLocaleString()}" readonly>
                        </div>
                        <div class="form-field">
                            <label class="form-label">应纳税额</label>
                            <input type="text" class="form-input" value="¥${state.declarationData.totalTaxAmount.toFixed(2)}" readonly style="font-weight: 700; color: var(--primary-color);">
                        </div>
                    </div>

                    <div style="margin-top: 2rem;">
                        <h5>申报明细</h5>
                        <table class="calculation-table" style="margin-top: 1rem;">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>合同编号</th>
                                    <th>合同类型</th>
                                    <th>合同金额</th>
                                    <th>税率</th>
                                    <th>应纳税额</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${state.taxCalculations.map((calc, index) => `
                                    <tr>
                                        <td>${index + 1}</td>
                                        <td>${calc.data.contractNumber}</td>
                                        <td>${calc.taxCalculation.contractType}</td>
                                        <td>¥${calc.data.contractAmount.toLocaleString()}</td>
                                        <td>${calc.taxCalculation.taxRateName}</td>
                                        <td>¥${calc.taxCalculation.finalTaxAmount.toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                                <tr class="total-row">
                                    <td colspan="3"><strong>合计</strong></td>
                                    <td><strong>¥${state.declarationData.totalContractAmount.toLocaleString()}</strong></td>
                                    <td></td>
                                    <td><strong>¥${state.declarationData.totalTaxAmount.toFixed(2)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div style="margin-top: 2rem; padding: 1rem; background: #DCFCE7; border-radius: 0.5rem; border: 1px solid #10B981;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; color: #166534; font-weight: 600;">
                            <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                            </svg>
                            申报状态：准备提交
                        </div>
                        <p style="margin-top: 0.5rem; color: #166534; font-size: 0.875rem;">
                            申报表已生成完成，请核对信息无误后点击"完成任务"按钮提交申报。
                        </p>
                    </div>
                </div>
            `;

            elements.completeTaskBtn.disabled = false;
            elements.declarationPanel.classList.remove('hidden');
        }

        // 完成任务
        function completeTask() {
            state.completedSteps.push('declaration');
            updateStepDisplay();
            
            // 准备提交数据
            const taskData = {
                completedSteps: state.completedSteps,
                uploadedFileCount: state.uploadedFiles.length,
                extractedContractCount: state.contractData.length,
                calculatedTaxCount: state.taxCalculations.length,
                totalContractAmount: state.declarationData.totalContractAmount,
                totalTaxAmount: state.declarationData.totalTaxAmount,
                averageConfidence: state.contractData.reduce((sum, c) => sum + c.confidence, 0) / state.contractData.length,
                declarationGenerated: !!state.declarationData.declarationNumber,
                contractTypes: [...new Set(state.contractData.map(c => c.data.contractType))],
                timeSpent: Math.floor((Date.now() - state.startTime) / 1000),
                taskCompleted: true
            };

            // 提交验证
            submitTaskValidation(taskData);
        }

        // 提交任务验证
        function submitTaskValidation(taskData) {
            // 模拟API调用
            fetch('/api/scenarios/11/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(taskData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.valid) {
                    showAlert('success', `🎉 ${data.feedback} 得分: ${data.score}分`);
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', data.feedback || '任务验证未通过，请检查操作步骤');
                }
            })
            .catch(error => {
                console.error('任务验证失败:', error);
                // 本地验证逻辑
                if (validateLocalTask(taskData)) {
                    showAlert('success', '🎉 恭喜！您已成功完成合同信息提取与印花税计算申报任务！');
                    elements.completeTaskBtn.disabled = true;
                    elements.completeTaskBtn.innerHTML = '✅ 任务已完成';
                } else {
                    showAlert('warning', '任务验证未通过，请确保完成所有步骤');
                }
            });
        }

        // 本地任务验证
        function validateLocalTask(data) {
            const requiredSteps = ['upload', 'ocr', 'calculation', 'declaration'];
            const stepsCompleted = requiredSteps.every(step => 
                data.completedSteps && data.completedSteps.includes(step)
            );
            
            return stepsCompleted && 
                   data.uploadedFileCount >= 1 && 
                   data.extractedContractCount >= 1 && 
                   data.calculatedTaxCount >= 1 &&
                   data.totalTaxAmount > 0 &&
                   data.averageConfidence >= 0.7 &&
                   data.declarationGenerated;
        }

        // 重置任务
        function resetTask() {
            if (confirm('确定要重置任务吗？所有进度将丢失。')) {
                location.reload();
            }
        }

        // 步骤管理
        function nextStep() {
            if (state.currentStep < 4) {
                state.currentStep++;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // 更新进度条
            const progress = (state.currentStep - 1) * 25;
            elements.progressFill.style.width = `${progress}%`;
            elements.progressPercentage.textContent = `${progress}%`;

            // 更新步骤状态
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                
                if (i < state.currentStep) {
                    step.classList.add('completed');
                } else if (i === state.currentStep) {
                    step.classList.add('active');
                }
            }
        }

        // 显示提示消息
        function showAlert(type, message) {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `
                <svg style="width: 1.25rem; height: 1.25rem; fill: currentColor;" viewBox="0 0 24 24">
                    <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                </svg>
                ${message}
            `;

            document.querySelector('.container').insertBefore(alert, document.querySelector('.progress-container'));

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // 暴露全局函数供HTML调用
        window.removeFile = removeFile;
    </script>
</body>
</html>