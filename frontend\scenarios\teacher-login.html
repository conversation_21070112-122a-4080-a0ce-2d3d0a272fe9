<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师登录 - RPA财务机器人实训平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 50px rgba(92, 123, 255, 0.25);
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #8D99AE;
            font-size: 14px;
        }
        
        .teacher-badge {
            display: inline-block;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118, 115, 255, 0.15);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #7673FF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(118, 115, 255, 0.3);
        }
        
        .btn:hover {
            background: #6366F1;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(118, 115, 255, 0.4);
        }
        
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #7673FF;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .demo-accounts {
            background: #F9FAFB;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 12px;
            color: #8D99AE;
        }
        
        .demo-accounts h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .demo-account {
            margin-bottom: 5px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s;
        }
        
        .demo-account:hover {
            background: #e0f2fe;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features {
            background: #F9FAFB;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 12px;
            color: #8D99AE;
        }

        .features h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .features ul {
            margin-left: 16px;
        }

        .features li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🤖 RPA实训平台</h1>
            <p>财务机器人入门与进阶实训平台 v1.1</p>
            <div class="teacher-badge">👨‍🏫 教师管理后台</div>
        </div>
        
        <div id="alertMessage" class="alert" style="display: none;"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">教师工号/用户名</label>
                <input type="text" id="username" class="form-control" placeholder="请输入教师工号或用户名" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" class="form-control" placeholder="请输入密码" required>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <span>正在登录...</span>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">登录管理后台</button>
        </form>
        
        <div class="links">
            <a href="login.html">学生登录入口</a>
        </div>
        
        <div class="demo-accounts">
            <h4>📚 演示账号</h4>
            <div class="demo-account">教师: teacher1 / 123456</div>
            <div class="demo-account">测试: test / 123456</div>
        </div>

        <div class="features">
            <h4>🎯 管理功能</h4>
            <ul>
                <li>学生进度实时监控</li>
                <li>任务完成状态追踪</li>
                <li>班级管理与统计</li>
                <li>学习数据导出</li>
            </ul>
        </div>
    </div>

    <script>
        class TeacherLogin {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.usernameInput = document.getElementById('username');
                this.passwordInput = document.getElementById('password');
                this.loginBtn = document.getElementById('loginBtn');
                this.loading = document.getElementById('loading');
                this.alertMessage = document.getElementById('alertMessage');
                
                this.init();
            }

            init() {
                this.form.addEventListener('submit', (e) => this.handleLogin(e));
                
                // 检查是否已经登录
                this.checkExistingAuth();
                
                // 演示账号快速填充
                this.addDemoAccountHandlers();
            }

            checkExistingAuth() {
                const token = localStorage.getItem('token');
                const user = localStorage.getItem('user');
                
                if (token && user) {
                    try {
                        const userData = JSON.parse(user);
                        if (userData.role === 'teacher') {
                            this.verifyToken(token);
                        }
                    } catch (error) {
                        // 清除无效数据
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                }
            }

            async verifyToken(token) {
                try {
                    const response = await fetch('/api/auth/verify', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success && result.data.user.role === 'teacher') {
                            // 已经登录，跳转到教师管理后台
                            window.location.href = 'teacher-dashboard.html';
                            return;
                        }
                    }
                    
                    // Token无效，清除本地存储
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                } catch (error) {
                    console.error('Token验证失败:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            }

            async handleLogin(e) {
                e.preventDefault();
                
                const username = this.usernameInput.value.trim();
                const password = this.passwordInput.value;

                if (!username || !password) {
                    this.showAlert('请填写完整的登录信息', 'error');
                    return;
                }

                this.setLoading(true);

                try {
                    const response = await fetch('/api/auth/teacher/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 保存认证信息
                        localStorage.setItem('token', result.token);
                        localStorage.setItem('user', JSON.stringify(result.user));
                        
                        this.showAlert('登录成功，正在跳转到管理后台...', 'success');
                        
                        // 延迟跳转，让用户看到成功消息
                        setTimeout(() => {
                            window.location.href = 'teacher-dashboard.html';
                        }, 1000);
                    } else {
                        this.showAlert(result.message || '登录失败', 'error');
                    }
                } catch (error) {
                    console.error('登录请求失败:', error);
                    this.showAlert('网络连接失败，请检查网络设置', 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            setLoading(isLoading) {
                if (isLoading) {
                    this.loading.style.display = 'block';
                    this.loginBtn.disabled = true;
                    this.loginBtn.textContent = '登录中...';
                } else {
                    this.loading.style.display = 'none';
                    this.loginBtn.disabled = false;
                    this.loginBtn.textContent = '登录管理后台';
                }
            }

            showAlert(message, type) {
                this.alertMessage.textContent = message;
                this.alertMessage.className = `alert alert-${type}`;
                this.alertMessage.style.display = 'block';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    this.alertMessage.style.display = 'none';
                }, 3000);
            }

            addDemoAccountHandlers() {
                // 点击演示账号自动填充
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('demo-account')) {
                        const text = e.target.textContent;
                        const match = text.match(/(\w+)\s*\/\s*(\w+)/);
                        if (match) {
                            this.usernameInput.value = match[1];
                            this.passwordInput.value = match[2];
                            
                            // 高亮显示
                            e.target.style.background = '#e0f2fe';
                            setTimeout(() => {
                                e.target.style.background = '';
                            }, 1000);
                        }
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new TeacherLogin();
        });
    </script>
</body>
</html>