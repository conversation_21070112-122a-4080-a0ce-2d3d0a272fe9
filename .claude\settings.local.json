{"permissions": {"allow": ["WebFetch(domain:tcb.cloud.tencent.com)", "Bash(npm run dev:*)", "Bash(where nodemon)", "Bash(npm cache clean:*)", "Bash(npm install)", "Bash(node server.js)", "Bash(npm install:*)", "Bash(npm view:*)", "Bash(node:*)", "Bash(npm start)", "Bash(git init:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(commit-current-work.bat)", "Bash(find:*)", "Bash(\"D:\\G盘（软件）\\cursor开发文件\\claude-RPA实训平台3.0\\commit-scenario9.bat\")"], "deny": []}}