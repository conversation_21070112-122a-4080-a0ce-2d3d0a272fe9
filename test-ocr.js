/**
 * OCR功能快速测试脚本
 * 验证OCR服务的核心功能
 */

const ocrService = require('./api/services/ocr-service');
const fs = require('fs');
const path = require('path');

async function testOCRService() {
    console.log('🧪 开始测试OCR服务...\n');

    try {
        // 1. 测试Mock发票识别
        console.log('📄 测试1: Mock发票识别');
        const mockResult = await ocrService.recognizeInvoice(null, '测试发票.jpg');
        console.log('✅ Mock识别成功:', {
            filename: mockResult.filename,
            confidence: `${(mockResult.confidence * 100).toFixed(1)}%`,
            invoiceNumber: mockResult.data.invoiceNumber,
            totalAmount: `¥${mockResult.data.totalAmount.toFixed(2)}`
        });
        console.log('');

        // 2. 测试批量识别
        console.log('📄 测试2: 批量发票识别');
        const batchData = [
            { data: Buffer.from('fake-image-1'), filename: '发票1.jpg' },
            { data: Buffer.from('fake-image-2'), filename: '发票2.jpg' },
            { data: Buffer.from('fake-image-3'), filename: '发票3.jpg' }
        ];
        
        const batchResults = await ocrService.batchRecognizeInvoices(batchData);
        console.log('✅ 批量识别成功:', {
            totalFiles: batchResults.length,
            successCount: batchResults.filter(r => r.success).length,
            totalAmount: batchResults
                .filter(r => r.success)
                .reduce((sum, r) => sum + r.data.totalAmount, 0)
                .toFixed(2)
        });
        console.log('');

        // 3. 测试OCR结果验证
        console.log('🔍 测试3: OCR结果验证');
        const validation = ocrService.validateOCRResult(mockResult);
        console.log('✅ 验证成功:', {
            isValid: validation.isValid,
            errors: validation.errors,
            confidence: `${(validation.confidence * 100).toFixed(1)}%`
        });
        console.log('');

        // 4. 测试通用OCR
        console.log('📋 测试4: 通用文档OCR');
        const generalResult = await ocrService.recognizeGeneral(Buffer.from('fake-doc'), '合同文档.pdf');
        console.log('✅ 通用OCR成功:', {
            filename: generalResult.filename,
            confidence: `${(generalResult.confidence * 100).toFixed(1)}%`,
            lineCount: generalResult.data.lineCount
        });
        console.log('');

        // 5. 测试错误处理
        console.log('❌ 测试5: 错误处理');
        try {
            const invalidResult = ocrService.validateOCRResult(null);
            console.log('✅ 错误处理正常:', invalidResult);
        } catch (error) {
            console.log('✅ 错误处理正常:', error.message);
        }
        console.log('');

        console.log('🎉 OCR服务测试全部通过！');
        console.log('\n📊 测试总结:');
        console.log('- ✅ Mock发票识别: 正常');
        console.log('- ✅ 批量处理: 正常');
        console.log('- ✅ 结果验证: 正常');
        console.log('- ✅ 通用OCR: 正常');
        console.log('- ✅ 错误处理: 正常');
        console.log('\n💡 提示: 要启用真实OCR功能，请配置 .env 文件中的百度API密钥');

    } catch (error) {
        console.error('❌ OCR服务测试失败:', error);
        process.exit(1);
    }
}

async function testAPIRoutes() {
    console.log('\n🔗 测试API路由结构...');
    
    try {
        // 检查OCR路由文件
        const ocrRouterPath = path.join(__dirname, 'api/routes/ocr.js');
        if (fs.existsSync(ocrRouterPath)) {
            console.log('✅ OCR路由文件存在');
        } else {
            console.log('❌ OCR路由文件不存在');
        }

        // 检查OCR服务文件
        const ocrServicePath = path.join(__dirname, 'api/services/ocr-service.js');
        if (fs.existsSync(ocrServicePath)) {
            console.log('✅ OCR服务文件存在');
        } else {
            console.log('❌ OCR服务文件不存在');
        }

        // 检查前端OCR页面
        const ocrPagePath = path.join(__dirname, 'frontend/scenarios/invoice-ocr-verification.html');
        if (fs.existsSync(ocrPagePath)) {
            console.log('✅ OCR前端页面存在');
        } else {
            console.log('❌ OCR前端页面不存在');
        }

        console.log('✅ API路由结构检查完成');

    } catch (error) {
        console.error('❌ API路由测试失败:', error);
    }
}

async function runAllTests() {
    console.log('🚀 RPA实训平台 OCR功能测试\n');
    console.log('=' .repeat(50));
    
    await testOCRService();
    await testAPIRoutes();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 测试完成！OCR功能已准备就绪');
    console.log('\n🔧 下一步建议:');
    console.log('1. 启动服务器: npm start');
    console.log('2. 访问场景9: http://localhost:3000/scenarios/invoice-ocr-verification.html');
    console.log('3. 上传发票图片测试OCR功能');
    console.log('4. 配置百度API密钥以启用真实OCR识别');
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testOCRService,
    testAPIRoutes,
    runAllTests
};