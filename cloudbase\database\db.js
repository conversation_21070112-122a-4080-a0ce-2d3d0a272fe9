/**
 * 腾讯云云开发数据库配置
 * 基于PRD要求的BaaS架构
 */

const cloudbase = require('@cloudbase/node-sdk');

// 初始化云开发环境
const tcb = cloudbase.init({
  env: process.env.TCB_ENV_ID,
  secretId: process.env.TCB_SECRET_ID,
  secretKey: process.env.TCB_SECRET_KEY
});

const db = tcb.database();

// 数据库集合名称常量
const COLLECTIONS = {
  USERS: 'users',
  CLASSES: 'classes', 
  SCENARIOS: 'training_scenarios',
  PROGRESS: 'student_progress',
  SUBMISSIONS: 'student_submissions'
};

/**
 * 初始化数据库集合和基础数据
 */
async function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    
    // 初始化12个PRD要求的训练场景
    await initTrainingScenarios();
    console.log('训练场景初始化完成');
    
    // 创建测试数据
    await initTestData();
    console.log('测试数据初始化完成');
    
    console.log('数据库初始化成功');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

/**
 * 初始化PRD中定义的12个训练场景
 */
async function initTrainingScenarios() {
  const scenarios = [
    // 第一模块：基础操作与数据搬运
    {
      id: 1,
      title: '企业网银流水查询下载',
      code: 'bank-statement-download', 
      description: '学习企业网银操作流程，掌握流水查询和批量下载技能',
      module: 1,
      module_name: '基础操作',
      difficulty: 'beginner',
      estimated_time: 30,
      validation_rules: {
        required_elements: ['login-form', 'query-form', 'download-btn'],
        success_criteria: 'download_completed',
        score_rules: {
          login: 20,
          query: 30, 
          download: 50
        }
      },
      is_active: true
    },
    {
      id: 2,
      title: '批量开具电子发票',
      code: 'batch-invoice-creation',
      description: '从Excel读取订单信息，在开票系统网页中为每笔订单开具发票',
      module: 1,
      module_name: '基础操作',
      difficulty: 'beginner',
      estimated_time: 35,
      validation_rules: {
        required_elements: ['excel-upload', 'invoice-form', 'batch-process'],
        success_criteria: 'all_invoices_created'
      },
      is_active: true
    },
    {
      id: 3,
      title: '固定资产卡片信息核对',
      code: 'asset-verification',
      description: '根据Excel清单，在固资系统查询并核对资产信息，将结果写回Excel',
      module: 1,
      module_name: '基础操作',
      difficulty: 'beginner',
      estimated_time: 40,
      validation_rules: {
        required_elements: ['asset-search', 'verification-form', 'result-export'],
        success_criteria: 'verification_completed'
      },
      is_active: true
    },
    {
      id: 4,
      title: '税务申报期提醒与状态核查',
      code: 'tax-status-check',
      description: '登录电子税务局，抓取本月申报日历，并核查上月各税种申报状态',
      module: 1,
      module_name: '基础操作', 
      difficulty: 'beginner',
      estimated_time: 25,
      validation_rules: {
        required_elements: ['tax-login', 'calendar-view', 'status-check'],
        success_criteria: 'status_retrieved'
      },
      is_active: true
    },
    
    // 第二模块：跨系统流程整合
    {
      id: 5,
      title: '应收账款对账与核销',
      code: 'receivable-reconciliation',
      description: '整合销售系统订单和银行流水数据，匹配成功后在财务系统中核销',
      module: 2,
      module_name: '流程整合',
      difficulty: 'intermediate',
      estimated_time: 50,
      validation_rules: {
        required_elements: ['data-integration', 'matching-algorithm', 'reconciliation'],
        success_criteria: 'reconciliation_completed'
      },
      is_active: true
    },
    {
      id: 6,
      title: '增值税进项税发票认证',
      code: 'vat-invoice-verification',
      description: '从CSV文件读取发票信息，在增值税平台进行批量勾选认证',
      module: 2,
      module_name: '流程整合',
      difficulty: 'intermediate',
      estimated_time: 35,
      validation_rules: {
        required_elements: ['csv-import', 'batch-selection', 'verification-process'],
        success_criteria: 'verification_completed'
      },
      is_active: true
    },
    {
      id: 7,
      title: '月度工资条数据计算与生成',
      code: 'payroll-calculation',
      description: '整合考勤表和绩效表，根据规则计算薪酬，为每人生成独立工资条文件',
      module: 2,
      module_name: '流程整合',
      difficulty: 'intermediate',
      estimated_time: 45,
      validation_rules: {
        required_elements: ['data-integration', 'calculation-engine', 'file-generation'],
        success_criteria: 'payroll_generated'
      },
      is_active: true
    },
    {
      id: 8,
      title: '财务报表数据自动汇总',
      code: 'financial-report-consolidation',
      description: '登录多个子公司系统，抓取关键财务数据，汇总到集团合并报表模板中',
      module: 2,
      module_name: '流程整合',
      difficulty: 'intermediate',
      estimated_time: 55,
      validation_rules: {
        required_elements: ['multi-system-login', 'data-extraction', 'consolidation'],
        success_criteria: 'report_generated'
      },
      is_active: true
    },
    
    // 第三模块：智能审核与综合应用
    {
      id: 9,
      title: '供应商发票批量验真与入账(OCR)',
      code: 'invoice-ocr-verification',
      description: '用OCR识别发票图片，提取信息录入系统验真，成功后生成会计分录',
      module: 3,
      module_name: '智能应用',
      difficulty: 'advanced',
      estimated_time: 40,
      validation_rules: {
        required_elements: ['ocr-processing', 'verification-api', 'accounting-entry'],
        success_criteria: 'invoice_processed'
      },
      is_active: true
    },
    {
      id: 10,
      title: '员工差旅费报销智能初审(人机协作)',
      code: 'expense-intelligent-review',
      description: '根据报销制度对报销单进行预审，合规通过，不合规或存疑则转交人工',
      module: 3,
      module_name: '智能应用',
      difficulty: 'advanced',
      estimated_time: 45,
      validation_rules: {
        required_elements: ['rule-engine', 'intelligent-review', 'human-handover'],
        success_criteria: 'review_completed'
      },
      is_active: true
    },
    {
      id: 11,
      title: '合同关键信息提取与印花税计算申报(OCR)',
      code: 'contract-tax-calculation',
      description: '用OCR识别合同PDF，提取金额、类型等，根据税率表计算印花税并填报',
      module: 3,
      module_name: '智能应用',
      difficulty: 'advanced',
      estimated_time: 50,
      validation_rules: {
        required_elements: ['pdf-processing', 'ocr-extraction', 'tax-calculation'],
        success_criteria: 'tax_calculated'
      },
      is_active: true
    },
    {
      id: 12,
      title: '自动生成总账科目余额调节表',
      code: 'balance-reconciliation-table',
      description: '分别获取银行对账单和系统日记账余额，结合未达账项清单，自动编制余额调节表',
      module: 3,
      module_name: '智能应用',
      difficulty: 'advanced',
      estimated_time: 60,
      validation_rules: {
        required_elements: ['balance-extraction', 'reconciliation-logic', 'table-generation'],
        success_criteria: 'table_generated'
      },
      is_active: true
    }
  ];

  const collection = db.collection(COLLECTIONS.SCENARIOS);
  
  // 先查询现有数据，如果有则删除
  try {
    const existingData = await collection.get();
    if (existingData.data && existingData.data.length > 0) {
      // 使用批量删除，每次删除的记录需要指定_id
      for (const item of existingData.data) {
        await collection.doc(item._id).remove();
      }
      console.log(`删除了${existingData.data.length}条现有场景数据`);
    }
    
    // 插入新数据
    await collection.add(scenarios);
    console.log(`成功初始化${scenarios.length}个训练场景`);
  } catch (error) {
    console.error('初始化训练场景失败:', error);
    throw error;
  }
}

/**
 * 初始化测试数据
 */
async function initTestData() {
  // 创建测试教师
  const teacherData = {
    username: 'teacher1',
    password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq/3Haa', // password123
    name: '张老师',
    email: '<EMAIL>',
    role: 'teacher',
    department: '财务学院',
    status: 'active',
    created_at: new Date()
  };

  // 创建测试班级
  const classData = {
    class_name: 'RPA实训班2024秋季',
    description: 'RPA财务机器人实训班，学习各种财务自动化场景',
    teacher_id: 'teacher1',
    max_students: 50,
    status: 'active',
    created_at: new Date()
  };

  // 创建测试学生
  const studentData = [
    {
      username: 'student1',
      password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq/3Haa', // password123
      name: '李小明',
      student_id: '2024001',
      email: '<EMAIL>',
      role: 'student',
      class_id: 'class1',
      status: 'active',
      created_at: new Date()
    },
    {
      username: 'student2', 
      password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq/3Haa',
      name: '王小红',
      student_id: '2024002',
      email: '<EMAIL>',
      role: 'student',
      class_id: 'class1',
      status: 'active',
      created_at: new Date()
    }
  ];

  try {
    // 检查是否已存在测试数据，避免重复插入
    const existingUsers = await db.collection(COLLECTIONS.USERS).where({
      username: db.command.in(['teacher1', 'student1', 'student2'])
    }).get();
    
    if (existingUsers.data.length === 0) {
      // 插入测试用户数据
      await db.collection(COLLECTIONS.USERS).add([teacherData, ...studentData]);
      console.log('测试用户数据插入完成');
    } else {
      console.log('测试用户数据已存在，跳过插入');
    }
    
    // 检查班级数据
    const existingClasses = await db.collection(COLLECTIONS.CLASSES).where({
      class_name: classData.class_name
    }).get();
    
    if (existingClasses.data.length === 0) {
      await db.collection(COLLECTIONS.CLASSES).add(classData);
      console.log('测试班级数据插入完成');
    } else {
      console.log('测试班级数据已存在，跳过插入');
    }
    
    console.log('测试数据初始化完成');
  } catch (error) {
    console.error('测试数据初始化失败:', error);
  }
}

module.exports = {
  db,
  tcb,
  COLLECTIONS,
  initDatabase
};