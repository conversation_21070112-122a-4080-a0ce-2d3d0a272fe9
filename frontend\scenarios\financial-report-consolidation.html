<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景8：财务报表数据自动汇总系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #8D99AE;
            font-size: 16px;
        }

        .workflow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .step-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
        }

        .step-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(118, 115, 255, 0.25);
        }

        .step-card.active {
            border-color: #7673FF;
            background: rgba(118, 115, 255, 0.05);
        }

        .step-card.completed {
            border-color: #27AE60;
            background: rgba(39, 174, 96, 0.05);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        .step-card.completed .step-number {
            background: #27AE60;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .step-content {
            margin-bottom: 20px;
        }

        .company-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .company-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            margin: 8px 0;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .company-item:hover {
            background: rgba(118, 115, 255, 0.05);
            border-color: #7673FF;
        }

        .company-item.connected {
            background: rgba(39, 174, 96, 0.1);
            border-color: #27AE60;
        }

        .company-info {
            display: flex;
            align-items: center;
        }

        .company-icon {
            font-size: 20px;
            margin-right: 10px;
        }

        .company-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .company-status {
            font-size: 12px;
            color: #8D99AE;
            margin-left: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }

        .status-pending {
            background: #F39C12;
        }

        .status-connected {
            background: #27AE60;
        }

        .status-failed {
            background: #E74C3C;
        }

        .data-extraction-panel {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
        }

        .extraction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: rgba(118, 115, 255, 0.05);
            border-radius: 8px;
            margin: 8px 0;
        }

        .extraction-label {
            font-weight: 500;
            color: #2c3e50;
        }

        .extraction-progress {
            display: flex;
            align-items: center;
        }

        .progress-bar-mini {
            width: 80px;
            height: 6px;
            background: #E9ECEF;
            border-radius: 3px;
            overflow: hidden;
            margin-right: 10px;
        }

        .progress-fill-mini {
            height: 100%;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
        }

        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
            font-size: 13px;
        }

        .data-table tr:hover {
            background: rgba(118, 115, 255, 0.05);
        }

        .data-table .number-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(118, 115, 255, 0.4);
        }

        .btn-success {
            background: #27AE60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #F39C12;
            color: white;
        }

        .btn-warning:hover {
            background: #E67E22;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-indicator {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #E9ECEF;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #7673FF;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 11px;
            color: #8D99AE;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .consolidated-report {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .report-header {
            text-align: center;
            border-bottom: 2px solid #7673FF;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }

        .report-period {
            font-size: 14px;
            color: #8D99AE;
            margin-top: 5px;
        }

        .report-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 4px solid #7673FF;
        }

        .success-message {
            background: rgba(39, 174, 96, 0.1);
            border: 2px solid #27AE60;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .success-message.show {
            display: block;
            animation: slideDown 0.5s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #7673FF;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>财务报表数据自动汇总系统</h1>
            <p>登录多个子公司系统，抓取关键财务数据，汇总到集团合并报表模板中</p>
        </div>

        <div class="progress-indicator">
            <h3>任务进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            <p id="progressText">准备开始 - 连接子公司系统</p>
        </div>

        <div class="workflow">
            <!-- 步骤1：连接子公司系统 -->
            <div class="step-card active" id="step1">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">连接子公司</div>
                </div>
                <div class="step-content">
                    <div class="company-list">
                        <h4>子公司列表</h4>
                        <div class="company-item" data-company="subsidiary1">
                            <div class="company-info">
                                <span class="company-icon">🏢</span>
                                <div>
                                    <div class="company-name">北京科技子公司</div>
                                    <div class="company-status">待连接</div>
                                </div>
                            </div>
                            <span class="status-indicator status-pending"></span>
                        </div>
                        <div class="company-item" data-company="subsidiary2">
                            <div class="company-info">
                                <span class="company-icon">🏭</span>
                                <div>
                                    <div class="company-name">上海制造子公司</div>
                                    <div class="company-status">待连接</div>
                                </div>
                            </div>
                            <span class="status-indicator status-pending"></span>
                        </div>
                        <div class="company-item" data-company="subsidiary3">
                            <div class="company-info">
                                <span class="company-icon">🏪</span>
                                <div>
                                    <div class="company-name">深圳贸易子公司</div>
                                    <div class="company-status">待连接</div>
                                </div>
                            </div>
                            <span class="status-indicator status-pending"></span>
                        </div>
                        <div class="company-item" data-company="subsidiary4">
                            <div class="company-info">
                                <span class="company-icon">🏛️</span>
                                <div>
                                    <div class="company-name">广州投资子公司</div>
                                    <div class="company-status">待连接</div>
                                </div>
                            </div>
                            <span class="status-indicator status-pending"></span>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="connectCompaniesBtn">批量连接系统</button>
                </div>
            </div>

            <!-- 步骤2：数据抓取 -->
            <div class="step-card" id="step2">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">数据抓取</div>
                </div>
                <div class="step-content">
                    <div class="data-extraction-panel">
                        <h4>财务数据抓取</h4>
                        <div class="extraction-item">
                            <span class="extraction-label">资产负债表</span>
                            <div class="extraction-progress">
                                <div class="progress-bar-mini">
                                    <div class="progress-fill-mini" id="balanceProgress"></div>
                                </div>
                                <span id="balanceStatus">待处理</span>
                            </div>
                        </div>
                        <div class="extraction-item">
                            <span class="extraction-label">利润表</span>
                            <div class="extraction-progress">
                                <div class="progress-bar-mini">
                                    <div class="progress-fill-mini" id="incomeProgress"></div>
                                </div>
                                <span id="incomeStatus">待处理</span>
                            </div>
                        </div>
                        <div class="extraction-item">
                            <span class="extraction-label">现金流量表</span>
                            <div class="extraction-progress">
                                <div class="progress-bar-mini">
                                    <div class="progress-fill-mini" id="cashflowProgress"></div>
                                </div>
                                <span id="cashflowStatus">待处理</span>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-success" id="extractDataBtn" disabled>开始数据抓取</button>
                </div>
            </div>

            <!-- 步骤3：数据合并汇总 -->
            <div class="step-card" id="step3">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">数据合并</div>
                </div>
                <div class="step-content">
                    <div class="summary-stats" id="extractionStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="connectedCompanies">0</div>
                            <div class="stat-label">已连接公司</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="extractedReports">0</div>
                            <div class="stat-label">抓取报表</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="dataPoints">0</div>
                            <div class="stat-label">数据点</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="consolidationRate">0%</div>
                            <div class="stat-label">合并进度</div>
                        </div>
                    </div>
                    <button class="btn btn-warning" id="consolidateBtn" disabled>执行数据合并</button>
                </div>
            </div>

            <!-- 步骤4：生成合并报表 -->
            <div class="step-card" id="step4">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <div class="step-title">生成报表</div>
                </div>
                <div class="step-content">
                    <div class="summary-stats" id="finalStats" style="display: none;">
                        <div class="stat-card">
                            <div class="stat-number" id="totalAssets">¥0</div>
                            <div class="stat-label">总资产</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalRevenue">¥0</div>
                            <div class="stat-label">总收入</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="netProfit">¥0</div>
                            <div class="stat-label">净利润</div>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="generateReportBtn" disabled>生成合并报表</button>
                    <button class="btn btn-success" id="exportReportBtn" disabled>导出报表</button>
                </div>
            </div>
        </div>

        <!-- 数据展示区域 -->
        <div id="dataDisplayArea" style="display: none;">
            <!-- 抓取的数据表格 -->
            <div class="step-card">
                <h3>抓取的财务数据</h3>
                <div class="loading" id="extractionLoading">
                    <div class="spinner"></div>
                    <p>正在抓取各子公司财务数据...</p>
                </div>
                <table class="data-table" id="extractedDataTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>子公司</th>
                            <th>总资产</th>
                            <th>总负债</th>
                            <th>所有者权益</th>
                            <th>营业收入</th>
                            <th>净利润</th>
                            <th>现金流量</th>
                        </tr>
                    </thead>
                    <tbody id="extractedDataBody">
                    </tbody>
                </table>
            </div>

            <!-- 合并报表 -->
            <div class="consolidated-report" id="consolidatedReport" style="display: none;">
                <div class="report-header">
                    <div class="report-title">集团合并财务报表</div>
                    <div class="report-period" id="reportPeriod">2024年度</div>
                </div>
                
                <div class="report-section">
                    <div class="section-title">资产负债表（合并）</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目</th>
                                <th>期末余额</th>
                                <th>期初余额</th>
                            </tr>
                        </thead>
                        <tbody id="balanceSheetData">
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <div class="section-title">利润表（合并）</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目</th>
                                <th>本期金额</th>
                                <th>上期金额</th>
                            </tr>
                        </thead>
                        <tbody id="incomeStatementData">
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <div class="section-title">现金流量表（合并）</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目</th>
                                <th>本期金额</th>
                                <th>上期金额</th>
                            </tr>
                        </thead>
                        <tbody id="cashFlowData">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 成功提示 -->
        <div class="success-message" id="successMessage">
            <h3>🎉 任务完成！</h3>
            <p>您已成功完成财务报表数据自动汇总操作，系统已记录您的学习进度。</p>
        </div>
    </div>

    <script>
        // 全局变量
        let connectedCompanies = [];
        let extractedData = [];
        let consolidatedData = {};
        let currentStep = 1;
        let completedSteps = [];

        // 模拟子公司财务数据
        const mockCompaniesData = {
            subsidiary1: {
                name: '北京科技子公司',
                assets: 50000000,
                liabilities: 20000000,
                equity: 30000000,
                revenue: 80000000,
                netProfit: 8000000,
                cashFlow: 12000000
            },
            subsidiary2: {
                name: '上海制造子公司',
                assets: 80000000,
                liabilities: 35000000,
                equity: 45000000,
                revenue: 120000000,
                netProfit: 15000000,
                cashFlow: 18000000
            },
            subsidiary3: {
                name: '深圳贸易子公司',
                assets: 60000000,
                liabilities: 25000000,
                equity: 35000000,
                revenue: 95000000,
                netProfit: 12000000,
                cashFlow: 14000000
            },
            subsidiary4: {
                name: '广州投资子公司',
                assets: 100000000,
                liabilities: 40000000,
                equity: 60000000,
                revenue: 70000000,
                netProfit: 10000000,
                cashFlow: 15000000
            }
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateProgress();
        });

        function initializeEventListeners() {
            // 按钮事件
            document.getElementById('connectCompaniesBtn').addEventListener('click', connectCompanies);
            document.getElementById('extractDataBtn').addEventListener('click', extractData);
            document.getElementById('consolidateBtn').addEventListener('click', consolidateData);
            document.getElementById('generateReportBtn').addEventListener('click', generateReport);
            document.getElementById('exportReportBtn').addEventListener('click', exportReport);
        }

        function connectCompanies() {
            showLoading('正在连接各子公司系统...');
            
            const companies = document.querySelectorAll('.company-item');
            let index = 0;
            
            const connectNext = () => {
                if (index >= companies.length) {
                    // 所有公司连接完成
                    completedSteps.push('connect_companies');
                    completeStep(1);
                    activateStep(2);
                    updateProgress();
                    hideLoading();
                    return;
                }
                
                const company = companies[index];
                const companyId = company.getAttribute('data-company');
                const statusEl = company.querySelector('.company-status');
                const indicatorEl = company.querySelector('.status-indicator');
                
                // 模拟连接过程
                statusEl.textContent = '连接中...';
                indicatorEl.className = 'status-indicator status-pending';
                
                setTimeout(() => {
                    // 90%成功率
                    const isSuccess = Math.random() > 0.1;
                    
                    if (isSuccess) {
                        statusEl.textContent = '已连接';
                        indicatorEl.className = 'status-indicator status-connected';
                        company.classList.add('connected');
                        connectedCompanies.push(companyId);
                    } else {
                        statusEl.textContent = '连接失败';
                        indicatorEl.className = 'status-indicator status-failed';
                    }
                    
                    index++;
                    setTimeout(connectNext, 500);
                }, 1000 + Math.random() * 1000);
            };
            
            connectNext();
        }

        function extractData() {
            showLoading('正在抓取财务数据...');
            document.getElementById('extractionLoading').style.display = 'block';
            document.getElementById('dataDisplayArea').style.display = 'block';
            
            let progress = 0;
            const totalSteps = connectedCompanies.length * 3; // 3种报表
            
            const updateExtractionProgress = (type, value) => {
                document.getElementById(`${type}Progress`).style.width = `${value}%`;
                document.getElementById(`${type}Status`).textContent = 
                    value === 100 ? '完成' : `${Math.round(value)}%`;
            };
            
            const extractionInterval = setInterval(() => {
                progress += Math.random() * 15;
                
                const balanceProgress = Math.min(100, progress);
                const incomeProgress = Math.min(100, Math.max(0, progress - 30));
                const cashflowProgress = Math.min(100, Math.max(0, progress - 60));
                
                updateExtractionProgress('balance', balanceProgress);
                updateExtractionProgress('income', incomeProgress);
                updateExtractionProgress('cashflow', cashflowProgress);
                
                if (progress >= 120) {
                    clearInterval(extractionInterval);
                    
                    // 生成抓取的数据
                    generateExtractedData();
                    displayExtractedData();
                    
                    completedSteps.push('extract_data');
                    completeStep(2);
                    activateStep(3);
                    updateProgress();
                    updateExtractionStats();
                    
                    document.getElementById('extractionLoading').style.display = 'none';
                    hideLoading();
                }
            }, 200);
        }

        function generateExtractedData() {
            extractedData = [];
            
            connectedCompanies.forEach(companyId => {
                const companyData = mockCompaniesData[companyId];
                if (companyData) {
                    extractedData.push({
                        company: companyData.name,
                        assets: companyData.assets,
                        liabilities: companyData.liabilities,
                        equity: companyData.equity,
                        revenue: companyData.revenue,
                        netProfit: companyData.netProfit,
                        cashFlow: companyData.cashFlow
                    });
                }
            });
        }

        function displayExtractedData() {
            const tableBody = document.getElementById('extractedDataBody');
            tableBody.innerHTML = '';

            extractedData.forEach(data => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${data.company}</td>
                    <td class="number-cell">¥${(data.assets / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(data.liabilities / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(data.equity / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(data.revenue / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(data.netProfit / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(data.cashFlow / 10000).toFixed(0)}万</td>
                `;
                tableBody.appendChild(row);
            });

            document.getElementById('extractedDataTable').style.display = 'table';
        }

        function updateExtractionStats() {
            document.getElementById('connectedCompanies').textContent = connectedCompanies.length;
            document.getElementById('extractedReports').textContent = extractedData.length * 3;
            document.getElementById('dataPoints').textContent = extractedData.length * 18;
            document.getElementById('extractionStats').style.display = 'grid';
        }

        function consolidateData() {
            showLoading('正在执行数据合并...');
            
            let progress = 0;
            const consolidationInterval = setInterval(() => {
                progress += Math.random() * 10;
                document.getElementById('consolidationRate').textContent = `${Math.min(100, Math.round(progress))}%`;
                
                if (progress >= 100) {
                    clearInterval(consolidationInterval);
                    
                    // 执行合并计算
                    performConsolidation();
                    
                    completedSteps.push('consolidate_data');
                    completeStep(3);
                    activateStep(4);
                    updateProgress();
                    updateFinalStats();
                    hideLoading();
                }
            }, 200);
        }

        function performConsolidation() {
            consolidatedData = {
                totalAssets: extractedData.reduce((sum, data) => sum + data.assets, 0),
                totalLiabilities: extractedData.reduce((sum, data) => sum + data.liabilities, 0),
                totalEquity: extractedData.reduce((sum, data) => sum + data.equity, 0),
                totalRevenue: extractedData.reduce((sum, data) => sum + data.revenue, 0),
                totalNetProfit: extractedData.reduce((sum, data) => sum + data.netProfit, 0),
                totalCashFlow: extractedData.reduce((sum, data) => sum + data.cashFlow, 0)
            };
        }

        function updateFinalStats() {
            document.getElementById('totalAssets').textContent = `¥${(consolidatedData.totalAssets / 100000000).toFixed(1)}亿`;
            document.getElementById('totalRevenue').textContent = `¥${(consolidatedData.totalRevenue / 100000000).toFixed(1)}亿`;
            document.getElementById('netProfit').textContent = `¥${(consolidatedData.totalNetProfit / 10000).toFixed(0)}万`;
            document.getElementById('finalStats').style.display = 'grid';
        }

        function generateReport() {
            showLoading('正在生成合并报表...');
            
            setTimeout(() => {
                generateBalanceSheet();
                generateIncomeStatement();
                generateCashFlowStatement();
                
                document.getElementById('consolidatedReport').style.display = 'block';
                document.getElementById('reportPeriod').textContent = `${new Date().getFullYear()}年度`;
                
                completedSteps.push('generate_report');
                completeStep(4);
                updateProgress();
                hideLoading();
            }, 2000);
        }

        function generateBalanceSheet() {
            const balanceSheetData = [
                { item: '流动资产', current: consolidatedData.totalAssets * 0.6, previous: consolidatedData.totalAssets * 0.55 },
                { item: '非流动资产', current: consolidatedData.totalAssets * 0.4, previous: consolidatedData.totalAssets * 0.45 },
                { item: '资产总计', current: consolidatedData.totalAssets, previous: consolidatedData.totalAssets * 0.9 },
                { item: '流动负债', current: consolidatedData.totalLiabilities * 0.7, previous: consolidatedData.totalLiabilities * 0.75 },
                { item: '非流动负债', current: consolidatedData.totalLiabilities * 0.3, previous: consolidatedData.totalLiabilities * 0.25 },
                { item: '负债合计', current: consolidatedData.totalLiabilities, previous: consolidatedData.totalLiabilities * 0.95 },
                { item: '所有者权益合计', current: consolidatedData.totalEquity, previous: consolidatedData.totalEquity * 0.85 }
            ];

            const tbody = document.getElementById('balanceSheetData');
            tbody.innerHTML = '';

            balanceSheetData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.item}</td>
                    <td class="number-cell">¥${(item.current / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(item.previous / 10000).toFixed(0)}万</td>
                `;
                tbody.appendChild(row);
            });
        }

        function generateIncomeStatement() {
            const incomeStatementData = [
                { item: '营业收入', current: consolidatedData.totalRevenue, previous: consolidatedData.totalRevenue * 0.85 },
                { item: '营业成本', current: consolidatedData.totalRevenue * 0.6, previous: consolidatedData.totalRevenue * 0.65 },
                { item: '营业利润', current: consolidatedData.totalNetProfit * 1.2, previous: consolidatedData.totalNetProfit * 1.1 },
                { item: '利润总额', current: consolidatedData.totalNetProfit * 1.1, previous: consolidatedData.totalNetProfit * 1.05 },
                { item: '净利润', current: consolidatedData.totalNetProfit, previous: consolidatedData.totalNetProfit * 0.9 }
            ];

            const tbody = document.getElementById('incomeStatementData');
            tbody.innerHTML = '';

            incomeStatementData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.item}</td>
                    <td class="number-cell">¥${(item.current / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(item.previous / 10000).toFixed(0)}万</td>
                `;
                tbody.appendChild(row);
            });
        }

        function generateCashFlowStatement() {
            const cashFlowData = [
                { item: '经营活动现金流量净额', current: consolidatedData.totalCashFlow * 0.7, previous: consolidatedData.totalCashFlow * 0.6 },
                { item: '投资活动现金流量净额', current: consolidatedData.totalCashFlow * 0.2, previous: consolidatedData.totalCashFlow * 0.25 },
                { item: '筹资活动现金流量净额', current: consolidatedData.totalCashFlow * 0.1, previous: consolidatedData.totalCashFlow * 0.15 },
                { item: '现金及现金等价物净增加额', current: consolidatedData.totalCashFlow, previous: consolidatedData.totalCashFlow * 0.9 }
            ];

            const tbody = document.getElementById('cashFlowData');
            tbody.innerHTML = '';

            cashFlowData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.item}</td>
                    <td class="number-cell">¥${(item.current / 10000).toFixed(0)}万</td>
                    <td class="number-cell">¥${(item.previous / 10000).toFixed(0)}万</td>
                `;
                tbody.appendChild(row);
            });
        }

        function exportReport() {
            // 模拟导出功能
            console.log('导出合并报表:', consolidatedData);
            alert('合并财务报表已导出到Excel文件');
            
            // 提交验证
            submitValidation();
        }

        function completeStep(stepNumber) {
            const stepCard = document.getElementById(`step${stepNumber}`);
            stepCard.classList.remove('active');
            stepCard.classList.add('completed');
        }

        function activateStep(stepNumber) {
            if (stepNumber <= 4) {
                const stepCard = document.getElementById(`step${stepNumber}`);
                stepCard.classList.add('active');
                
                // 启用对应的按钮
                switch(stepNumber) {
                    case 2:
                        document.getElementById('extractDataBtn').disabled = false;
                        break;
                    case 3:
                        document.getElementById('consolidateBtn').disabled = false;
                        break;
                    case 4:
                        document.getElementById('generateReportBtn').disabled = false;
                        document.getElementById('exportReportBtn').disabled = false;
                        break;
                }
            }
        }

        function updateProgress() {
            const totalSteps = 4;
            const progress = (completedSteps.length / totalSteps) * 100;
            
            document.getElementById('progressBar').querySelector('.progress-fill').style.width = `${progress}%`;
            
            let progressText = '';
            switch(completedSteps.length) {
                case 0:
                    progressText = '准备开始 - 连接子公司系统';
                    break;
                case 1:
                    progressText = '系统连接完成 - 开始数据抓取';
                    break;
                case 2:
                    progressText = '数据抓取完成 - 执行数据合并';
                    break;
                case 3:
                    progressText = '数据合并完成 - 生成合并报表';
                    break;
                case 4:
                    progressText = '任务完成 - 合并报表已生成';
                    break;
            }
            
            document.getElementById('progressText').textContent = progressText;
        }

        function showLoading(message) {
            console.log('Loading:', message);
        }

        function hideLoading() {
            // 隐藏加载提示
        }

        async function submitValidation() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const validationData = {
                    completedSteps: completedSteps,
                    connectedCompanies: connectedCompanies.length,
                    extractedDataCount: extractedData.length,
                    consolidatedData: {
                        totalAssets: consolidatedData.totalAssets,
                        totalRevenue: consolidatedData.totalRevenue,
                        totalNetProfit: consolidatedData.totalNetProfit
                    },
                    reportGenerated: completedSteps.includes('generate_report'),
                    taskCompleted: completedSteps.length === 4
                };

                const response = await fetch('/api/scenarios/8/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(validationData)
                });

                const result = await response.json();
                
                if (result.success && result.valid) {
                    document.getElementById('successMessage').classList.add('show');
                    console.log('场景8验证成功:', result);
                } else {
                    console.error('场景8验证失败:', result.feedback);
                }
            } catch (error) {
                console.error('提交验证失败:', error);
            }
        }
    </script>
</body>
</html>