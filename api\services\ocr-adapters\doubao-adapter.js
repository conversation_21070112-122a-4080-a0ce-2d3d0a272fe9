/**
 * 豆包OCR适配器
 * 基于火山引擎豆包大模型的OCR识别服务
 */

const axios = require('axios');
const BaseOCRAdapter = require('./base-adapter');

class DoubaoOCRAdapter extends BaseOCRAdapter {
    constructor(config = {}) {
        super(config);
        this.name = 'doubao';
        this.priority = 1; // 最高优先级
        this.enabled = true;
        
        this.apiKey = config.apiKey || process.env.ARK_API_KEY;
        this.baseUrl = config.baseUrl || 'https://ark.cn-beijing.volces.com/api/v3';
        this.model = config.model || 'doubao-seed-1-6-thinking-250715';
        this.endpoint = `${this.baseUrl}/chat/completions`;
    }

    /**
     * 检查豆包适配器是否可用
     */
    isAvailable() {
        return !!(this.apiKey && this.apiKey.trim() !== '');
    }

    /**
     * 获取支持的功能
     */
    getFeatures() {
        return [
            '智能发票识别',
            '多模态理解', 
            '结构化输出',
            '上下文理解',
            '通用OCR',
            'AI增强识别'
        ];
    }

    /**
     * 豆包发票OCR识别
     */
    async recognizeInvoice(imageData, filename) {
        try {
            console.log(`🤖 豆包OCR识别发票: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('豆包OCR未配置API密钥');
            }

            // 准备图片数据
            const imageUrl = this.createDataUrl(imageData);

            // 构建豆包请求
            const requestData = {
                model: this.model,
                messages: [{
                    role: "user",
                    content: [{
                        type: "image_url",
                        image_url: { url: imageUrl }
                    }, {
                        type: "text",
                        text: this.buildInvoicePrompt()
                    }]
                }],
                temperature: 0.1, // 低温度确保结果稳定
                max_tokens: 2000
            };

            // 调用豆包API
            const response = await axios.post(this.endpoint, requestData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                timeout: 60000
            });

            if (!response.data.choices || !response.data.choices[0]) {
                throw new Error('豆包API返回格式异常');
            }

            const content = response.data.choices[0].message.content;
            return this.parseInvoiceResult(content, filename);

        } catch (error) {
            console.error(`❌ 豆包OCR识别失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 豆包通用OCR识别
     */
    async recognizeGeneral(imageData, filename) {
        try {
            console.log(`🤖 豆包通用OCR识别: ${filename}`);
            
            if (!this.isAvailable()) {
                throw new Error('豆包OCR未配置API密钥');
            }

            const imageUrl = this.createDataUrl(imageData);

            const requestData = {
                model: this.model,
                messages: [{
                    role: "user",
                    content: [{
                        type: "image_url",
                        image_url: { url: imageUrl }
                    }, {
                        type: "text",
                        text: "请识别图片中的所有文字内容，按行输出，保持原有的格式和排版。"
                    }]
                }],
                temperature: 0.1,
                max_tokens: 1500
            };

            const response = await axios.post(this.endpoint, requestData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                timeout: 45000
            });

            if (!response.data.choices || !response.data.choices[0]) {
                throw new Error('豆包API返回格式异常');
            }

            const content = response.data.choices[0].message.content;
            return this.parseGeneralResult(content, filename);

        } catch (error) {
            console.error(`❌ 豆包通用OCR失败: ${error.message}`);
            return this.standardizeError(error, filename);
        }
    }

    /**
     * 构建发票识别提示词
     */
    buildInvoicePrompt() {
        return `请识别这张增值税发票，并以JSON格式返回以下信息：

{
  "invoiceNumber": "发票号码",
  "invoiceCode": "发票代码", 
  "date": "开票日期(YYYY-MM-DD格式)",
  "sellerName": "销售方名称",
  "sellerTaxId": "销售方纳税人识别号",
  "sellerAddress": "销售方地址",
  "sellerPhone": "销售方电话",
  "buyerName": "购买方名称",
  "buyerTaxId": "购买方纳税人识别号",
  "buyerAddress": "购买方地址",
  "buyerPhone": "购买方电话",
  "totalAmount": 价税合计金额(数字),
  "amountWithoutTax": 不含税金额(数字),
  "taxAmount": 税额(数字),
  "items": [
    {
      "name": "商品名称",
      "quantity": 数量,
      "unitPrice": 单价,
      "amount": 金额
    }
  ],
  "remarks": "备注",
  "checkCode": "校验码"
}

要求：
1. 返回纯JSON格式，不要包含其他文字说明
2. 如果某个字段无法识别，请设为空字符串或0
3. 金额必须是数字类型，不要包含货币符号
4. 日期格式必须是YYYY-MM-DD
5. 确保JSON格式正确，可以被解析`;
    }

    /**
     * 解析发票识别结果
     */
    parseInvoiceResult(content, filename) {
        try {
            // 提取JSON内容
            let jsonStr = content.trim();
            const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                jsonStr = jsonMatch[0];
            }

            // 解析JSON
            const invoiceData = JSON.parse(jsonStr);
            
            // 数据清洗和标准化
            const standardizedData = {
                invoiceNumber: invoiceData.invoiceNumber || '',
                invoiceCode: invoiceData.invoiceCode || '',
                date: invoiceData.date || '',
                sellerName: invoiceData.sellerName || '',
                sellerTaxId: invoiceData.sellerTaxId || '',
                sellerAddress: invoiceData.sellerAddress || '',
                sellerPhone: invoiceData.sellerPhone || '',
                buyerName: invoiceData.buyerName || '',
                buyerTaxId: invoiceData.buyerTaxId || '',
                buyerAddress: invoiceData.buyerAddress || '',
                buyerPhone: invoiceData.buyerPhone || '',
                totalAmount: this.parseAmount(invoiceData.totalAmount),
                amountWithoutTax: this.parseAmount(invoiceData.amountWithoutTax),
                taxAmount: this.parseAmount(invoiceData.taxAmount),
                items: this.parseInvoiceItems(invoiceData.items),
                remarks: invoiceData.remarks || '',
                checkCode: invoiceData.checkCode || ''
            };

            const result = this.standardizeResult({
                data: standardizedData,
                confidence: 0.92
            }, filename, 'doubao');

            console.log(`✅ 豆包发票识别成功: ${standardizedData.invoiceNumber}`);
            return result;

        } catch (error) {
            console.warn(`⚠️ 豆包JSON解析失败，尝试正则提取: ${error.message}`);
            return this.fallbackParseInvoice(content, filename);
        }
    }

    /**
     * 解析通用OCR结果
     */
    parseGeneralResult(content, filename) {
        const lines = content.split('\n').filter(line => line.trim() !== '');
        
        return this.standardizeResult({
            data: {
                text: content,
                lines: lines,
                lineCount: lines.length
            },
            confidence: 0.90
        }, filename, 'doubao');
    }

    /**
     * 解析发票商品条目
     */
    parseInvoiceItems(items) {
        if (!Array.isArray(items)) return [];
        
        return items.map((item, index) => ({
            id: index + 1,
            name: item.name || '',
            quantity: this.parseAmount(item.quantity) || 1,
            unitPrice: this.parseAmount(item.unitPrice),
            amount: this.parseAmount(item.amount)
        }));
    }

    /**
     * 降级解析 - 使用正则表达式提取信息
     */
    fallbackParseInvoice(content, filename) {
        console.log('🔄 豆包正则降级解析模式');
        
        const extractPattern = (pattern, text) => {
            const match = text.match(pattern);
            return match ? match[1].trim() : '';
        };

        const standardizedData = {
            invoiceNumber: extractPattern(/发票号码[：:\s]*([^\n,，\s]+)/, content),
            invoiceCode: extractPattern(/发票代码[：:\s]*([^\n,，\s]+)/, content),
            date: extractPattern(/开票日期[：:\s]*([^\n,，\s]+)/, content),
            sellerName: extractPattern(/销售方[名称]*[：:\s]*([^\n,，]+)/, content),
            buyerName: extractPattern(/购买方[名称]*[：:\s]*([^\n,，]+)/, content),
            totalAmount: this.parseAmount(extractPattern(/[价总]税?合计[：:\s]*([0-9,.]+)/, content)),
            amountWithoutTax: this.parseAmount(extractPattern(/不含税[金额]*[：:\s]*([0-9,.]+)/, content)),
            taxAmount: this.parseAmount(extractPattern(/税额[：:\s]*([0-9,.]+)/, content)),
            sellerTaxId: extractPattern(/销售方.*识别号[：:\s]*([^\n,，\s]+)/, content),
            buyerTaxId: extractPattern(/购买方.*识别号[：:\s]*([^\n,，\s]+)/, content),
            sellerAddress: '',
            sellerPhone: '',
            buyerAddress: '',
            buyerPhone: '',
            items: [],
            remarks: extractPattern(/备注[：:\s]*([^\n]+)/, content),
            checkCode: extractPattern(/校验码[：:\s]*([^\n,，\s]+)/, content)
        };

        return this.standardizeResult({
            data: standardizedData,
            confidence: 0.75 // 降级模式置信度较低
        }, filename, 'doubao-fallback');
    }
}

module.exports = DoubaoOCRAdapter;