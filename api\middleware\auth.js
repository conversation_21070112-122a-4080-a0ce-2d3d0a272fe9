/**
 * 认证中间件
 * 验证JWT token和用户权限
 */

const jwt = require('jsonwebtoken');
// 动态导入数据库配置
let dbConfig;
try {
  if (process.env.TCB_ENV_ID && process.env.TCB_SECRET_ID && process.env.TCB_SECRET_KEY) {
    dbConfig = require('../../cloudbase/database/db');
  } else {
    dbConfig = require('../../cloudbase/database/db-mock');
  }
} catch (error) {
  dbConfig = require('../../cloudbase/database/db-mock');
}
const { db, COLLECTIONS } = dbConfig;

/**
 * JWT认证中间件
 */
const authMiddleware = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证token'
      });
    }

    // 验证JWT
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 从数据库验证用户状态
    const userResult = await db
      .collection(COLLECTIONS.USERS)
      .doc(decoded.userId)
      .get();

    if (!userResult.data.length) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = userResult.data[0];

    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户已被禁用'
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      userId: user._id,
      username: user.username,
      name: user.name,
      role: user.role,
      ...(user.role === 'student' && {
        studentId: user.student_id,
        classId: user.class_id
      }),
      ...(user.role === 'teacher' && {
        department: user.department
      })
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token无效'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token已过期'
      });
    }

    console.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '认证验证失败'
    });
  }
};

/**
 * 学生权限中间件
 */
const studentOnlyMiddleware = (req, res, next) => {
  if (req.user.role !== 'student') {
    return res.status(403).json({
      success: false,
      message: '需要学生权限'
    });
  }
  next();
};

/**
 * 教师权限中间件
 */
const teacherOnlyMiddleware = (req, res, next) => {
  if (req.user.role !== 'teacher') {
    return res.status(403).json({
      success: false,
      message: '需要教师权限'
    });
  }
  next();
};

/**
 * 可选认证中间件（允许未登录用户访问）
 */
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const userResult = await db
      .collection(COLLECTIONS.USERS)
      .doc(decoded.userId)
      .get();

    if (userResult.data.length && userResult.data[0].status === 'active') {
      const user = userResult.data[0];
      req.user = {
        userId: user._id,
        username: user.username,
        name: user.name,
        role: user.role,
        ...(user.role === 'student' && {
          studentId: user.student_id,
          classId: user.class_id
        })
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

module.exports = {
  authMiddleware,
  studentOnlyMiddleware,
  teacherOnlyMiddleware,
  optionalAuthMiddleware
};