@echo off
echo ==========================================
echo      RPA Training Platform - Quick Commit
echo ==========================================
echo.

cd /d "D:\G盘（软件）\cursor开发文件\claude-RPA实训平台3.0"

echo Checking file changes...
git status
echo.

echo Adding all files to staging area...
git add .
echo.

echo Committing changes...
git commit -m "feat: Add third module intelligent financial application scenarios

New Features:
- Contract stamp tax calculation system
- Travel expense audit system
- Invoice OCR verification system
- Bank reconciliation system

Technical Improvements:
- Unified Twilight theme design
- 4-step standardized workflow
- Enhanced backend API support
- Optimized user experience

Progress: Module 3 completed, all 12 scenarios developed

Generated with Claude Code"

echo.
echo Pushing to GitHub...
git push origin main

echo.
if %errorlevel% equ 0 (
    echo SUCCESS: Commit and push completed!
    echo Repository: https://github.com/chanwarmsun/RPA
    echo Congratulations! RPA Training Platform development completed!
) else (
    echo ERROR: Push failed, please check network or authentication
)

echo.
echo Press any key to exit...
pause > nul
