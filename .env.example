# RPA实训平台环境配置示例

# 应用配置
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:8080

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_EXPIRES_IN=7d

# 腾讯云云开发配置（可选）
TCB_ENV_ID=your-tcb-env-id
TCB_SECRET_ID=your-tcb-secret-id
TCB_SECRET_KEY=your-tcb-secret-key

# OCR服务配置 - 多厂商适配器架构
# 优先级：豆包 > 百度 > 腾讯云 > 阿里云 > Mock

# 豆包OCR配置（推荐 - 优先使用）
ARK_API_KEY=your-ark-api-key

# 百度智能云OCR配置
BAIDU_OCR_API_KEY=your-baidu-api-key
BAIDU_OCR_SECRET_KEY=your-baidu-secret-key

# 腾讯云OCR配置
TENCENT_SECRET_ID=your-tencent-secret-id
TENCENT_SECRET_KEY=your-tencent-secret-key

# 阿里云OCR配置
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret

# 数据库配置（如使用本地数据库）
DB_HOST=localhost
DB_PORT=3306
DB_NAME=rpa_training
DB_USER=root
DB_PASSWORD=password

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
CORS_ORIGIN=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 文件上传配置
MAX_FILE_SIZE=10485760
MAX_FILES=10
UPLOAD_PATH=uploads/

# 其他配置
ENABLE_MOCK_DATA=true
ENABLE_DEBUG=true