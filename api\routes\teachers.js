/**
 * 教师相关路由
 */

const express = require('express');
const { db, COLLECTIONS } = require('../../cloudbase/database/db');
const { authMiddleware, teacherOnlyMiddleware } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取教师个人信息
 * GET /api/teachers/profile
 */
router.get('/profile', authMiddleware, teacherOnlyMiddleware, async (req, res) => {
  try {
    res.json({
      success: true,
      data: req.user
    });
  } catch (error) {
    console.error('获取教师信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取教师信息失败'
    });
  }
});

module.exports = router;