<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>税务申报期提醒与状态核查 - RPA实训场景4</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .header .scenario-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #8D99AE;
            font-size: 14px;
        }
        
        .simulation-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .system-header {
            background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .system-logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .system-subtitle {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .nav-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .nav-item {
            padding: 8px 16px;
            background: #7673FF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .nav-item:hover {
            background: #6366F1;
            transform: translateY(-1px);
        }
        
        .nav-item.active {
            background: #4338CA;
        }
        
        .content-area {
            padding: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            background: #f8f9fa;
            border-radius: 25px;
            font-size: 14px;
            color: #8D99AE;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: #7673FF;
            color: white;
        }
        
        .step.completed {
            background: #10B981;
            color: white;
        }
        
        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: rgba(255,255,255,0.3);
        }
        
        .step.completed .step-number {
            background: rgba(255,255,255,0.3);
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .form-input {
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118,115,255,0.1);
        }
        
        .form-select {
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #7673FF;
            box-shadow: 0 0 0 3px rgba(118,115,255,0.1);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }
        
        .btn-primary {
            background: #7673FF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #6366F1;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(118,115,255,0.3);
        }
        
        .btn-secondary {
            background: #8D99AE;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #6c757d;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #10B981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            margin: 0 auto;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .calendar-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .calendar-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .calendar-nav {
            display: flex;
            gap: 10px;
        }
        
        .calendar-nav button {
            background: #f3f4f6;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .calendar-nav button:hover {
            background: #e5e7eb;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .calendar-day {
            background: white;
            padding: 15px 10px;
            text-align: center;
            font-size: 14px;
            position: relative;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        
        .calendar-day-header {
            background: #f9fafb;
            font-weight: bold;
            color: #6b7280;
        }
        
        .calendar-day-number {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .calendar-event {
            background: #FEF3C7;
            color: #92400E;
            font-size: 11px;
            padding: 2px 4px;
            border-radius: 3px;
            margin: 1px 0;
            text-align: center;
        }
        
        .calendar-event.deadline {
            background: #FEE2E2;
            color: #991B1B;
        }
        
        .calendar-event.completed {
            background: #D1FAE5;
            color: #065F46;
        }
        
        .status-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .status-table th {
            background: #2E8B57;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
        }
        
        .status-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            color: #374151;
        }
        
        .status-table tr:hover {
            background: #f9fafb;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-submitted {
            background: #D1FAE5;
            color: #065F46;
        }
        
        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .status-overdue {
            background: #FEE2E2;
            color: #991B1B;
        }
        
        .task-completed {
            background: #D1FAE5;
            border: 2px solid #10B981;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
        }
        
        .completion-icon {
            font-size: 48px;
            color: #10B981;
            margin-bottom: 15px;
        }
        
        .completion-text {
            font-size: 18px;
            font-weight: bold;
            color: #065F46;
            margin-bottom: 10px;
        }
        
        .completion-details {
            color: #047857;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2E8B57, #228B22);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 场景信息头部 -->
        <div class="header">
            <h1>🏛️ 税务申报期提醒与状态核查</h1>
            <p>登录电子税务局，获取申报日历信息，并核查各税种申报完成状态</p>
            <div class="scenario-info">
                <div class="info-item">
                    <span>📊</span>
                    <span>模块：基础操作</span>
                </div>
                <div class="info-item">
                    <span>⏱️</span>
                    <span>预计时间：35分钟</span>
                </div>
                <div class="info-item">
                    <span>🎯</span>
                    <span>难度：初级</span>
                </div>
            </div>
        </div>

        <!-- 模拟的电子税务局界面 -->
        <div class="simulation-container">
            <div class="system-header">
                <div class="system-logo">🏛️ 国家税务总局电子税务局</div>
                <div class="system-subtitle">Electronic Tax Bureau - 企业版</div>
            </div>

            <!-- 系统导航栏 -->
            <div class="nav-bar">
                <div class="nav-content">
                    <a href="#" class="nav-item active" data-tab="login">🔐 系统登录</a>
                    <a href="#" class="nav-item" data-tab="calendar">📅 申报日历</a>
                    <a href="#" class="nav-item" data-tab="status">📊 申报状态</a>
                    <a href="#" class="nav-item" data-tab="report">📋 状态报告</a>
                </div>
            </div>

            <div class="content-area">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">
                        <div class="step-number">1</div>
                        <span>登录税务局</span>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-number">2</div>
                        <span>获取申报日历</span>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-number">3</div>
                        <span>核查申报状态</span>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-number">4</div>
                        <span>生成状态报告</span>
                    </div>
                </div>

                <!-- 步骤1：登录界面 -->
                <div id="step1" class="step-content">
                    <div class="login-form">
                        <div class="section-title">
                            <span>🔐</span>
                            <span>企业用户登录</span>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">统一社会信用代码</label>
                            <input type="text" class="form-input" id="creditCode" value="91110000000000001X" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">企业名称</label>
                            <input type="text" class="form-input" id="companyName" value="北京示例科技有限公司" readonly>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">管理员账号</label>
                            <input type="text" class="form-input" id="adminAccount" placeholder="请输入管理员账号">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">登录密码</label>
                            <input type="password" class="form-input" id="loginPassword" placeholder="请输入登录密码">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">验证码</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" class="form-input" id="captcha" placeholder="请输入验证码" style="flex: 1;">
                                <div style="background: #f0f0f0; padding: 8px 16px; border-radius: 4px; font-family: monospace; letter-spacing: 2px; cursor: pointer;" onclick="refreshCaptcha()">
                                    <span id="captchaDisplay">AB3D</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-primary" id="loginBtn">
                                <span>🚀</span>
                                <span>登录系统</span>
                            </button>
                        </div>
                        
                        <div class="alert alert-info">
                            <span>💡</span>
                            <span>提示：请使用企业CA证书或密码方式登录系统</span>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：申报日历 -->
                <div id="step2" class="step-content hidden">
                    <div class="form-section">
                        <div class="section-title">
                            <span>📅</span>
                            <span>2024年11月申报日历</span>
                        </div>
                        
                        <div class="alert alert-info">
                            <span>📢</span>
                            <span>当前查看：2024年11月份申报截止日期及相关税种信息</span>
                        </div>
                        
                        <div class="calendar-container">
                            <div class="calendar-header">
                                <div class="calendar-title">2024年11月</div>
                                <div class="calendar-nav">
                                    <button onclick="previousMonth()">« 上月</button>
                                    <button onclick="currentMonth()">本月</button>
                                    <button onclick="nextMonth()">下月 »</button>
                                </div>
                            </div>
                            
                            <div class="calendar-grid" id="calendarGrid">
                                <!-- 动态生成日历 -->
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <span>⚠️</span>
                            <span>重要提醒：增值税申报截止日期为11月15日，请及时完成申报</span>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-success" id="calendarBtn">
                                <span>📊</span>
                                <span>查看申报状态</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：申报状态核查 -->
                <div id="step3" class="step-content hidden">
                    <div class="form-section">
                        <div class="section-title">
                            <span>📊</span>
                            <span>10月份各税种申报状态核查</span>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" id="statusProgress"></div>
                        </div>
                        
                        <div style="text-align: center; color: #8D99AE; margin: 20px 0;">
                            <div id="statusProgressText">正在查询各税种申报状态...</div>
                        </div>

                        <div id="statusResults" class="hidden">
                            <table class="status-table">
                                <thead>
                                    <tr>
                                        <th>税种名称</th>
                                        <th>所属期间</th>
                                        <th>申报截止日</th>
                                        <th>申报状态</th>
                                        <th>申报日期</th>
                                        <th>应纳税额</th>
                                    </tr>
                                </thead>
                                <tbody id="statusTableBody">
                                    <!-- 动态生成 -->
                                </tbody>
                            </table>
                            
                            <div style="margin-top: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                                <div style="background: #D1FAE5; padding: 20px; border-radius: 10px; text-align: center;">
                                    <div style="font-size: 32px; font-weight: bold; color: #065F46;" id="submittedCount">4</div>
                                    <div style="color: #047857; font-size: 14px;">已申报</div>
                                </div>
                                <div style="background: #FEF3C7; padding: 20px; border-radius: 10px; text-align: center;">
                                    <div style="font-size: 32px; font-weight: bold; color: #92400E;" id="pendingCount">1</div>
                                    <div style="color: #B45309; font-size: 14px;">待申报</div>
                                </div>
                                <div style="background: #FEE2E2; padding: 20px; border-radius: 10px; text-align: center;">
                                    <div style="font-size: 32px; font-weight: bold; color: #991B1B;" id="overdueCount">0</div>
                                    <div style="color: #B91C1C; font-size: 14px;">逾期申报</div>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons" id="statusActions" style="display: none;">
                            <button class="btn btn-primary" id="generateReportBtn">
                                <span>📋</span>
                                <span>生成状态报告</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤4：任务完成 -->
                <div id="step4" class="step-content hidden">
                    <div class="task-completed">
                        <div class="completion-icon">🎉</div>
                        <div class="completion-text">税务申报状态核查完成！</div>
                        <div class="completion-details">
                            已成功获取申报日历信息，核查了 <span id="finalTaxCount">5</span> 个税种的申报状态，
                            生成详细的状态分析报告
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-secondary" onclick="location.href='student-dashboard.html'">
                            <span>🏠</span>
                            <span>返回主页</span>
                        </button>
                        <button class="btn btn-primary" onclick="window.location.reload()">
                            <span>🔄</span>
                            <span>重新练习</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // 模拟的申报状态数据
        const mockTaxStatus = [
            { name: '增值税', period: '2024年10月', deadline: '2024-11-15', status: 'submitted', date: '2024-11-10', amount: 12800.50 },
            { name: '企业所得税', period: '2024年第三季度', deadline: '2024-10-31', status: 'submitted', date: '2024-10-25', amount: 45600.00 },
            { name: '印花税', period: '2024年10月', deadline: '2024-11-15', status: 'submitted', date: '2024-11-08', amount: 320.00 },
            { name: '城市维护建设税', period: '2024年10月', deadline: '2024-11-15', status: 'submitted', date: '2024-11-10', amount: 896.00 },
            { name: '教育费附加', period: '2024年10月', deadline: '2024-11-15', status: 'pending', date: null, amount: 0 }
        ];

        let currentStep = 1;
        let calendarData = [];
        let statusData = [];

        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            generateCalendar();
        });

        function initializeEventListeners() {
            // 按钮事件
            document.getElementById('loginBtn').addEventListener('click', handleLogin);
            document.getElementById('calendarBtn').addEventListener('click', () => goToStep(3));
            document.getElementById('generateReportBtn').addEventListener('click', generateReport);
            
            // 导航栏事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', handleNavClick);
            });
        }

        function handleLogin() {
            const adminAccount = document.getElementById('adminAccount').value;
            const loginPassword = document.getElementById('loginPassword').value;
            const captcha = document.getElementById('captcha').value;
            
            // 基础验证
            if (!adminAccount || !loginPassword || !captcha) {
                alert('请填写完整的登录信息');
                return;
            }
            
            if (captcha.toUpperCase() !== 'AB3D') {
                alert('验证码错误，请重新输入');
                document.getElementById('captcha').value = '';
                refreshCaptcha();
                return;
            }
            
            const btn = document.getElementById('loginBtn');
            btn.innerHTML = '<div class="loading"></div><span>登录中...</span>';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '<span>✅</span><span>登录成功</span>';
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    goToStep(2);
                }, 1000);
            }, 2000);
        }

        function refreshCaptcha() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let captcha = '';
            for (let i = 0; i < 4; i++) {
                captcha += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            document.getElementById('captchaDisplay').textContent = captcha;
        }

        function generateCalendar() {
            const grid = document.getElementById('calendarGrid');
            const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六'];
            
            // 添加星期头部
            daysOfWeek.forEach(day => {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day calendar-day-header';
                dayElement.textContent = day;
                grid.appendChild(dayElement);
            });
            
            // 生成11月份的日历（2024年11月1日是星期五）
            const startDay = 5; // 星期五
            const daysInMonth = 30;
            
            // 添加前面的空格
            for (let i = 0; i < startDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day';
                grid.appendChild(emptyDay);
            }
            
            // 添加实际日期
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.innerHTML = `<div class="calendar-day-number">${day}</div>`;
                
                // 添加重要申报日期
                if (day === 15) {
                    const event = document.createElement('div');
                    event.className = 'calendar-event deadline';
                    event.textContent = '增值税申报截止';
                    dayElement.appendChild(event);
                }
                
                if (day === 31) {
                    const event = document.createElement('div');
                    event.className = 'calendar-event deadline';
                    event.textContent = '所得税申报截止';
                    dayElement.appendChild(event);
                }
                
                if (day === 10) {
                    const event = document.createElement('div');
                    event.className = 'calendar-event completed';
                    event.textContent = '已申报增值税';
                    dayElement.appendChild(event);
                }
                
                grid.appendChild(dayElement);
            }
            
            // 存储日历数据
            calendarData = [
                { date: '2024-11-15', event: '增值税申报截止', type: 'deadline' },
                { date: '2024-11-31', event: '企业所得税申报截止', type: 'deadline' },
                { date: '2024-11-10', event: '已申报增值税', type: 'completed' }
            ];
        }

        function goToStep(step) {
            // 隐藏当前步骤
            document.getElementById(`step${currentStep}`).classList.add('hidden');
            
            // 更新步骤指示器
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('completed');
            
            // 显示新步骤
            currentStep = step;
            document.getElementById(`step${step}`).classList.remove('hidden');
            document.getElementById(`step${step}`).classList.add('fade-in');
            document.querySelector(`.step[data-step="${step}"]`).classList.add('active');
            
            // 根据步骤执行相应操作
            if (step === 3) {
                startStatusCheck();
            }
        }

        function startStatusCheck() {
            const progressFill = document.getElementById('statusProgress');
            const progressText = document.getElementById('statusProgressText');
            let progress = 0;
            
            const steps = [
                '正在连接税务系统数据库...',
                '正在查询增值税申报状态...',
                '正在查询企业所得税申报状态...',
                '正在查询印花税申报状态...',
                '正在查询城建税申报状态...',
                '正在查询教育费附加申报状态...',
                '正在汇总申报状态信息...',
                '状态核查完成'
            ];
            
            const interval = setInterval(() => {
                progress += 12.5;
                progressFill.style.width = progress + '%';
                progressText.textContent = steps[Math.floor(progress / 12.5)] || '核查完成';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        showStatusResults();
                    }, 1000);
                }
            }, 800);
        }

        function showStatusResults() {
            const tbody = document.getElementById('statusTableBody');
            tbody.innerHTML = '';
            
            statusData = mockTaxStatus;
            let submittedCount = 0;
            let pendingCount = 0;
            let overdueCount = 0;
            
            statusData.forEach(tax => {
                const row = tbody.insertRow();
                let statusBadge = '';
                
                switch (tax.status) {
                    case 'submitted':
                        statusBadge = '<span class="status-badge status-submitted">✓ 已申报</span>';
                        submittedCount++;
                        break;
                    case 'pending':
                        statusBadge = '<span class="status-badge status-pending">⏳ 待申报</span>';
                        pendingCount++;
                        break;
                    case 'overdue':
                        statusBadge = '<span class="status-badge status-overdue">⚠ 逾期</span>';
                        overdueCount++;
                        break;
                }
                
                row.innerHTML = `
                    <td id="tax-name-${tax.name}">${tax.name}</td>
                    <td>${tax.period}</td>
                    <td>${tax.deadline}</td>
                    <td>${statusBadge}</td>
                    <td>${tax.date || '未申报'}</td>
                    <td>${tax.amount ? '¥' + tax.amount.toLocaleString() : '-'}</td>
                `;
            });
            
            // 更新统计数据
            document.getElementById('submittedCount').textContent = submittedCount;
            document.getElementById('pendingCount').textContent = pendingCount;
            document.getElementById('overdueCount').textContent = overdueCount;
            
            document.getElementById('statusResults').classList.remove('hidden');
            document.getElementById('statusResults').classList.add('fade-in');
            document.getElementById('statusActions').style.display = 'flex';
        }

        async function generateReport() {
            const btn = document.getElementById('generateReportBtn');
            btn.innerHTML = '<div class="loading"></div><span>生成中...</span>';
            btn.disabled = true;
            
            setTimeout(() => {
                // 调用验证API
                validateTask();
            }, 2000);
        }

        async function validateTask() {
            try {
                const response = await fetch('/api/scenarios/4/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        completedSteps: ['login', 'calendar', 'status', 'report'],
                        calendarData: calendarData,
                        statusData: statusData,
                        taxCount: statusData.length
                    })
                });
                
                const result = await response.json();
                
                if (result.valid) {
                    document.getElementById('finalTaxCount').textContent = statusData.length;
                    goToStep(4);
                } else {
                    throw new Error(result.message || '验证失败');
                }
            } catch (error) {
                console.error('验证失败:', error);
                alert('任务验证失败，请检查操作步骤');
                
                const btn = document.getElementById('generateReportBtn');
                btn.innerHTML = '<span>📋</span><span>重新生成报告</span>';
                btn.disabled = false;
            }
        }

        function handleNavClick(e) {
            e.preventDefault();
            const tabName = e.target.dataset.tab;
            
            // 更新导航栏状态
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            e.target.classList.add('active');
            
            // 这里可以根据需要添加标签页切换逻辑
        }

        function previousMonth() {
            alert('这是演示版本，暂不支持月份切换功能');
        }

        function currentMonth() {
            alert('当前显示的就是本月（2024年11月）');
        }

        function nextMonth() {
            alert('这是演示版本，暂不支持月份切换功能');
        }
    </script>
</body>
</html>