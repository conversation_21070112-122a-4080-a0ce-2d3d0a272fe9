const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');

// MIME 类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

// Mock用户数据
const mockUsers = {
  students: [
    { id: 1, username: 'student1', password: '123456', name: '张三', studentId: '2024001', class: '财会1班' },
    { id: 2, username: 'student2', password: '123456', name: '李四', studentId: '2024002', class: '财会1班' },
    { id: 3, username: 'test', password: '123456', name: '测试学生', studentId: '2024999', class: '测试班' }
  ],
  teachers: [
    { id: 1, username: 'teacher1', password: '123456', name: '王老师', role: 'teacher' },
    { id: 2, username: 'admin', password: '123456', name: '管理员', role: 'admin' },
    { id: 3, username: 'test', password: '123456', name: '测试教师', role: 'teacher' }
  ]
};

// Mock学生进度数据
const studentProgress = {
  1: {
    completedScenarios: [1, 2, 3],
    scores: { 1: 95, 2: 88, 3: 92 },
    totalScore: 275,
    completionRate: 0.25
  },
  2: {
    completedScenarios: [1, 2, 3, 4, 5],
    scores: { 1: 90, 2: 85, 3: 94, 4: 89, 5: 91 },
    totalScore: 449,
    completionRate: 0.42
  },
  3: {
    completedScenarios: [],
    scores: {},
    totalScore: 0,
    completionRate: 0
  }
};

// 生成JWT Token (简化版)
function generateToken(user) {
  return Buffer.from(JSON.stringify({
    id: user.id,
    username: user.username,
    name: user.name,
    role: user.role || 'student',
    exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时过期
  })).toString('base64');
}

// 验证Token
function verifyToken(token) {
  try {
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString('utf8'));
    if (decoded.exp > Date.now()) {
      return decoded;
    }
  } catch (e) {
    return null;
  }
  return null;
}

// API路由处理
function handleAPI(req, res, pathname, parsedUrl) {
  const method = req.method;
  
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 读取请求体
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', () => {
    try {
      const data = body ? JSON.parse(body) : {};
      
      // 学生登录
      if (pathname === '/api/auth/student/login' && method === 'POST') {
        const { username, password } = data;
        console.log(`学生登录尝试: ${username} / ${password}`);
        const student = mockUsers.students.find(s => s.username === username && s.password === password);
        
        if (student) {
          const token = generateToken(student);
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: true,
            message: '登录成功',
            token,
            user: {
              id: student.id,
              username: student.username,
              name: student.name,
              studentId: student.studentId,
              class: student.class,
              role: 'student'
            }
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          }));
        }
        return;
      }

      // 教师登录
      if (pathname === '/api/auth/teacher/login' && method === 'POST') {
        const { username, password } = data;
        console.log(`教师登录尝试: ${username} / ${password}`);
        const teacher = mockUsers.teachers.find(t => t.username === username && t.password === password);
        
        if (teacher) {
          const token = generateToken(teacher);
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: true,
            message: '登录成功',
            token,
            user: {
              id: teacher.id,
              username: teacher.username,
              name: teacher.name,
              role: teacher.role
            }
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          }));
        }
        return;
      }

      // 获取学生进度
      if (pathname === '/api/students/progress' && method === 'GET') {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        const user = verifyToken(token);
        
        if (!user) {
          res.writeHead(401, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({ success: false, message: '未授权' }));
          return;
        }

        const progress = studentProgress[user.id] || {
          completedScenarios: [],
          scores: {},
          totalScore: 0,
          completionRate: 0
        };

        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
          success: true,
          data: progress
        }));
        return;
      }

      // 获取场景列表
      if (pathname === '/api/scenarios' && method === 'GET') {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        const user = verifyToken(token);
        
        if (!user) {
          res.writeHead(401, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({ success: false, message: '未授权' }));
          return;
        }

        // 模拟场景数据
        const scenarios = {
          module1: [
            { id: 1, title: '银行流水查询下载', description: '学习如何自动登录银行系统，查询并下载流水记录', difficulty: '基础', estimated_time: 20, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 2, title: '批量开具电子发票', description: '掌握发票开具系统的自动化操作，批量生成电子发票', difficulty: '基础', estimated_time: 25, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 3, title: '固定资产信息核对', description: '自动化核对资产管理系统中的固定资产信息', difficulty: '基础', estimated_time: 30, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 4, title: '税务申报期查询', description: '定期查询税务系统，获取申报期限提醒', difficulty: '基础', estimated_time: 15, progress: { status: 'not_started', progress: 0, score: null } }
          ],
          module2: [
            { id: 5, title: '应收账款对账核销', description: '复杂的应收账款自动对账和核销流程', difficulty: '中级', estimated_time: 35, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 6, title: '增值税发票认证', description: '发票认证系统的批量处理和异常处理', difficulty: '中级', estimated_time: 30, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 7, title: '工资条批量生成', description: '从HR系统获取数据，自动生成并发送工资条', difficulty: '中级', estimated_time: 40, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 8, title: '财务报表汇总', description: '多系统数据整合，自动生成财务报表', difficulty: '中级', estimated_time: 45, progress: { status: 'not_started', progress: 0, score: null } }
          ],
          module3: [
            { id: 9, title: '发票OCR识别验真', description: '使用AI OCR技术识别发票信息并验证真伪', difficulty: '高级', estimated_time: 50, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 10, title: '差旅费报销审核', description: '智能审核差旅费报销单据，人机协作处理', difficulty: '高级', estimated_time: 45, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 11, title: '合同印花税计算', description: '自动识别合同类型，计算印花税并生成申报', difficulty: '高级', estimated_time: 40, progress: { status: 'not_started', progress: 0, score: null } },
            { id: 12, title: '银行余额调节表', description: '智能化银行余额调节，自动发现并处理差异', difficulty: '高级', estimated_time: 55, progress: { status: 'not_started', progress: 0, score: null } }
          ]
        };

        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
          success: true,
          data: { scenarios }
        }));
        return;
      }

      // 场景验证 API
      const scenarioMatch = pathname.match(/^\/api\/scenarios\/(\d+)\/validate$/);
      if (scenarioMatch && method === 'POST') {
        const scenarioId = parseInt(scenarioMatch[1]);
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        const user = verifyToken(token);
        
        if (!user) {
          res.writeHead(401, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({ success: false, message: '未授权' }));
          return;
        }

        // 模拟场景验证逻辑
        const isValid = validateScenario(scenarioId, data);
        const score = calculateScore(scenarioId, data);
        
        // 更新学生进度
        if (!studentProgress[user.id]) {
          studentProgress[user.id] = {
            completedScenarios: [],
            scores: {},
            totalScore: 0,
            completionRate: 0
          };
        }

        if (isValid && !studentProgress[user.id].completedScenarios.includes(scenarioId)) {
          studentProgress[user.id].completedScenarios.push(scenarioId);
          studentProgress[user.id].scores[scenarioId] = score;
          studentProgress[user.id].totalScore += score;
          studentProgress[user.id].completionRate = studentProgress[user.id].completedScenarios.length / 12;
        }

        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({
          success: true,
          valid: isValid,
          score: score,
          feedback: generateFeedback(scenarioId, isValid, score)
        }));
        return;
      }

      // OCR API模拟
      if (pathname === '/api/ocr/invoice' && method === 'POST') {
        // 模拟OCR处理延迟
        setTimeout(() => {
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: true,
            data: {
              results: [
                {
                  filename: "invoice_001.jpg",
                  confidence: 0.95,
                  data: {
                    invoiceNumber: "12345678",
                    date: "2024-08-04",
                    sellerName: "北京科技有限公司",
                    sellerTaxId: "91110108MA01234567",
                    buyerName: "上海商贸有限公司",
                    buyerTaxId: "91310115MA0987654321",
                    totalAmount: 11600.00,
                    taxAmount: 1600.00,
                    amountWithoutTax: 10000.00,
                    taxRate: 0.16
                  }
                }
              ],
              statistics: {
                successCount: 1,
                totalFiles: 1,
                totalAmount: 11600.00,
                averageConfidence: 0.95
              }
            }
          }));
        }, 2000);
        return;
      }

      // 发票验证API
      if (pathname === '/api/ocr/verify-invoice' && method === 'POST') {
        setTimeout(() => {
          res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
          res.end(JSON.stringify({
            success: true,
            data: {
              results: data.ocrResults.map(result => ({
                ...result,
                isValid: Math.random() > 0.2,
                verificationCode: `V${Date.now()}`,
                verificationTime: new Date().toISOString()
              })),
              summary: {
                valid: Math.floor(data.ocrResults.length * 0.8),
                total: data.ocrResults.length,
                validRate: "80%"
              }
            }
          }));
        }, 1500);
        return;
      }

      // 默认404响应
      res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({
        success: false,
        message: 'API接口未找到',
        path: pathname
      }));

    } catch (error) {
      console.error('API处理错误:', error);
      res.writeHead(500, { 'Content-Type': 'application/json; charset=utf-8' });
      res.end(JSON.stringify({
        success: false,
        message: '服务器内部错误'
      }));
    }
  });
}

// 验证场景完成情况
function validateScenario(scenarioId, data) {
  // 简化的验证逻辑，根据场景ID和提交的数据判断是否完成
  if (!data.taskCompleted) return false;
  
  switch (scenarioId) {
    case 1: // 银行流水查询
      return data.downloadedFiles >= 1 && data.completedSteps?.includes('download');
    case 2: // 发票开具
      return data.generatedInvoices >= 1 && data.completedSteps?.includes('generate');
    case 3: // 资产核对
      return data.verifiedAssets >= 1 && data.completedSteps?.includes('verify');
    case 4: // 税务申报
      return data.checkedDeclarations >= 1 && data.completedSteps?.includes('check');
    case 5: // 应收账款对账
      return data.reconciledAccounts >= 1 && data.completedSteps?.includes('reconcile');
    case 6: // 发票认证
      return data.authenticatedInvoices >= 1 && data.completedSteps?.includes('authenticate');
    case 7: // 工资条生成
      return data.generatedPayslips >= 1 && data.completedSteps?.includes('generate');
    case 8: // 财务报表汇总
      return data.consolidatedReports >= 1 && data.completedSteps?.includes('consolidate');
    case 9: // 发票OCR验真
      return data.verifiedInvoiceCount >= 1 && data.generatedEntriesCount >= 1;
    case 10: // 差旅费审核
      return data.totalExpenses >= 3 && data.processingAccuracy >= 0.8;
    case 11: // 合同印花税
      return data.extractedContractCount >= 1 && data.totalTaxAmount > 0;
    case 12: // 余额调节表
      return data.dataSourcesUsed >= 2 && data.isReconciled;
    default:
      return data.completedSteps?.length >= 3;
  }
}

// 计算得分
function calculateScore(scenarioId, data) {
  let baseScore = 70;
  let bonusScore = 0;
  
  // 根据完成质量添加奖励分
  if (data.timeSpent && data.timeSpent < 300) bonusScore += 10; // 5分钟内完成
  if (data.completedSteps?.length >= 4) bonusScore += 10; // 完成所有步骤
  
  // 场景特定加分
  switch (scenarioId) {
    case 9:
      if (data.averageConfidence >= 0.9) bonusScore += 10;
      break;
    case 10:
      if (data.processingAccuracy >= 0.95) bonusScore += 10;
      break;
    case 11:
      if (data.averageConfidence >= 0.9 && data.declarationGenerated) bonusScore += 10;
      break;
    case 12:
      if (data.reconciliationAccuracy >= 1.0) bonusScore += 10;
      break;
  }
  
  return Math.min(baseScore + bonusScore, 100);
}

// 生成反馈信息
function generateFeedback(scenarioId, isValid, score) {
  if (!isValid) {
    return '任务未完成，请检查是否按要求完成了所有步骤。';
  }
  
  const scenarioNames = {
    1: '银行流水查询下载',
    2: '批量开具电子发票',
    3: '固定资产信息核对',
    4: '税务申报期查询',
    5: '应收账款对账核销',
    6: '增值税发票认证',
    7: '工资条批量生成',
    8: '财务报表汇总',
    9: '发票OCR识别验真',
    10: '差旅费报销审核',
    11: '合同印花税计算',
    12: '银行余额调节表'
  };
  
  const scenarioName = scenarioNames[scenarioId] || `场景${scenarioId}`;
  
  if (score >= 90) {
    return `🎉 优秀！您成功完成了${scenarioName}任务，操作规范，效率很高！`;
  } else if (score >= 80) {
    return `👍 良好！您完成了${scenarioName}任务，还有提升空间。`;
  } else {
    return `✅ 合格！您完成了${scenarioName}任务，建议多练习提高熟练度。`;
  }
}

// 处理静态文件
function handleStaticFile(req, res, pathname) {
  // 处理根路径，重定向到学生登录页面
  if (pathname === '/') {
    pathname = '/frontend/scenarios/login.html';
  }
  
  // 构建文件路径
  let filePath = path.join(__dirname, pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回404
      res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html lang="zh">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>页面未找到 - RPA实训平台</title>
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    display: flex; 
                    justify-content: center; 
                    align-items: center; 
                    height: 100vh; 
                    margin: 0;
                    background: linear-gradient(135deg, #5C7BFF 0%, #A069FF 100%);
                    color: white;
                }
                .error-container {
                    text-align: center;
                    background: rgba(255,255,255,0.1);
                    padding: 2rem;
                    border-radius: 1rem;
                    backdrop-filter: blur(10px);
                }
                h1 { font-size: 3rem; margin-bottom: 1rem; }
                p { font-size: 1.2rem; margin-bottom: 2rem; }
                .nav-links a {
                    color: white;
                    text-decoration: none;
                    margin: 0 1rem;
                    padding: 0.5rem 1rem;
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 0.5rem;
                    transition: all 0.3s ease;
                    display: inline-block;
                }
                .nav-links a:hover {
                    background: rgba(255,255,255,0.2);
                    transform: translateY(-2px);
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>404</h1>
                <p>页面未找到</p>
                <div class="nav-links">
                    <a href="/frontend/scenarios/login.html">🎓 学生登录</a>
                    <a href="/frontend/scenarios/teacher-login.html">👨‍🏫 教师登录</a>
                    <a href="/frontend/scenarios/student-dashboard.html">📊 学生Dashboard</a>
                </div>
            </div>
        </body>
        </html>
      `);
      return;
    }
    
    // 检查是否是目录
    fs.stat(filePath, (err, stats) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
        res.end('服务器错误');
        return;
      }
      
      if (stats.isDirectory()) {
        // 如果是目录，尝试找index.html
        filePath = path.join(filePath, 'index.html');
      }
      
      // 读取并返回文件
      fs.readFile(filePath, (err, data) => {
        if (err) {
          res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
          res.end('服务器错误');
          return;
        }
        
        // 设置正确的MIME类型
        const ext = path.extname(filePath).toLowerCase();
        const mimeType = mimeTypes[ext] || 'application/octet-stream';
        
        res.writeHead(200, { 
          'Content-Type': mimeType + (mimeType.startsWith('text/') ? '; charset=utf-8' : ''),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end(data);
      });
    });
  });
}

// 创建服务器
const server = http.createServer((req, res) => {
  console.log(`${new Date().toLocaleTimeString()} - ${req.method} ${req.url}`);
  
  const parsedUrl = url.parse(req.url);
  const pathname = parsedUrl.pathname;
  
  // API路由
  if (pathname.startsWith('/api/')) {
    handleAPI(req, res, pathname, parsedUrl);
  } else {
    // 静态文件
    handleStaticFile(req, res, pathname);
  }
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log('🚀 RPA财务机器人实训平台已启动！');
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log('');
  console.log('🎯 快速访问链接:');
  console.log(`   学生登录: http://localhost:${PORT}/frontend/scenarios/login.html`);
  console.log(`   教师登录: http://localhost:${PORT}/frontend/scenarios/teacher-login.html`);
  console.log(`   学生Dashboard: http://localhost:${PORT}/frontend/scenarios/student-dashboard.html`);
  console.log('');
  console.log('🔑 测试账号:');
  console.log('   学生: test / 123456');
  console.log('   教师: test / 123456');
  console.log('');
  console.log('💡 提示: 按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭RPA实训平台服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});