# 🔧 项目重构指南

## 📁 目录重构步骤

### 第一步：创建模块化目录结构

在命令行中执行以下命令：

```bash
# 进入项目根目录
cd "D:\G盘（软件）\cursor开发文件\claude-RPA实训平台3.0"

# 创建模块化目录
mkdir frontend\scenarios\module1
mkdir frontend\scenarios\module2
mkdir frontend\scenarios\module3
mkdir frontend\components
mkdir frontend\assets
mkdir frontend\utils
mkdir tests\unit
mkdir tests\integration
mkdir tests\e2e
mkdir docs
mkdir scripts
mkdir config
```

### 第二步：移动现有文件到对应模块

```bash
# 移动第一模块文件（基础操作）
move frontend\scenarios\bank-statement-download.html frontend\scenarios\module1\
move frontend\scenarios\invoice-generation.html frontend\scenarios\module1\
move frontend\scenarios\asset-verification.html frontend\scenarios\module1\
move frontend\scenarios\tax-reminder.html frontend\scenarios\module1\

# 移动第二模块文件（流程整合）
move frontend\scenarios\receivables-reconciliation.html frontend\scenarios\module2\

# 保留通用文件
# login.html, student-dashboard.html, teacher-dashboard.html, teacher-login.html 保持原位置
```

### 第三步：初始化Git仓库

```bash
# 初始化Git
git init

# 设置用户信息
git config user.name "chanwarmsun"
git config user.email "<EMAIL>"

# 设置默认分支名
git config init.defaultBranch main

# 添加远程仓库（可选）
# git remote add origin https://github.com/username/rpa-training-platform.git
```

### 第四步：安装开发依赖

```bash
# 安装代码规范工具
npm install --save-dev eslint eslint-config-standard eslint-plugin-import eslint-plugin-n eslint-plugin-promise

# 安装代码格式化工具
npm install --save-dev prettier

# 安装Git钩子工具
npm install --save-dev husky lint-staged

# 安装测试工具
npm install --save-dev supertest

# 初始化husky
npx husky install

# 添加Git钩子
npx husky add .husky/pre-commit "npx lint-staged"
npx husky add .husky/pre-push "npm run test"
```

### 第五步：首次提交

```bash
# 添加所有文件到暂存区
git add .

# 创建初始提交
git commit -m "feat: 初始化项目，添加企业级开发规范

- 创建模块化目录结构
- 添加ESLint和Prettier配置
- 配置Git钩子和代码规范
- 完成第一模块（基础操作）4个场景
- 开发第二模块场景5（应收账款对账）

Closes #1"
```

## 📋 重构后的目录结构

```
rpa-training-platform/
├── api/
│   ├── controllers/
│   ├── middleware/
│   ├── routes/
│   └── utils/
├── cloudbase/
│   └── database/
├── frontend/
│   ├── scenarios/
│   │   ├── module1/          # 第一模块：基础操作
│   │   │   ├── bank-statement-download.html
│   │   │   ├── invoice-generation.html
│   │   │   ├── asset-verification.html
│   │   │   └── tax-reminder.html
│   │   ├── module2/          # 第二模块：流程整合
│   │   │   ├── receivables-reconciliation.html
│   │   │   ├── vat-invoice-certification.html
│   │   │   ├── payroll-generation.html
│   │   │   └── financial-report-consolidation.html
│   │   ├── module3/          # 第三模块：智能应用
│   │   │   ├── invoice-ocr-verification.html
│   │   │   ├── expense-reimbursement-review.html
│   │   │   ├── contract-stamp-tax-calculation.html
│   │   │   └── bank-reconciliation.html
│   │   ├── login.html        # 学生登录页
│   │   ├── student-dashboard.html
│   │   ├── teacher-dashboard.html
│   │   └── teacher-login.html
│   ├── components/           # 共享组件
│   ├── assets/              # 静态资源
│   └── utils/               # 前端工具函数
├── tests/
│   ├── unit/                # 单元测试
│   ├── integration/         # 集成测试
│   └── e2e/                 # 端到端测试
├── docs/                    # 项目文档
├── scripts/                 # 构建脚本
├── config/                  # 配置文件
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
├── .gitignore              # Git忽略文件
├── DEVELOPMENT.md          # 开发规范文档
└── package.json            # 项目配置
```

## 🔥 重构优势

1. **模块化结构** - 按PRD设计的3个模块组织代码
2. **企业级规范** - ESLint + Prettier + Git钩子
3. **测试框架** - 完整的测试目录结构
4. **版本控制** - Git工作流和提交规范
5. **文档完善** - 开发规范和重构指南

## ⚡ 下一步计划

1. **完善第二模块** - 继续开发场景6-8
2. **添加单元测试** - 为关键功能编写测试
3. **集成测试** - API接口测试
4. **代码质量** - 运行lint和format工具
5. **持续集成** - 配置CI/CD流程

---

**立即执行这些步骤，让你的项目达到企业级标准！**