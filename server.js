/**
 * RPA教学平台服务器启动文件
 * 基于腾讯云云开发的BaaS架构
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// 数据库配置 - 优先使用腾讯云云开发，fallback到Mock
let dbConfig;
try {
  // 尝试使用腾讯云云开发
  if (process.env.TCB_ENV_ID && process.env.TCB_SECRET_ID && process.env.TCB_SECRET_KEY) {
    dbConfig = require('./cloudbase/database/db');
    console.log('✅ 使用腾讯云云开发数据库');
  } else {
    throw new Error('腾讯云配置不完整，使用Mock数据库');
  }
} catch (error) {
  // 使用Mock数据库
  dbConfig = require('./cloudbase/database/db-mock');
  console.log('🎭 使用Mock数据库（开发模式）');
}

const { initDatabase } = dbConfig;

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:"],
    },
  },
}));

// CORS配置
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:8080',
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 提供靶场页面
app.use('/scenarios', express.static(path.join(__dirname, 'frontend/scenarios')));
app.use('/assets', express.static(path.join(__dirname, 'frontend/assets')));

// API路由
app.use('/api/auth', require('./api/routes/auth'));
app.use('/api/scenarios', require('./api/routes/scenarios'));
app.use('/api/students', require('./api/routes/students'));
app.use('/api/teachers', require('./api/routes/teachers'));
app.use('/api/validate', require('./api/routes/validation'));
app.use('/api/ocr', require('./api/routes/ocr'));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.1.0',
    environment: process.env.NODE_ENV
  });
});

// 根路径重定向到学生登录页
app.get('/', (req, res) => {
  res.redirect('/scenarios/login.html');
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  
  res.status(err.status || 500).json({
    message: err.message || 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.stack : {}
  });
});

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库
    console.log('正在初始化云数据库...');
    await initDatabase();
    
    // 启动服务器
    app.listen(PORT, () => {
      console.log(`🚀 RPA教学平台服务器已启动`);
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📚 PRD版本: v1.1`);
      console.log(`🎯 当前阶段: 阶段一 - 环境搭建与基础开发`);
      console.log(`🔥 优先任务: 场景1 - 企业网银流水查询下载`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在优雅关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在优雅关闭服务器...');
  process.exit(0);
});

startServer();